"""
Clustering Engine Integration Module

This module integrates the data manager, Rust clustering core, and state manager
to provide a complete clustering pipeline for real-time FX market analysis.
"""

import logging
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import time

from clustering.data_manager import ClusteringDataManager
from clustering.state_manager import StateManager, ClusteringEvent
from config import CURRENCY_PAIRS, MARKET_TIMEZONE

# Configure logging
logger = logging.getLogger(__name__)


class ClusteringEngine:
    """
    Main clustering engine that orchestrates data fetching, clustering analysis,
    and state management for real-time FX market regime detection.
    """
    
    def __init__(self, 
                 symbols: List[str] = None,
                 event_threshold: float = 0.7,
                 min_data_quality: float = 0.8,
                 persistence_dir: str = "data/clustering"):
        """
        Initialize ClusteringEngine
        
        Args:
            symbols: List of currency pairs to analyze (default: all 28 pairs)
            event_threshold: ARI threshold for event detection
            min_data_quality: Minimum data quality score to proceed with clustering
            persistence_dir: Directory for state persistence
        """
        self.symbols = symbols or CURRENCY_PAIRS
        self.min_data_quality = min_data_quality
        
        # Initialize components
        self.data_manager = ClusteringDataManager()
        self.state_manager = StateManager(
            event_threshold=event_threshold,
            persistence_dir=persistence_dir
        )
        
        # Clustering parameters
        self.min_correlation_threshold = 0.1
        self.max_clusters = min(len(self.symbols) // 2, 10)
        
        # Performance tracking
        self.last_update_time: Optional[datetime] = None
        self.update_count = 0
        self.error_count = 0

        # Clustering results storage for visualization
        self.last_linkage_matrix = None
        self.last_distance_matrix = None
        self.last_clustering_result = None

        logger.info(f"ClusteringEngine initialized for {len(self.symbols)} symbols")
    
    def connect(self) -> bool:
        """
        Connect to data sources
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            if self.data_manager.connect():
                logger.info("ClusteringEngine: Connected to data sources")
                return True
            else:
                logger.error("ClusteringEngine: Failed to connect to data sources")
                return False
        except Exception as e:
            logger.error(f"ClusteringEngine connection error: {str(e)}", exc_info=True)
            return False
    
    def disconnect(self):
        """Disconnect from data sources"""
        try:
            self.data_manager.disconnect()
            logger.info("ClusteringEngine: Disconnected from data sources")
        except Exception as e:
            logger.error(f"ClusteringEngine disconnect error: {str(e)}", exc_info=True)
    
    def run_clustering_analysis(self, 
                               hours_back: int = 24,
                               use_weekend_fallback: bool = True) -> Optional[ClusteringEvent]:
        """
        Run complete clustering analysis pipeline
        
        Args:
            hours_back: Hours of historical data to analyze
            use_weekend_fallback: Whether to use Friday data during weekends
            
        Returns:
            ClusteringEvent if regime change detected, None otherwise
        """
        start_time = time.time()
        
        try:
            logger.info(f"Starting clustering analysis for {len(self.symbols)} symbols")
            
            # Step 1: Fetch market data
            market_data = self.data_manager.fetch_clustering_data(
                pairs=self.symbols,
                hours_back=hours_back,
                use_weekend_fallback=use_weekend_fallback
            )
            
            if not market_data:
                logger.warning("No market data available for clustering")
                self.error_count += 1
                return None
            
            # Step 2: Prepare data for clustering
            price_matrix = self.data_manager.prepare_data_for_rust(market_data)
            
            if not price_matrix or len(price_matrix) < 10:
                logger.warning("Insufficient data for clustering analysis")
                self.error_count += 1
                return None
            
            # Step 3: Calculate data quality score
            data_quality = self._calculate_data_quality(market_data, price_matrix)

            if data_quality < self.min_data_quality:
                logger.warning(f"Data quality too low ({data_quality:.3f} < {self.min_data_quality})")
                self.error_count += 1
                return None
            
            # Step 4: Perform clustering analysis
            clustering_results = self._perform_clustering(price_matrix)
            
            if not clustering_results:
                logger.warning("Clustering analysis failed")
                self.error_count += 1
                return None
            
            correlation_matrix, cluster_assignments, volatility_profiles = clustering_results
            
            # Step 5: Update state and detect events
            event = self.state_manager.update_state(
                correlation_matrix=correlation_matrix,
                cluster_assignments=cluster_assignments,
                symbols=self.symbols,
                volatility_profiles=volatility_profiles,
                data_quality_score=data_quality
            )
            
            # Update performance tracking
            processing_time = time.time() - start_time
            self.last_update_time = datetime.now(MARKET_TIMEZONE)
            self.update_count += 1
            
            logger.info(f"Clustering analysis completed in {processing_time:.3f}s")
            logger.info(f"Results: {len(set(cluster_assignments))} clusters, "
                       f"quality={data_quality:.3f}, "
                       f"event={'detected' if event else 'none'}")
            
            return event
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Clustering analysis error: {str(e)}", exc_info=True)
            return None
    
    def _calculate_data_quality(self, 
                               market_data: Dict[str, Any], 
                               price_matrix: List[List[float]]) -> float:
        """
        Calculate data quality score based on completeness and consistency
        
        Args:
            market_data: Raw market data
            price_matrix: Processed price matrix
            
        Returns:
            Data quality score (0-1)
        """
        try:
            # Check data completeness
            expected_pairs = len(self.symbols)
            actual_pairs = len(market_data)
            completeness_score = actual_pairs / expected_pairs
            
            # Check data consistency (no NaN values, reasonable price ranges)
            price_array = np.array(price_matrix)
            
            # Check for NaN or infinite values
            valid_data_ratio = np.sum(np.isfinite(price_array)) / price_array.size
            
            # Check price range reasonableness (basic sanity check)
            price_ranges = np.ptp(price_array, axis=0)  # Peak-to-peak range
            reasonable_ranges = np.sum((price_ranges > 0) & (price_ranges < 10)) / len(price_ranges)
            
            # Combine scores
            quality_score = (completeness_score * 0.4 + 
                           valid_data_ratio * 0.4 + 
                           reasonable_ranges * 0.2)
            
            return min(max(quality_score, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating data quality: {str(e)}")
            return 0.0
    
    def _perform_clustering(self, 
                           price_matrix: List[List[float]]) -> Optional[Tuple[np.ndarray, List[int], Dict[str, float]]]:
        """
        Perform clustering analysis using correlation-based approach
        
        Args:
            price_matrix: Price data matrix
            
        Returns:
            Tuple of (correlation_matrix, cluster_assignments, volatility_profiles)
        """
        try:
            # Convert to numpy array
            price_array = np.array(price_matrix)
            
            if price_array.shape[0] < 2 or price_array.shape[1] != len(self.symbols):
                logger.warning(f"Invalid price matrix shape: {price_array.shape}")
                return None
            
            # Calculate correlation matrix
            correlation_matrix = np.corrcoef(price_array.T)
            
            # Handle NaN values in correlation matrix
            correlation_matrix = np.nan_to_num(correlation_matrix, nan=0.0)
            
            # Perform hierarchical clustering using Rust core if available
            cluster_assignments, linkage_matrix = self._perform_hierarchical_clustering(correlation_matrix)
            
            # Calculate volatility profiles
            volatility_profiles = {}
            for i, symbol in enumerate(self.symbols):
                if i < price_array.shape[1]:
                    # Calculate rolling volatility (standard deviation of returns)
                    prices = price_array[:, i]
                    returns = np.diff(prices) / prices[:-1]
                    volatility = np.std(returns) if len(returns) > 1 else 0.01
                    volatility_profiles[symbol] = float(volatility)
                else:
                    volatility_profiles[symbol] = 0.01

            return correlation_matrix, cluster_assignments, volatility_profiles
            
        except Exception as e:
            logger.error(f"Error performing clustering: {str(e)}", exc_info=True)
            return None

    def _perform_hierarchical_clustering(self, correlation_matrix: np.ndarray) -> Tuple[List[int], Optional[List[List[float]]]]:
        """
        Perform hierarchical clustering using Rust core or fallback to simple clustering

        Args:
            correlation_matrix: Correlation matrix

        Returns:
            Tuple of (cluster_assignments, linkage_matrix)
        """
        try:
            # Try to use Rust clustering core
            try:
                import cluster_core

                # Convert correlation matrix to distance matrix
                correlation_list = correlation_matrix.tolist()
                distance_matrix = cluster_core.correlation_to_distance_matrix(correlation_list)

                # Perform hierarchical clustering
                linkage_matrix, cluster_assignments = cluster_core.perform_hierarchical_clustering(
                    distance_matrix, "complete"
                )

                # Store results for visualization - but don't use Rust linkage matrix for dendrogram
                # Let scipy create its own linkage matrix for proper dendrogram visualization
                self.last_linkage_matrix = None  # Force scipy fallback for dendrogram
                self.last_distance_matrix = distance_matrix

                return cluster_assignments, None  # Return None for linkage to force scipy fallback

            except ImportError:
                pass  # Rust cluster_core not available, using fallback clustering
            except Exception as rust_error:
                logger.warning(f"Rust clustering failed: {rust_error}, using fallback")

            # Fallback to simple correlation clustering
            cluster_assignments = self._simple_correlation_clustering(correlation_matrix)
            return cluster_assignments, None

        except Exception as e:
            logger.error(f"Error in hierarchical clustering: {str(e)}")
            # Return single cluster as ultimate fallback
            return list(range(len(self.symbols))), None
    
    def _simple_correlation_clustering(self, correlation_matrix: np.ndarray) -> List[int]:
        """
        Simple correlation-based clustering (placeholder for Rust implementation)
        
        Args:
            correlation_matrix: Correlation matrix
            
        Returns:
            List of cluster assignments
        """
        try:
            n_symbols = correlation_matrix.shape[0]
            cluster_assignments = list(range(n_symbols))  # Start with each symbol in its own cluster
            
            # Merge highly correlated pairs
            high_corr_threshold = 0.7
            
            for i in range(n_symbols):
                for j in range(i + 1, n_symbols):
                    if correlation_matrix[i, j] > high_corr_threshold:
                        # Merge clusters
                        cluster_j = cluster_assignments[j]
                        cluster_i = cluster_assignments[i]
                        
                        # Replace all occurrences of cluster_j with cluster_i
                        for k in range(n_symbols):
                            if cluster_assignments[k] == cluster_j:
                                cluster_assignments[k] = cluster_i
            
            # Renumber clusters to be consecutive
            unique_clusters = list(set(cluster_assignments))
            cluster_map = {old: new for new, old in enumerate(unique_clusters)}
            cluster_assignments = [cluster_map[c] for c in cluster_assignments]
            
            return cluster_assignments
            
        except Exception as e:
            logger.error(f"Error in simple clustering: {str(e)}")
            return list(range(len(self.symbols)))  # Fallback: each symbol in its own cluster
    
    def get_current_status(self) -> Dict[str, Any]:
        """
        Get current engine status and statistics
        
        Returns:
            Dictionary containing current status information
        """
        try:
            current_state = self.state_manager.get_current_state()
            performance_metrics = self.state_manager.get_performance_metrics()
            recent_events = self.state_manager.get_recent_events(hours_back=24)
            
            status = {
                'engine_status': 'active' if current_state else 'inactive',
                'symbols_count': len(self.symbols),
                'last_update': self.last_update_time.isoformat() if self.last_update_time else None,
                'update_count': self.update_count,
                'error_count': self.error_count,
                'error_rate': self.error_count / max(self.update_count, 1),
                'current_clusters': current_state.cluster_count if current_state else 0,
                'regime_stability': current_state.regime_stability if current_state else 0.0,
                'data_quality': current_state.data_quality_score if current_state else 0.0,
                'recent_events_24h': len(recent_events),
                'performance_metrics': {
                    'total_updates': performance_metrics.total_updates,
                    'events_detected': performance_metrics.events_detected,
                    'average_processing_time': performance_metrics.average_processing_time,
                    'uptime_hours': performance_metrics.uptime_hours
                }
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting engine status: {str(e)}", exc_info=True)
            return {'engine_status': 'error', 'error': str(e)}
    
    def get_recent_events(self, hours_back: int = 24) -> List[ClusteringEvent]:
        """Get recent clustering events"""
        return self.state_manager.get_recent_events(hours_back)
    
    def export_state_summary(self) -> Dict[str, Any]:
        """Export comprehensive state summary"""
        engine_status = self.get_current_status()
        state_summary = self.state_manager.export_state_summary()
        
        return {
            'engine': engine_status,
            'state_manager': state_summary,
            'export_timestamp': datetime.now(MARKET_TIMEZONE).isoformat()
        }
