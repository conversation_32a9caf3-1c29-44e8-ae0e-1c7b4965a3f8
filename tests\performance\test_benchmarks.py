"""
Performance benchmarks for Dynamic FX Clustering Application
"""

import pytest
import time
import psutil
import os
import numpy as np
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from memory_profiler import profile
import cProfile
import pstats
from io import StringIO

from clustering.clustering_engine import ClusteringEngine
from clustering.state_manager import StateManager
from clustering.data_manager import ClusteringDataManager


@pytest.mark.performance
class TestPerformanceBenchmarks:
    """Performance benchmark test cases"""
    
    def test_clustering_speed_benchmark(self, test_symbols, temp_data_dir, performance_thresholds):
        """Benchmark clustering algorithm speed"""
        engine = ClusteringEngine(
            symbols=test_symbols,
            persistence_dir=temp_data_dir
        )
        
        # Generate test data
        n_periods = 1000
        n_symbols = len(test_symbols)
        test_data = np.random.rand(n_periods, n_symbols)
        
        # Mock data fetch
        with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
            mock_fetch.return_value = test_data
            
            # Mock Rust clustering with realistic timing
            with patch('clustering.clustering_engine.cluster_core') as mock_cluster:
                def mock_clustering(*args, **kwargs):
                    # Simulate clustering computation time
                    time.sleep(0.1)  # Realistic clustering time
                    return {
                        'cluster_assignments': list(np.random.randint(0, 5, n_symbols)),
                        'silhouette_score': 0.75,
                        'inertia': 0.25,
                        'n_clusters': 5
                    }
                
                mock_cluster.cluster_currencies.side_effect = mock_clustering
                
                # Benchmark clustering
                start_time = time.time()
                result = engine.run_clustering_analysis()
                end_time = time.time()
                
                clustering_time = end_time - start_time
                
                # Verify performance
                assert result is not None
                assert clustering_time < performance_thresholds['clustering_time_max']
                
                print(f"Clustering time: {clustering_time:.3f}s (threshold: {performance_thresholds['clustering_time_max']}s)")
    
    def test_data_fetch_speed_benchmark(self, test_symbols, temp_data_dir, performance_thresholds):
        """Benchmark data fetching speed"""
        data_manager = ClusteringDataManager(symbols=test_symbols)
        
        # Mock MT5 data fetch with realistic timing
        def mock_fetch_data(*args, **kwargs):
            # Simulate network/database fetch time
            time.sleep(0.5)
            n_periods = 1000
            return np.random.rand(n_periods, len(test_symbols))
        
        with patch.object(data_manager, 'fetch_data', side_effect=mock_fetch_data):
            start_time = time.time()
            data = data_manager.fetch_data()
            end_time = time.time()
            
            fetch_time = end_time - start_time
            
            # Verify performance
            assert data is not None
            assert fetch_time < performance_thresholds['data_fetch_time_max']
            
            print(f"Data fetch time: {fetch_time:.3f}s (threshold: {performance_thresholds['data_fetch_time_max']}s)")
    
    def test_memory_usage_benchmark(self, test_symbols, temp_data_dir, performance_thresholds):
        """Benchmark memory usage during clustering"""
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        engine = ClusteringEngine(
            symbols=test_symbols,
            persistence_dir=temp_data_dir
        )
        
        # Generate large test dataset
        n_periods = 5000
        n_symbols = len(test_symbols)
        large_dataset = np.random.rand(n_periods, n_symbols)
        
        with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
            mock_fetch.return_value = large_dataset
            
            with patch('clustering.clustering_engine.cluster_core') as mock_cluster:
                mock_cluster.cluster_currencies.return_value = {
                    'cluster_assignments': list(np.random.randint(0, 5, n_symbols)),
                    'silhouette_score': 0.75,
                    'inertia': 0.25,
                    'n_clusters': 5
                }
                
                # Run clustering and measure memory
                result = engine.run_clustering_analysis()
                
                peak_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_usage = peak_memory - initial_memory
                
                # Verify performance
                assert result is not None
                assert memory_usage < performance_thresholds['memory_usage_max']
                
                print(f"Memory usage: {memory_usage:.1f}MB (threshold: {performance_thresholds['memory_usage_max']}MB)")
    
    def test_cpu_usage_benchmark(self, test_symbols, temp_data_dir, performance_thresholds):
        """Benchmark CPU usage during clustering"""
        engine = ClusteringEngine(
            symbols=test_symbols,
            persistence_dir=temp_data_dir
        )
        
        # Generate test data
        n_periods = 2000
        n_symbols = len(test_symbols)
        test_data = np.random.rand(n_periods, n_symbols)
        
        cpu_percentages = []
        
        def monitor_cpu():
            """Monitor CPU usage in background thread"""
            for _ in range(20):  # Monitor for 2 seconds
                cpu_percentages.append(psutil.cpu_percent(interval=0.1))
        
        with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
            mock_fetch.return_value = test_data
            
            with patch('clustering.clustering_engine.cluster_core') as mock_cluster:
                def cpu_intensive_clustering(*args, **kwargs):
                    # Simulate CPU-intensive clustering
                    for _ in range(1000000):
                        _ = sum(range(100))
                    return {
                        'cluster_assignments': list(np.random.randint(0, 5, n_symbols)),
                        'silhouette_score': 0.75,
                        'inertia': 0.25,
                        'n_clusters': 5
                    }
                
                mock_cluster.cluster_currencies.side_effect = cpu_intensive_clustering
                
                # Start CPU monitoring
                monitor_thread = threading.Thread(target=monitor_cpu)
                monitor_thread.start()
                
                # Run clustering
                result = engine.run_clustering_analysis()
                
                # Wait for monitoring to complete
                monitor_thread.join()
                
                # Calculate average CPU usage
                avg_cpu = sum(cpu_percentages) / len(cpu_percentages) if cpu_percentages else 0
                
                # Verify performance
                assert result is not None
                assert avg_cpu < performance_thresholds['cpu_usage_max']
                
                print(f"Average CPU usage: {avg_cpu:.1f}% (threshold: {performance_thresholds['cpu_usage_max']}%)")
    
    def test_dashboard_render_speed_benchmark(self, test_symbols, performance_thresholds):
        """Benchmark dashboard rendering speed"""
        # Mock clustering data
        clustering_data = {
            'cluster_assignments': list(np.random.randint(0, 5, len(test_symbols))),
            'silhouette_score': 0.75,
            'n_clusters': 5,
            'symbols': test_symbols,
            'correlation_matrix': np.random.rand(len(test_symbols), len(test_symbols)).tolist(),
            'timestamp': datetime.now().isoformat()
        }
        
        # Import dashboard functions
        from run_clustering_app import (
            update_dendrogram,
            update_statistics_panel,
            update_event_log
        )
        
        # Benchmark dendrogram rendering
        start_time = time.time()
        dendrogram_fig = update_dendrogram(clustering_data)
        dendrogram_time = time.time() - start_time
        
        # Benchmark statistics panel rendering
        start_time = time.time()
        stats_content = update_statistics_panel(clustering_data, None)
        stats_time = time.time() - start_time
        
        # Benchmark event log rendering
        start_time = time.time()
        event_content = update_event_log(clustering_data)
        event_time = time.time() - start_time
        
        total_render_time = dendrogram_time + stats_time + event_time
        
        # Verify performance
        assert dendrogram_fig is not None
        assert stats_content is not None
        assert event_content is not None
        assert total_render_time < performance_thresholds['dashboard_render_time_max']
        
        print(f"Dashboard render time: {total_render_time:.3f}s (threshold: {performance_thresholds['dashboard_render_time_max']}s)")
    
    def test_concurrent_performance_benchmark(self, test_symbols, temp_data_dir):
        """Benchmark performance under concurrent load"""
        import queue
        import threading
        
        results_queue = queue.Queue()
        
        def clustering_worker(worker_id):
            """Worker function for concurrent clustering"""
            try:
                engine = ClusteringEngine(
                    symbols=test_symbols[:5],  # Smaller subset for faster testing
                    persistence_dir=f"{temp_data_dir}/worker_{worker_id}"
                )
                
                with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
                    mock_fetch.return_value = np.random.rand(500, 5)
                    
                    with patch('clustering.clustering_engine.cluster_core') as mock_cluster:
                        mock_cluster.cluster_currencies.return_value = {
                            'cluster_assignments': [0, 1, 2, 0, 1],
                            'silhouette_score': 0.75,
                            'inertia': 0.25,
                            'n_clusters': 3
                        }
                        
                        start_time = time.time()
                        result = engine.run_clustering_analysis()
                        end_time = time.time()
                        
                        results_queue.put({
                            'worker_id': worker_id,
                            'success': result is not None,
                            'time': end_time - start_time
                        })
            except Exception as e:
                results_queue.put({
                    'worker_id': worker_id,
                    'success': False,
                    'error': str(e),
                    'time': 0
                })
        
        # Start multiple workers
        num_workers = 5
        threads = []
        
        start_time = time.time()
        
        for i in range(num_workers):
            thread = threading.Thread(target=clustering_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all workers to complete
        for thread in threads:
            thread.join(timeout=10.0)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Collect results
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())
        
        # Verify performance
        assert len(results) == num_workers
        successful_results = [r for r in results if r['success']]
        assert len(successful_results) == num_workers
        
        avg_time = sum(r['time'] for r in successful_results) / len(successful_results)
        
        print(f"Concurrent performance: {num_workers} workers, {total_time:.3f}s total, {avg_time:.3f}s average")
    
    def test_data_throughput_benchmark(self, test_symbols, temp_data_dir):
        """Benchmark data processing throughput"""
        state_manager = StateManager(persistence_dir=temp_data_dir)
        
        # Generate test data
        num_updates = 100
        data_points_per_update = 1000
        
        start_time = time.time()
        
        for i in range(num_updates):
            test_state = {
                'cluster_assignments': list(np.random.randint(0, 5, len(test_symbols))),
                'silhouette_score': np.random.uniform(0.3, 0.9),
                'n_clusters': np.random.randint(3, 7),
                'timestamp': datetime.now(),
                'data_points': data_points_per_update
            }
            
            state_manager.update_state(test_state)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        total_data_points = num_updates * data_points_per_update
        throughput = total_data_points / total_time
        
        print(f"Data throughput: {throughput:.0f} points/second ({total_data_points} points in {total_time:.3f}s)")
        
        # Verify reasonable throughput
        assert throughput > 10000  # At least 10k points per second
    
    @profile
    def test_memory_profile_clustering(self, test_symbols, temp_data_dir):
        """Profile memory usage during clustering (requires memory_profiler)"""
        engine = ClusteringEngine(
            symbols=test_symbols,
            persistence_dir=temp_data_dir
        )
        
        # Generate large dataset
        large_data = np.random.rand(2000, len(test_symbols))
        
        with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
            mock_fetch.return_value = large_data
            
            with patch('clustering.clustering_engine.cluster_core') as mock_cluster:
                mock_cluster.cluster_currencies.return_value = {
                    'cluster_assignments': list(np.random.randint(0, 5, len(test_symbols))),
                    'silhouette_score': 0.75,
                    'inertia': 0.25,
                    'n_clusters': 5
                }
                
                # This will be profiled by @profile decorator
                result = engine.run_clustering_analysis()
                assert result is not None
    
    def test_cpu_profile_clustering(self, test_symbols, temp_data_dir):
        """Profile CPU usage during clustering"""
        engine = ClusteringEngine(
            symbols=test_symbols,
            persistence_dir=temp_data_dir
        )
        
        # Setup profiler
        profiler = cProfile.Profile()
        
        with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
            mock_fetch.return_value = np.random.rand(1000, len(test_symbols))
            
            with patch('clustering.clustering_engine.cluster_core') as mock_cluster:
                mock_cluster.cluster_currencies.return_value = {
                    'cluster_assignments': list(np.random.randint(0, 5, len(test_symbols))),
                    'silhouette_score': 0.75,
                    'inertia': 0.25,
                    'n_clusters': 5
                }
                
                # Profile clustering
                profiler.enable()
                result = engine.run_clustering_analysis()
                profiler.disable()
                
                # Generate profile report
                s = StringIO()
                ps = pstats.Stats(profiler, stream=s).sort_stats('cumulative')
                ps.print_stats(10)  # Top 10 functions
                
                profile_output = s.getvalue()
                print("CPU Profile (Top 10 functions):")
                print(profile_output)
                
                assert result is not None
