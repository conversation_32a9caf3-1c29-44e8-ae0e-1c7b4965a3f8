{"rules": [{"name": "regime_changes", "event_types": ["regime_change"], "severity_threshold": "medium", "cooldown_minutes": 5, "enabled": true, "description": "Alert on market regime changes"}, {"name": "high_severity_events", "event_types": ["market_fragmentation", "cluster_count_change", "quality_degradation"], "severity_threshold": "high", "cooldown_minutes": 10, "enabled": true, "description": "Alert on high-severity clustering events"}, {"name": "critical_events", "event_types": ["regime_change", "market_fragmentation"], "severity_threshold": "critical", "cooldown_minutes": 1, "enabled": true, "description": "Immediate alerts for critical events"}], "channels": [{"name": "file_log", "channel_type": "file", "config": {"file_path": "alerts.log"}, "enabled": true}, {"name": "console", "channel_type": "callback", "config": {"callback": "console_print"}, "enabled": true}]}