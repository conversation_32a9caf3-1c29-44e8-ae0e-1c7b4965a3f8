# Installation Guide - Dynamic FX Clustering Application

## System Requirements

### Hardware Requirements
- **CPU**: Intel i5 or AMD Ryzen 5 (minimum), Intel i7 or AMD Ryzen 7 (recommended)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 2GB free space for application and data
- **Network**: Stable internet connection for real-time data

### Software Requirements
- **Operating System**: Windows 10/11 (64-bit) - Required for MetaTrader 5
- **Python**: Version 3.9, 3.10, 3.11, or 3.12
- **MetaTrader 5**: Latest version from MetaQuotes
- **Rust**: Latest stable version (for building clustering engine)

## Pre-Installation Setup

### Step 1: Install Python
1. Download Python from [python.org](https://python.org)
2. **Important**: Check "Add Python to PATH" during installation
3. Verify installation:
   ```powershell
   python --version
   pip --version
   ```

### Step 2: Install Rust
1. Download Rust from [rustup.rs](https://rustup.rs)
2. Run the installer and follow prompts
3. Restart your terminal/PowerShell
4. Verify installation:
   ```powershell
   rustc --version
   cargo --version
   ```

### Step 3: Install MetaTrader 5
1. Download MT5 from [MetaQuotes](https://www.metatrader5.com)
2. Install and create/login to your trading account
3. Configure Expert Advisors:
   - Tools → Options → Expert Advisors
   - ✅ Enable "Allow automated trading"
   - ✅ Enable "Allow DLL imports"
   - ✅ Enable "Allow imports of external experts"

## Installation Process

### Method 1: Quick Installation (Recommended)

1. **Download the Application**
   ```powershell
   # Clone or download the project to your desired location
   cd C:\Users\<USER>\Desktop\Clustering
   ```

2. **Install Python Dependencies**
   ```powershell
   # Install core dependencies
   pip install dash==2.14.1 plotly==5.17.0 pandas==2.1.3
   pip install numpy==1.24.3 scipy==1.11.4 scikit-learn==1.3.2
   pip install MetaTrader5==5.0.45 pytz==2023.3
   pip install dash-bootstrap-components==1.5.0
   
   # Install development and testing dependencies
   pip install psutil==5.9.6 memory-profiler==0.61.0
   pip install pytest==7.4.3 pytest-cov==4.1.0
   ```

3. **Build Rust Clustering Engine**
   ```powershell
   # Navigate to Rust project
   cd cluster_core
   
   # Build release version
   cargo build --release
   
   # Return to main directory
   cd ..
   ```

4. **Verify Installation**
   ```powershell
   # Test the clustering engine
   python test_clustering_engine.py
   
   # Test the dashboard components
   python test_dashboard.py
   ```

### Method 2: Development Installation

For developers who want to modify the code:

1. **Install Additional Development Tools**
   ```powershell
   # Install development dependencies
   pip install black==23.10.1 flake8==6.1.0 mypy==1.7.1
   pip install jupyter==1.0.0 matplotlib==3.8.2
   ```

2. **Install in Development Mode**
   ```powershell
   # Install package in editable mode
   pip install -e .
   ```

3. **Setup Pre-commit Hooks** (Optional)
   ```powershell
   pip install pre-commit==3.5.0
   pre-commit install
   ```

## Configuration

### Step 1: Basic Configuration
Edit `config.py` to match your setup:

```python
# Timezone Configuration
MARKET_TIMEZONE = 'Europe/Bucharest'  # Change to your timezone

# Currency Pairs (modify as needed)
CURRENCY_PAIRS = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD',
    'AUDUSD', 'NZDUSD', 'EURJPY', 'EURGBP', 'EURCHF',
    'GBPJPY', 'GBPCHF', 'GBPCAD', 'GBPAUD', 'GBPNZD',
    'CHFJPY', 'CADJPY', 'AUDJPY', 'NZDJPY', 'EURAUD',
    'EURCAD', 'EURNZD', 'AUDCAD', 'AUDCHF', 'AUDNZD',
    'CADCHF', 'NZDCAD', 'NZDCHF'
]

# Performance Settings
UPDATE_INTERVAL = 30  # seconds between updates
DATA_HISTORY_HOURS = 24  # hours of historical data to maintain
```

### Step 2: MetaTrader 5 Configuration
1. **Account Setup**:
   - Ensure your account has access to all required currency pairs
   - Verify sufficient account balance for data access
   - Check that your broker provides the required symbols

2. **Terminal Settings**:
   - Keep MetaTrader 5 running during application use
   - Ensure stable internet connection
   - Configure automatic login if desired

### Step 3: Directory Structure
Ensure the following directory structure exists:
```
Clustering/
├── clustering/          # Core application modules
├── cluster_core/        # Rust clustering engine
├── data/               # Data storage (created automatically)
├── exports/            # Export files (created automatically)
├── tests/              # Test suite
├── docs/               # Documentation
├── config.py           # Configuration file
└── run_clustering_app.py  # Main application
```

## Verification and Testing

### Step 1: Component Tests
```powershell
# Test individual components
python test_clustering_engine.py
python test_state_manager.py
python test_database.py
python test_dashboard.py
```

### Step 2: Integration Tests
```powershell
# Run comprehensive test suite
pytest tests/ -v
```

### Step 3: Performance Tests
```powershell
# Run performance benchmarks
pytest tests/performance/ -v --tb=short
```

### Step 4: End-to-End Test
```powershell
# Start the application
python run_clustering_app.py

# In another terminal, run E2E tests
pytest tests/e2e/ -v
```

## First Run

### Step 1: Start MetaTrader 5
1. Open MetaTrader 5
2. Log in to your trading account
3. Verify connection to trading server
4. Ensure required currency pairs are visible in Market Watch

### Step 2: Launch Application
```powershell
# Start the clustering application
python run_clustering_app.py
```

Expected output:
```
INFO:__main__:Starting Dynamic FX Clustering Application
INFO:__main__:Initializing clustering engine...
INFO:__main__:Connecting to MetaTrader 5...
INFO:__main__:MT5 connection successful
INFO:__main__:Starting data collection...
INFO:__main__:Dashboard server starting on http://localhost:8050
```

### Step 3: Access Dashboard
1. Open web browser
2. Navigate to `http://localhost:8050`
3. Wait for initial data loading (30-60 seconds)
4. Verify all tabs load correctly

### Step 4: Verify Functionality
- ✅ Connection status shows "MT5 Connected"
- ✅ Clustering data appears in main tab
- ✅ Volatility regimes show current data
- ✅ Advanced analytics display metrics
- ✅ No error messages in browser console

## Troubleshooting Installation

### Common Issues

#### Python Installation Issues
**Problem**: `python` command not recognized
**Solution**: 
- Reinstall Python with "Add to PATH" checked
- Or use `py` command instead of `python`

#### Rust Installation Issues
**Problem**: `cargo` command not found
**Solution**:
- Restart terminal after Rust installation
- Add Rust to PATH manually if needed

#### MetaTrader 5 Connection Issues
**Problem**: MT5 connection fails
**Solution**:
- Verify MT5 is running and logged in
- Check Expert Advisors settings
- Ensure account has data access permissions

#### Dependency Installation Issues
**Problem**: Package installation fails
**Solution**:
```powershell
# Upgrade pip first
python -m pip install --upgrade pip

# Install with verbose output
pip install -v package_name

# Use alternative index if needed
pip install -i https://pypi.org/simple/ package_name
```

#### Build Issues
**Problem**: Rust build fails
**Solution**:
```powershell
# Clean and rebuild
cd cluster_core
cargo clean
cargo build --release

# Check for specific error messages
cargo check
```

### Performance Optimization

#### For Low-End Systems
```python
# In config.py, reduce resource usage
CURRENCY_PAIRS = CURRENCY_PAIRS[:14]  # Monitor fewer pairs
UPDATE_INTERVAL = 60  # Update less frequently
DATA_HISTORY_HOURS = 12  # Keep less history
```

#### For High-End Systems
```python
# In config.py, maximize performance
UPDATE_INTERVAL = 15  # Update more frequently
DATA_HISTORY_HOURS = 48  # Keep more history
# Monitor all 28 currency pairs
```

## Uninstallation

To completely remove the application:

1. **Stop the Application**
   - Close browser tabs
   - Stop the Python application (Ctrl+C)

2. **Remove Python Packages**
   ```powershell
   pip uninstall dash plotly pandas numpy scipy scikit-learn
   pip uninstall MetaTrader5 pytz dash-bootstrap-components
   pip uninstall psutil memory-profiler pytest
   ```

3. **Remove Application Files**
   - Delete the entire application directory
   - Remove any shortcuts or desktop icons

4. **Clean Up Data** (Optional)
   - Remove data files if no longer needed
   - Clear browser cache for localhost:8050

---

**Installation Support**: If you encounter issues not covered in this guide, please check the troubleshooting section in the User Guide or contact technical support with detailed error messages and system information.
