# Dynamic FX Clustering Application - Core Dependencies
# Production requirements for the clustering application

# Core Framework
dash==2.14.1
plotly==5.17.0
dash-bootstrap-components==1.5.0

# Data Processing
pandas==2.1.3
numpy==1.24.3
scipy==1.11.4
scikit-learn==1.3.2

# MetaTrader 5 Integration
MetaTrader5==5.0.45
pytz==2023.3

# System Monitoring
psutil==5.9.6

# Optional: Memory profiling (for performance monitoring)
memory-profiler==0.61.0

# Note: Rust toolchain required for building cluster_core
# Install via: https://rustup.rs/
