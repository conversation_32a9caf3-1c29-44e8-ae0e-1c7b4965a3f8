# Dynamic FX Clustering Application - Development Dependencies
# Additional requirements for development, testing, and debugging

# Include production requirements
-r requirements.txt

# Testing Framework
pytest==7.4.3
pytest-cov==4.1.0
pytest-benchmark==4.0.0
pytest-mock==3.12.0

# Code Quality
black==23.10.1
flake8==6.1.0
mypy==1.7.1
isort==5.12.0
pre-commit==3.5.0

# Development Tools
jupyter==1.0.0
matplotlib==3.8.2
ipython==8.17.2

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Performance Analysis
line-profiler==4.1.1
py-spy==0.3.14

# Additional Testing Utilities
factory-boy==3.3.0
freezegun==1.2.2
responses==0.24.1
