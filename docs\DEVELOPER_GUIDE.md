# Developer Guide - Dynamic FX Clustering Application

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Code Structure](#code-structure)
3. [API Reference](#api-reference)
4. [Extension Guide](#extension-guide)
5. [Development Setup](#development-setup)
6. [Testing Framework](#testing-framework)
7. [Performance Optimization](#performance-optimization)
8. [Contribution Guidelines](#contribution-guidelines)

## Architecture Overview

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Dashboard     │    │   Python        │    │   Rust          │
│   (Dash/Plotly) │◄──►│   Backend       │◄──►│   Clustering    │
│                 │    │                 │    │   Engine        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   State         │    │   PyO3          │
│   (User Interface)   │   Management    │    │   Bindings      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   MetaTrader 5  │
                       │   Data Source   │
                       └─────────────────┘
```

### Component Responsibilities

#### Frontend (Dashboard)
- **Technology**: Dash + Plotly + Bootstrap
- **Responsibilities**: 
  - User interface rendering
  - Real-time chart updates
  - User interaction handling
  - Export functionality

#### Backend (Python)
- **Technology**: Python 3.9+
- **Responsibilities**:
  - Data management and persistence
  - State management
  - Advanced analytics
  - Alert system
  - API coordination

#### Clustering Engine (Rust)
- **Technology**: Rust + PyO3
- **Responsibilities**:
  - High-performance clustering algorithms
  - Correlation matrix calculations
  - Volatility regime detection
  - Mathematical computations

#### Data Source (MetaTrader 5)
- **Technology**: MT5 Python API
- **Responsibilities**:
  - Real-time forex data
  - Historical data retrieval
  - Market connectivity

## Code Structure

### Directory Layout
```
Clustering/
├── clustering/                 # Core Python modules
│   ├── __init__.py
│   ├── clustering_engine.py    # Main clustering orchestrator
│   ├── state_manager.py        # State persistence and management
│   ├── data_manager.py         # MT5 data interface
│   ├── advanced_analytics.py   # Analytics engine
│   ├── alert_system.py         # Real-time alerts
│   ├── database.py             # Data persistence
│   ├── dendrogram_utils.py     # Dendrogram visualization
│   ├── sankey_utils.py         # Sankey diagram utilities
│   └── export_utils.py         # Data export functionality
├── cluster_core/               # Rust clustering engine
│   ├── Cargo.toml              # Rust dependencies
│   ├── src/
│   │   ├── lib.rs              # Main Rust library
│   │   ├── clustering.rs       # Clustering algorithms
│   │   ├── correlation.rs      # Correlation calculations
│   │   └── volatility.rs       # Volatility analysis
├── tests/                      # Comprehensive test suite
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   ├── e2e/                    # End-to-end tests
│   ├── performance/            # Performance benchmarks
│   └── conftest.py             # Test configuration
├── docs/                       # Documentation
├── data/                       # Runtime data storage
├── exports/                    # Export files
├── config.py                   # Configuration settings
└── run_clustering_app.py       # Main application entry point
```

### Key Modules

#### ClusteringEngine (`clustering/clustering_engine.py`)
Main orchestrator that coordinates all components:

```python
class ClusteringEngine:
    def __init__(self, symbols, event_threshold=0.8, min_data_quality=0.7):
        self.symbols = symbols
        self.data_manager = ClusteringDataManager(symbols)
        self.state_manager = StateManager()
        
    def run_clustering_analysis(self) -> Optional[Dict]:
        """Main clustering workflow"""
        # 1. Fetch data from MT5
        # 2. Validate data quality
        # 3. Run Rust clustering
        # 4. Detect market events
        # 5. Update state
        # 6. Return results
```

#### StateManager (`clustering/state_manager.py`)
Manages application state and persistence:

```python
class StateManager:
    def __init__(self, persistence_dir, enable_advanced_analytics=True):
        self.current_state = None
        self.historical_states = []
        self.advanced_analytics = AdvancedAnalytics()
        self.alert_system = AlertSystem()
        
    def update_state(self, new_state: Dict):
        """Update current state and trigger analytics"""
        # 1. Validate new state
        # 2. Update current state
        # 3. Add to historical states
        # 4. Run analytics
        # 5. Check for alerts
        # 6. Persist to disk
```

#### DataManager (`clustering/data_manager.py`)
Handles MetaTrader 5 data operations:

```python
class ClusteringDataManager:
    def __init__(self, symbols: List[str]):
        self.symbols = symbols
        self.mt5_connected = False
        
    def fetch_data(self, timeframe='M5', hours_back=24) -> Optional[np.ndarray]:
        """Fetch real-time data from MT5"""
        # 1. Connect to MT5
        # 2. Fetch OHLC data for all symbols
        # 3. Calculate returns/correlations
        # 4. Validate data quality
        # 5. Return processed data
```

## API Reference

### Core APIs

#### ClusteringEngine API
```python
# Initialize engine
engine = ClusteringEngine(
    symbols=['EURUSD', 'GBPUSD', 'USDJPY'],
    event_threshold=0.8,
    min_data_quality=0.7,
    persistence_dir="data"
)

# Connect to data sources
connected = engine.connect_to_data_sources()

# Run clustering analysis
result = engine.run_clustering_analysis()
# Returns: {
#     'cluster_assignments': [0, 1, 2, ...],
#     'silhouette_score': 0.75,
#     'n_clusters': 3,
#     'event_detected': True,
#     'event_type': 'regime_change'
# }

# Get current state
state = engine.get_current_state()

# Get historical states
history = engine.get_historical_states(hours_back=24)

# Get performance metrics
metrics = engine.get_performance_metrics()
```

#### StateManager API
```python
# Initialize state manager
state_manager = StateManager(
    persistence_dir="data/state",
    enable_advanced_analytics=True,
    enable_alerts=True
)

# Update state
state_manager.update_state(clustering_result)

# Get analytics summary
analytics = state_manager.get_advanced_analytics_summary()
# Returns: {
#     'avg_silhouette_score': 0.7,
#     'avg_cluster_count': 3.2,
#     'total_measurements': 100,
#     'quality_trend': 'improving'
# }

# Get alert history
alerts = state_manager.get_alert_history(hours_back=24)

# Get regime classification
regime = state_manager.get_current_regime_classification()
```

#### AdvancedAnalytics API
```python
# Initialize analytics engine
analytics = AdvancedAnalytics()

# Add clustering result
analytics.add_clustering_result(result)

# Get quality metrics
quality = analytics.get_clustering_quality_metrics()

# Get performance trends
trends = analytics.get_performance_trends()

# Validate clustering
validation = analytics.validate_clustering_result(result)
```

#### AlertSystem API
```python
# Initialize alert system
alerts = AlertSystem()

# Add alert rule
alerts.add_rule(
    name="high_volatility",
    condition=lambda state: state.get('volatility', 0) > 0.05,
    severity="high",
    message="High volatility detected"
)

# Process state for alerts
alerts.process_state(current_state)

# Get alert history
history = alerts.get_alert_history(hours_back=24)
```

### Rust API (via PyO3)

#### Clustering Functions
```python
import cluster_core

# Cluster currencies
result = cluster_core.cluster_currencies(
    correlation_matrix,  # 2D numpy array
    min_clusters=3,
    max_clusters=7,
    method="ward"
)

# Calculate correlation matrix
correlation = cluster_core.calculate_correlation_matrix(
    price_data  # 2D numpy array: [time, symbols]
)

# Detect volatility regimes
regimes = cluster_core.detect_volatility_regimes(
    returns_data,  # 2D numpy array
    n_regimes=5,
    window_size=60
)
```

## Extension Guide

### Adding New Data Sources

1. **Create Data Source Interface**:
```python
# clustering/data_sources/new_source.py
class NewDataSource:
    def __init__(self, config):
        self.config = config
        
    def connect(self) -> bool:
        """Connect to data source"""
        pass
        
    def fetch_data(self, symbols, timeframe, start, end) -> np.ndarray:
        """Fetch OHLC data"""
        pass
        
    def get_symbols(self) -> List[str]:
        """Get available symbols"""
        pass
```

2. **Integrate with DataManager**:
```python
# In clustering/data_manager.py
from .data_sources.new_source import NewDataSource

class ClusteringDataManager:
    def __init__(self, symbols, data_source="mt5"):
        if data_source == "new_source":
            self.data_source = NewDataSource(config)
        # ... existing code
```

### Adding New Clustering Algorithms

1. **Implement in Rust**:
```rust
// cluster_core/src/clustering.rs
pub fn new_clustering_algorithm(
    data: &Array2<f64>,
    params: &ClusteringParams
) -> ClusteringResult {
    // Implementation
}
```

2. **Expose via PyO3**:
```rust
// cluster_core/src/lib.rs
#[pyfunction]
fn new_clustering_algorithm(
    data: PyReadonlyArray2<f64>,
    params: &PyDict
) -> PyResult<PyDict> {
    // Wrapper implementation
}
```

3. **Use in Python**:
```python
# clustering/clustering_engine.py
import cluster_core

result = cluster_core.new_clustering_algorithm(data, params)
```

### Adding New Dashboard Components

1. **Create Component Function**:
```python
# In run_clustering_app.py
def create_new_component():
    return dbc.Card([
        dbc.CardHeader("New Component"),
        dbc.CardBody([
            # Component content
        ])
    ])
```

2. **Add to Layout**:
```python
# Add to main layout
new_tab = dbc.Tab(
    label="New Feature",
    tab_id="new-tab",
    children=[create_new_component()]
)
```

3. **Create Callback**:
```python
@app.callback(
    Output('new-component-output', 'children'),
    Input('clustering-data-store', 'data')
)
def update_new_component(clustering_data):
    # Update logic
    return new_content
```

### Adding New Analytics

1. **Extend AdvancedAnalytics**:
```python
# clustering/advanced_analytics.py
class AdvancedAnalytics:
    def new_analysis_method(self, data):
        """New analysis implementation"""
        # Analysis logic
        return results
```

2. **Add to StateManager**:
```python
# clustering/state_manager.py
def get_new_analytics(self):
    return self.advanced_analytics.new_analysis_method(self.historical_states)
```

3. **Expose in Dashboard**:
```python
# In run_clustering_app.py
@app.callback(...)
def update_new_analytics(clustering_data):
    analytics = clustering_engine.state_manager.get_new_analytics()
    return format_analytics_display(analytics)
```

## Development Setup

### Development Environment
```powershell
# Clone repository
git clone <repository-url>
cd Clustering

# Create virtual environment
python -m venv venv
venv\Scripts\activate

# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Build Rust components
cd cluster_core
cargo build --release
cd ..
```

### Code Style and Standards

#### Python Code Style
- **Formatter**: Black (line length: 88)
- **Linter**: Flake8 + MyPy
- **Import sorting**: isort
- **Docstrings**: Google style

```python
def example_function(param1: str, param2: int) -> Dict[str, Any]:
    """Example function with proper typing and docstring.

    Args:
        param1: Description of parameter 1
        param2: Description of parameter 2

    Returns:
        Dictionary containing results

    Raises:
        ValueError: If parameters are invalid
    """
    pass
```

#### Rust Code Style
- **Formatter**: rustfmt
- **Linter**: clippy
- **Documentation**: rustdoc comments

```rust
/// Example function with proper documentation
///
/// # Arguments
/// * `data` - Input data array
/// * `params` - Configuration parameters
///
/// # Returns
/// Clustering result structure
///
/// # Errors
/// Returns error if data is invalid
pub fn example_function(data: &Array2<f64>, params: &Params) -> Result<ClusteringResult> {
    // Implementation
}
```

## Testing Framework

### Test Structure
```
tests/
├── unit/                   # Unit tests (fast, isolated)
│   ├── test_clustering_engine.py
│   ├── test_state_manager.py
│   └── test_data_manager.py
├── integration/            # Integration tests (component interaction)
│   ├── test_clustering_pipeline.py
│   └── test_dashboard_integration.py
├── e2e/                   # End-to-end tests (full workflow)
│   └── test_dashboard_workflow.py
├── performance/           # Performance benchmarks
│   └── test_benchmarks.py
└── conftest.py           # Shared fixtures and configuration
```

### Running Tests
```powershell
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/ -v
pytest tests/integration/ -v
pytest tests/e2e/ -v
pytest tests/performance/ -v

# Run with coverage
pytest --cov=clustering --cov-report=html

# Run performance benchmarks
pytest tests/performance/ --benchmark-only
```

### Writing Tests
```python
import pytest
from clustering.clustering_engine import ClusteringEngine

@pytest.mark.unit
def test_clustering_engine_initialization(test_symbols, temp_data_dir):
    """Test clustering engine initialization"""
    engine = ClusteringEngine(
        symbols=test_symbols,
        persistence_dir=temp_data_dir
    )

    assert engine.symbols == test_symbols
    assert engine.state_manager is not None
```

## Performance Optimization

### Profiling Tools
```python
# Memory profiling
from memory_profiler import profile

@profile
def memory_intensive_function():
    # Function implementation
    pass

# CPU profiling
import cProfile
import pstats

profiler = cProfile.Profile()
profiler.enable()
# Code to profile
profiler.disable()

stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats(10)
```

### Optimization Guidelines

#### Python Optimization
- Use NumPy for numerical computations
- Minimize data copying
- Use appropriate data structures
- Profile before optimizing

#### Rust Optimization
- Use release builds for production
- Leverage Rust's zero-cost abstractions
- Optimize hot paths with profiling
- Use SIMD when appropriate

#### Dashboard Optimization
- Minimize callback complexity
- Use clientside callbacks when possible
- Implement data caching
- Optimize chart rendering

## Contribution Guidelines

### Development Workflow
1. **Fork and Clone**: Fork the repository and clone locally
2. **Branch**: Create feature branch from main
3. **Develop**: Implement changes with tests
4. **Test**: Run full test suite
5. **Document**: Update documentation
6. **Submit**: Create pull request

### Pull Request Process
1. **Description**: Clear description of changes
2. **Tests**: All tests must pass
3. **Coverage**: Maintain or improve test coverage
4. **Documentation**: Update relevant documentation
5. **Review**: Address review feedback
6. **Merge**: Squash and merge when approved

### Code Review Checklist
- [ ] Code follows style guidelines
- [ ] Tests are comprehensive and pass
- [ ] Documentation is updated
- [ ] Performance impact is considered
- [ ] Security implications are reviewed
- [ ] Backward compatibility is maintained

### Release Process
1. **Version Bump**: Update version numbers
2. **Changelog**: Update CHANGELOG.md
3. **Testing**: Run full test suite
4. **Documentation**: Update documentation
5. **Tag**: Create git tag
6. **Release**: Create GitHub release

---

**Developer Support**: For development questions, check the existing documentation, review test examples, or contact the development team with specific technical questions.
