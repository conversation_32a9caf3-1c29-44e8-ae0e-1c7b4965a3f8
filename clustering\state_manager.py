"""
State Management System for Dynamic FX Clustering Application

This module provides centralized state management for:
- Clustering results and historical data
- Event detection using Adjusted Rand Index
- Real-time state updates and persistence
- Performance metrics and statistics
"""

# Fix OpenBLAS threading issue before importing numpy/sklearn
import os
os.environ['OPENBLAS_NUM_THREADS'] = '8'
os.environ['MKL_NUM_THREADS'] = '8'
os.environ['NUMEXPR_NUM_THREADS'] = '8'
os.environ['OMP_NUM_THREADS'] = '8'

import logging
import threading
import time
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import deque
import numpy as np
from sklearn.metrics import adjusted_rand_score
import json
import pickle
from pathlib import Path

from config import MARKET_TIMEZONE

# Import database manager (optional dependency)
try:
    from .database import DatabaseManager
    DATABASE_AVAILABLE = True
except ImportError as e1:
    try:
        # Try absolute import as fallback
        from clustering.database import DatabaseManager
        DATABASE_AVAILABLE = True
    except ImportError as e2:
        try:
            # Try direct import
            import sys
            import os
            sys.path.append(os.path.dirname(__file__))
            from database import DatabaseManager
            DATABASE_AVAILABLE = True
        except ImportError as e3:
            DATABASE_AVAILABLE = False
            DatabaseManager = None
            pass  # Database import failed

# Import advanced analytics and alert system
try:
    from .advanced_analytics import AdvancedAnalytics, ClusteringMetrics, RegimeClassification
    from .alert_system import AlertSystem
    ADVANCED_ANALYTICS_AVAILABLE = True
except ImportError as e1:
    try:
        # Try absolute import as fallback
        from clustering.advanced_analytics import AdvancedAnalytics, ClusteringMetrics, RegimeClassification
        from clustering.alert_system import AlertSystem
        ADVANCED_ANALYTICS_AVAILABLE = True
    except ImportError as e2:
        ADVANCED_ANALYTICS_AVAILABLE = False
        AdvancedAnalytics = None
        AlertSystem = None
        ClusteringMetrics = None
        RegimeClassification = None
        logger.warning(f"Advanced analytics not available: {e1}, {e2}")

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class ClusteringState:
    """Current clustering state snapshot"""
    timestamp: datetime
    correlation_matrix: np.ndarray
    cluster_assignments: List[int]
    cluster_count: int
    symbols: List[str]
    volatility_profiles: Dict[str, float]
    regime_stability: float
    data_quality_score: float


@dataclass
class ClusteringEvent:
    """Detected clustering regime change event"""
    timestamp: datetime
    event_type: str  # 'regime_change', 'volatility_spike', 'correlation_shift'
    previous_state: ClusteringState
    current_state: ClusteringState
    rand_index: float
    significance_score: float
    affected_pairs: List[str]
    description: str


@dataclass
class PerformanceMetrics:
    """System performance and statistics"""
    total_updates: int
    events_detected: int
    average_processing_time: float
    data_quality_average: float
    uptime_hours: float
    last_update: datetime


class StateManager:
    """
    Centralized state management for clustering application
    
    Handles:
    - Current and historical clustering states
    - Event detection using Adjusted Rand Index
    - Performance monitoring and metrics
    - State persistence and recovery
    """
    
    def __init__(self,
                 max_history_size: int = 1000,
                 event_threshold: float = 0.7,
                 persistence_dir: str = "data/state",
                 use_database: bool = True,
                 db_path: str = "data/clustering.db",
                 enable_advanced_analytics: bool = True,
                 enable_alerts: bool = True):
        """
        Initialize StateManager

        Args:
            max_history_size: Maximum number of historical states to keep
            event_threshold: ARI threshold for detecting regime changes
            persistence_dir: Directory for state persistence
            use_database: Whether to use database for persistence
            db_path: Path to SQLite database file
            enable_advanced_analytics: Whether to enable advanced analytics
            enable_alerts: Whether to enable alert system
        """
        self.max_history_size = max_history_size
        self.event_threshold = event_threshold
        self.persistence_dir = Path(persistence_dir)
        self.persistence_dir.mkdir(parents=True, exist_ok=True)

        # Database integration
        self.use_database = use_database and DATABASE_AVAILABLE
        self.db_manager: Optional[DatabaseManager] = None

        if self.use_database:
            try:
                self.db_manager = DatabaseManager(db_path)
                if self.db_manager.connect():
                    logger.info("StateManager: Database integration enabled")
                else:
                    logger.warning("StateManager: Database connection failed, using file persistence")
                    self.use_database = False
                    self.db_manager = None
            except Exception as e:
                logger.error(f"StateManager: Database initialization failed: {str(e)}")
                self.use_database = False
                self.db_manager = None
        else:
            logger.info("StateManager: Using file-based persistence")

        # Advanced analytics integration
        self.enable_advanced_analytics = enable_advanced_analytics and ADVANCED_ANALYTICS_AVAILABLE
        self.advanced_analytics: Optional[AdvancedAnalytics] = None

        if self.enable_advanced_analytics:
            try:
                self.advanced_analytics = AdvancedAnalytics(history_length=max_history_size)
                logger.info("StateManager: Advanced analytics enabled")
            except Exception as e:
                logger.error(f"StateManager: Advanced analytics initialization failed: {str(e)}")
                self.enable_advanced_analytics = False
                self.advanced_analytics = None

        # Alert system integration
        self.enable_alerts = enable_alerts and ADVANCED_ANALYTICS_AVAILABLE
        self.alert_system: Optional[AlertSystem] = None

        if self.enable_alerts:
            try:
                alert_config_path = self.persistence_dir / "alert_config.json"
                self.alert_system = AlertSystem(config_file=str(alert_config_path))
                logger.info("StateManager: Alert system enabled")
            except Exception as e:
                logger.error(f"StateManager: Alert system initialization failed: {str(e)}")
                self.enable_alerts = False
                self.alert_system = None

        # Current state
        self.current_state: Optional[ClusteringState] = None
        self.previous_state: Optional[ClusteringState] = None

        # Historical data
        self.state_history: deque = deque(maxlen=max_history_size)
        self.event_history: deque = deque(maxlen=max_history_size)

        # Performance tracking
        self.start_time = datetime.now(MARKET_TIMEZONE)
        self.processing_times: deque = deque(maxlen=100)
        self.update_count = 0
        self.event_count = 0

        # Thread safety
        self.lock = threading.RLock()

        # Volatility regime tracking
        self.volatility_regimes = {}

        # Load persisted state if available
        self._load_persisted_state()

        logger.info("StateManager initialized")
    
    def update_state(self, 
                     correlation_matrix: np.ndarray,
                     cluster_assignments: List[int],
                     symbols: List[str],
                     volatility_profiles: Dict[str, float],
                     data_quality_score: float = 1.0) -> Optional[ClusteringEvent]:
        """
        Update current clustering state and detect events
        
        Args:
            correlation_matrix: Current correlation matrix
            cluster_assignments: Cluster assignments for each symbol
            symbols: List of currency pair symbols
            volatility_profiles: Volatility data for each symbol
            data_quality_score: Quality score of input data (0-1)
            
        Returns:
            ClusteringEvent if regime change detected, None otherwise
        """
        start_time = time.time()
        
        with self.lock:
            try:
                # Create new state
                new_state = ClusteringState(
                    timestamp=datetime.now(MARKET_TIMEZONE),
                    correlation_matrix=correlation_matrix.copy(),
                    cluster_assignments=cluster_assignments.copy(),
                    cluster_count=len(set(cluster_assignments)),
                    symbols=symbols.copy(),
                    volatility_profiles=volatility_profiles.copy(),
                    regime_stability=self._calculate_regime_stability(cluster_assignments),
                    data_quality_score=data_quality_score
                )
                
                # Detect events if we have previous state
                event = None
                if self.current_state is not None:
                    event = self._detect_regime_change(self.current_state, new_state)
                
                # Update states
                self.previous_state = self.current_state
                self.current_state = new_state
                
                # Add to history
                self.state_history.append(new_state)
                if event:
                    self.event_history.append(event)
                    self.event_count += 1
                
                # Update performance metrics
                processing_time = time.time() - start_time
                self.processing_times.append(processing_time)
                self.update_count += 1

                # Advanced analytics integration
                advanced_events = []
                if self.enable_advanced_analytics and self.advanced_analytics:
                    try:
                        # Calculate advanced clustering metrics
                        features = self._prepare_features_for_analytics(correlation_matrix, volatility_profiles)
                        clustering_metrics = self.advanced_analytics.calculate_clustering_metrics(
                            features, np.array(cluster_assignments), symbols, new_state.timestamp
                        )

                        # Classify market regime
                        regime_classification = self.advanced_analytics.classify_regime(
                            np.array(cluster_assignments), symbols, correlation_matrix, new_state.timestamp
                        )

                        # Detect advanced events
                        advanced_events = self.advanced_analytics.detect_advanced_events(
                            clustering_metrics, regime_classification, symbols
                        )

                        # Process events through alert system
                        if self.enable_alerts and self.alert_system:
                            for adv_event in advanced_events:
                                self.alert_system.process_event(adv_event)

                        pass  # Advanced analytics events processed

                    except Exception as e:
                        logger.error(f"Error in advanced analytics: {e}")

                # Persist state periodically
                if self.update_count % 10 == 0:
                    self._persist_state()

                logger.info(f"State updated: {new_state.cluster_count} clusters, "
                           f"quality={data_quality_score:.3f}, "
                           f"stability={new_state.regime_stability:.3f}")

                if event:
                    logger.warning(f"Regime change detected: {event.event_type} "
                                 f"(ARI={event.rand_index:.3f})")

                if advanced_events:
                    logger.info(f"Advanced events detected: {[e.event_type for e in advanced_events]}")

                return event
                
            except Exception as e:
                logger.error(f"Error updating state: {str(e)}", exc_info=True)
                return None
    
    def _detect_regime_change(self, 
                             previous: ClusteringState, 
                             current: ClusteringState) -> Optional[ClusteringEvent]:
        """
        Detect regime changes using Adjusted Rand Index
        
        Args:
            previous: Previous clustering state
            current: Current clustering state
            
        Returns:
            ClusteringEvent if significant change detected
        """
        try:
            # Calculate Adjusted Rand Index
            if len(previous.cluster_assignments) != len(current.cluster_assignments):
                logger.warning("Cluster assignment length mismatch")
                return None
            
            rand_index = adjusted_rand_score(
                previous.cluster_assignments, 
                current.cluster_assignments
            )
            
            # Detect different types of events
            event_type = "regime_change"
            significance_score = 1.0 - rand_index
            
            # Check for volatility spikes
            prev_vol_avg = np.mean(list(previous.volatility_profiles.values()))
            curr_vol_avg = np.mean(list(current.volatility_profiles.values()))
            vol_change = abs(curr_vol_avg - prev_vol_avg) / prev_vol_avg
            
            if vol_change > 0.5:  # 50% volatility change
                event_type = "volatility_spike"
                significance_score = max(significance_score, vol_change)
            
            # Check for correlation shifts
            corr_change = np.mean(np.abs(
                previous.correlation_matrix - current.correlation_matrix
            ))
            
            if corr_change > 0.3:  # 30% correlation change
                event_type = "correlation_shift"
                significance_score = max(significance_score, corr_change)
            
            # Determine affected pairs
            affected_pairs = []
            for i, (prev_cluster, curr_cluster) in enumerate(
                zip(previous.cluster_assignments, current.cluster_assignments)
            ):
                if prev_cluster != curr_cluster and i < len(current.symbols):
                    affected_pairs.append(current.symbols[i])
            
            # Create event if threshold exceeded
            if rand_index < self.event_threshold or significance_score > 0.3:
                return ClusteringEvent(
                    timestamp=current.timestamp,
                    event_type=event_type,
                    previous_state=previous,
                    current_state=current,
                    rand_index=rand_index,
                    significance_score=significance_score,
                    affected_pairs=affected_pairs,
                    description=self._generate_event_description(
                        event_type, rand_index, significance_score, affected_pairs
                    )
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting regime change: {str(e)}", exc_info=True)
            return None
    
    def _calculate_regime_stability(self, cluster_assignments: List[int]) -> float:
        """
        Calculate regime stability score based on cluster distribution
        
        Args:
            cluster_assignments: Current cluster assignments
            
        Returns:
            Stability score (0-1, higher = more stable)
        """
        try:
            if not cluster_assignments:
                return 0.0
            
            # Calculate cluster size distribution
            unique_clusters, counts = np.unique(cluster_assignments, return_counts=True)
            cluster_sizes = counts / len(cluster_assignments)
            
            # Stability is higher when clusters are more evenly distributed
            # Use inverse of coefficient of variation
            if len(cluster_sizes) == 1:
                return 1.0
            
            cv = np.std(cluster_sizes) / np.mean(cluster_sizes)
            stability = 1.0 / (1.0 + cv)
            
            return min(max(stability, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating regime stability: {str(e)}")
            return 0.0
    
    def _generate_event_description(self, 
                                   event_type: str, 
                                   rand_index: float,
                                   significance_score: float,
                                   affected_pairs: List[str]) -> str:
        """Generate human-readable event description"""
        
        descriptions = {
            "regime_change": f"Market regime shift detected (ARI={rand_index:.3f})",
            "volatility_spike": f"Volatility spike detected (score={significance_score:.3f})",
            "correlation_shift": f"Correlation structure change (score={significance_score:.3f})"
        }
        
        base_desc = descriptions.get(event_type, "Market event detected")
        
        if affected_pairs:
            pair_list = ", ".join(affected_pairs[:5])
            if len(affected_pairs) > 5:
                pair_list += f" and {len(affected_pairs) - 5} others"
            base_desc += f". Affected pairs: {pair_list}"
        
        return base_desc

    def get_current_state(self) -> Optional[ClusteringState]:
        """Get current clustering state"""
        with self.lock:
            return self.current_state

    def get_recent_events(self, hours_back: int = 24) -> List[ClusteringEvent]:
        """
        Get recent events within specified time window

        Args:
            hours_back: Number of hours to look back

        Returns:
            List of recent events
        """
        cutoff_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=hours_back)

        with self.lock:
            return [
                event for event in self.event_history
                if event.timestamp >= cutoff_time
            ]

    def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        with self.lock:
            uptime = (datetime.now(MARKET_TIMEZONE) - self.start_time).total_seconds() / 3600
            avg_processing_time = np.mean(self.processing_times) if self.processing_times else 0.0

            # Calculate average data quality from recent states
            recent_states = list(self.state_history)[-50:]  # Last 50 states
            avg_quality = np.mean([s.data_quality_score for s in recent_states]) if recent_states else 0.0

            return PerformanceMetrics(
                total_updates=self.update_count,
                events_detected=self.event_count,
                average_processing_time=avg_processing_time,
                data_quality_average=avg_quality,
                uptime_hours=uptime,
                last_update=self.current_state.timestamp if self.current_state else self.start_time
            )

    def get_state_history(self, limit: int = 100) -> List[ClusteringState]:
        """
        Get historical states

        Args:
            limit: Maximum number of states to return

        Returns:
            List of historical states (most recent first)
        """
        with self.lock:
            history = list(self.state_history)
            return history[-limit:] if limit else history

    def get_cluster_stability_trend(self, hours_back: int = 24) -> List[Tuple[datetime, float]]:
        """
        Get cluster stability trend over time

        Args:
            hours_back: Number of hours to analyze

        Returns:
            List of (timestamp, stability_score) tuples
        """
        cutoff_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=hours_back)

        with self.lock:
            trend_data = [
                (state.timestamp, state.regime_stability)
                for state in self.state_history
                if state.timestamp >= cutoff_time
            ]

            return sorted(trend_data, key=lambda x: x[0])

    def _persist_state(self):
        """Persist current state and recent history to disk and database"""
        try:
            # Database persistence (preferred)
            if self.use_database and self.db_manager:
                try:
                    # Store current state in database
                    if self.current_state:
                        self.db_manager.store_clustering_state(self.current_state)

                    # Store recent events in database
                    recent_events = list(self.event_history)[-10:]  # Last 10 events
                    for event in recent_events:
                        # For database storage, we need to handle the event structure
                        # Store basic event data without full state objects
                        self.db_manager.execute_query("""
                            INSERT OR IGNORE INTO clustering_events (
                                timestamp, event_type, rand_index, significance_score,
                                affected_pairs, description
                            ) VALUES (?, ?, ?, ?, ?, ?)
                        """, (
                            event.timestamp.isoformat(),
                            event.event_type,
                            event.rand_index,
                            event.significance_score,
                            json.dumps(event.affected_pairs),
                            event.description
                        ))

                    # Store performance metrics
                    metrics = self.get_performance_metrics()
                    self.db_manager.store_performance_metrics(metrics)

                    pass  # State persisted to database successfully
                    return  # Skip file persistence if database works

                except Exception as e:
                    logger.error(f"Database persistence failed: {str(e)}")
                    # Fall back to file persistence

            # File persistence (fallback)
            # Save current state
            if self.current_state:
                state_file = self.persistence_dir / "current_state.pkl"
                with open(state_file, 'wb') as f:
                    pickle.dump(self.current_state, f)

            # Save recent events
            recent_events = list(self.event_history)[-100:]  # Last 100 events
            events_file = self.persistence_dir / "recent_events.pkl"
            with open(events_file, 'wb') as f:
                pickle.dump(recent_events, f)

            # Save performance metrics
            metrics = self.get_performance_metrics()
            metrics_file = self.persistence_dir / "performance_metrics.json"
            with open(metrics_file, 'w') as f:
                # Convert to dict and handle datetime serialization
                metrics_dict = asdict(metrics)
                metrics_dict['last_update'] = metrics_dict['last_update'].isoformat()
                json.dump(metrics_dict, f, indent=2)

            pass  # State persisted successfully

        except Exception as e:
            logger.error(f"Error persisting state: {str(e)}", exc_info=True)

    def _load_persisted_state(self):
        """Load persisted state from disk"""
        try:
            # Load current state
            state_file = self.persistence_dir / "current_state.pkl"
            if state_file.exists():
                with open(state_file, 'rb') as f:
                    self.current_state = pickle.load(f)
                logger.info("Loaded persisted current state")

            # Load recent events
            events_file = self.persistence_dir / "recent_events.pkl"
            if events_file.exists():
                with open(events_file, 'rb') as f:
                    events = pickle.load(f)
                    self.event_history.extend(events)
                logger.info(f"Loaded {len(events)} persisted events")

            # Load performance metrics for continuity
            metrics_file = self.persistence_dir / "performance_metrics.json"
            if metrics_file.exists():
                with open(metrics_file, 'r') as f:
                    metrics_dict = json.load(f)
                    self.update_count = metrics_dict.get('total_updates', 0)
                    self.event_count = metrics_dict.get('events_detected', 0)
                logger.info("Loaded persisted performance metrics")

        except Exception as e:
            logger.error(f"Error loading persisted state: {str(e)}", exc_info=True)

    def _prepare_features_for_analytics(self, correlation_matrix: np.ndarray, volatility_profiles: Dict[str, float]) -> np.ndarray:
        """
        Prepare feature matrix for advanced analytics

        Args:
            correlation_matrix: Current correlation matrix
            volatility_profiles: Volatility data for each symbol

        Returns:
            Feature matrix for clustering analysis
        """
        try:
            # Use correlation vectors as primary features
            features = correlation_matrix.copy()

            # Add volatility as additional feature if available
            if volatility_profiles:
                vol_values = list(volatility_profiles.values())
                if len(vol_values) == features.shape[0]:
                    # Normalize volatility values
                    vol_array = np.array(vol_values).reshape(-1, 1)
                    vol_normalized = (vol_array - np.mean(vol_array)) / (np.std(vol_array) + 1e-10)
                    features = np.hstack([features, vol_normalized])

            return features

        except Exception as e:
            logger.error(f"Error preparing features for analytics: {e}")
            return correlation_matrix

    def get_advanced_analytics_summary(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get summary of advanced analytics data"""
        if not self.enable_advanced_analytics or not self.advanced_analytics:
            return {"error": "Advanced analytics not available"}

        try:
            return self.advanced_analytics.get_metrics_summary(hours_back)
        except Exception as e:
            logger.error(f"Error getting analytics summary: {e}")
            return {"error": str(e)}

    def get_alert_history(self, hours_back: int = 24) -> List[Dict[str, Any]]:
        """Get alert history"""
        if not self.enable_alerts or not self.alert_system:
            return []

        try:
            return self.alert_system.get_alert_history(hours_back)
        except Exception as e:
            logger.error(f"Error getting alert history: {e}")
            return []

    def get_current_regime_classification(self) -> Optional[Dict[str, Any]]:
        """Get current market regime classification"""
        if not self.enable_advanced_analytics or not self.advanced_analytics:
            return None

        try:
            if self.advanced_analytics.regime_history:
                latest_regime = self.advanced_analytics.regime_history[-1]
                return {
                    "timestamp": latest_regime.timestamp.isoformat(),
                    "regime_type": latest_regime.regime_type.value,
                    "confidence": latest_regime.confidence,
                    "description": latest_regime.description,
                    "cluster_distribution": latest_regime.cluster_distribution,
                    "dominant_cluster_ratio": latest_regime.dominant_cluster_ratio,
                    "fragmentation_index": latest_regime.fragmentation_index
                }
            return None
        except Exception as e:
            logger.error(f"Error getting regime classification: {e}")
            return None

    def clear_history(self):
        """Clear all historical data (keep current state)"""
        with self.lock:
            self.state_history.clear()
            self.event_history.clear()
            self.processing_times.clear()

            # Clear advanced analytics history
            if self.enable_advanced_analytics and self.advanced_analytics:
                self.advanced_analytics.metrics_history.clear()
                self.advanced_analytics.regime_history.clear()
                self.advanced_analytics.event_history.clear()

            logger.info("State history cleared")

    def export_state_summary(self) -> Dict[str, Any]:
        """
        Export comprehensive state summary for external use

        Returns:
            Dictionary containing current state and statistics
        """
        with self.lock:
            summary = {
                'timestamp': datetime.now(MARKET_TIMEZONE).isoformat(),
                'current_state': None,
                'performance_metrics': asdict(self.get_performance_metrics()),
                'recent_events_count': len(self.get_recent_events(24)),
                'total_states_recorded': len(self.state_history),
                'system_status': 'active' if self.current_state else 'inactive'
            }

            # Add current state info if available
            if self.current_state:
                summary['current_state'] = {
                    'timestamp': self.current_state.timestamp.isoformat(),
                    'cluster_count': self.current_state.cluster_count,
                    'regime_stability': self.current_state.regime_stability,
                    'data_quality_score': self.current_state.data_quality_score,
                    'symbols_count': len(self.current_state.symbols)
                }

            # Convert datetime in performance metrics
            summary['performance_metrics']['last_update'] = summary['performance_metrics']['last_update'].isoformat()

            return summary

    # ===== VOLATILITY REGIME CLUSTERING METHODS =====

    def update_volatility_regimes(self, num_days_history: int = 7, n_clusters: int = 5) -> bool:
        """
        Update volatility regime clustering using historical data

        Args:
            num_days_history: Number of days to include in clustering
            n_clusters: Number of volatility regimes to identify

        Returns:
            bool: True if update successful, False otherwise
        """
        try:
            # Initialize cache and lock for thread safety
            if not hasattr(self, '_volatility_cache'):
                self._volatility_cache = {
                    'last_update': None,
                    'cache_duration': 60,  # 1 minute cache (shorter for testing)
                    'num_days': None,
                    'n_clusters': None,
                    'in_progress': False,  # Flag to indicate if update is in progress
                    'result': None  # Store the result for waiting threads
                }
            if not hasattr(self, '_volatility_lock'):
                self._volatility_lock = threading.Lock()

            current_time = datetime.now(MARKET_TIMEZONE)
            cache = self._volatility_cache

            # Check if we can use cached data (outside lock for performance)
            if (cache['last_update'] is not None and
                cache['num_days'] == num_days_history and
                cache['n_clusters'] == n_clusters and
                (current_time - cache['last_update']).total_seconds() < cache['cache_duration']):
                logger.info(f"Using cached volatility regime data (updated {(current_time - cache['last_update']).total_seconds():.1f}s ago)")
                return True

            # Acquire lock to prevent concurrent data collection
            with self._volatility_lock:
                # Double-check cache after acquiring lock (another thread might have updated it)
                current_time = datetime.now(MARKET_TIMEZONE)
                if (cache['last_update'] is not None and
                    cache['num_days'] == num_days_history and
                    cache['n_clusters'] == n_clusters and
                    (current_time - cache['last_update']).total_seconds() < cache['cache_duration']):
                    logger.info(f"Using cached volatility regime data after lock (updated {(current_time - cache['last_update']).total_seconds():.1f}s ago)")
                    return True

                # Check if another thread is already processing
                if cache['in_progress']:
                    logger.info(f"Another thread is updating volatility regimes, waiting...")
                    # Wait for the other thread to complete (with timeout)
                    wait_count = 0
                    while cache['in_progress'] and wait_count < 30:  # Wait up to 30 seconds
                        time.sleep(1)
                        wait_count += 1

                    # Check if the other thread completed successfully
                    if (cache['last_update'] is not None and
                        cache['num_days'] == num_days_history and
                        cache['n_clusters'] == n_clusters and
                        (datetime.now(MARKET_TIMEZONE) - cache['last_update']).total_seconds() < cache['cache_duration']):
                        logger.info(f"Using volatility regime data updated by another thread")
                        return True

                # Mark as in progress
                cache['in_progress'] = True
                logger.info(f"Starting volatility regime update (cache miss or expired)")
                # Continue with actual data collection...
            # Import cluster_core for volatility functions
            import cluster_core

            # Get historical price data for volatility calculation
            historical_data = self._get_historical_price_data(num_days_history)
            if not historical_data:
                logger.warning("No historical data available for volatility regime clustering")
                return False

            # Calculate daily volatility profiles
            symbols = list(self.current_state.symbols) if self.current_state else []
            if not symbols:
                logger.warning("No symbols available for volatility regime clustering")
                return False

            # Debug: Log data being passed to Rust
            logger.info(f"Passing {len(historical_data)} data points to Rust for {len(symbols)} symbols")
            if historical_data:
                sample_data = historical_data[0]
                logger.info(f"Sample data point: symbol={sample_data.symbol}, timestamp={sample_data.timestamp}, open={sample_data.open}")

                # Check data distribution by symbol and date
                from collections import defaultdict
                symbol_counts = defaultdict(int)
                date_counts = defaultdict(int)
                for data_point in historical_data[:1000]:  # Sample first 1000 points
                    symbol_counts[data_point.symbol] += 1
                    # Convert timestamp to date for debugging
                    date_str = datetime.fromtimestamp(data_point.timestamp).strftime('%Y-%m-%d')
                    date_counts[date_str] += 1

                logger.info(f"Sample symbol distribution: {dict(list(symbol_counts.items())[:5])}")
                logger.info(f"Sample date distribution: {dict(list(date_counts.items())[:5])}")

            # Use M5 (5-minute) windows for fine-grained volatility regime detection
            # This creates more granular time windows for better regime identification
            logger.info("Using Python implementation for market-wide volatility profile calculation")
            logger.info(f"Processing {len(historical_data)} data points, {len(symbols)} symbols, window=5 (market-wide approach)")

            try:
                # Use Python implementation with 5-minute windows
                profiles = self._calculate_volatility_profiles_python(historical_data, symbols, 5)
                logger.info(f"Python implementation returned {len(profiles) if profiles else 0} profiles")
            except Exception as e:
                logger.error(f"Error in Python volatility calculation: {e}")
                profiles = []

            if not profiles:
                logger.warning("No volatility profiles calculated")
                return False

            # Perform K-means clustering on volatility profiles using Python
            try:
                regime_result = self._cluster_volatility_regimes_python(profiles, n_clusters)
                logger.info(f"Python market-wide clustering completed: {regime_result.n_regimes} regimes, inertia: {regime_result.inertia:.4f}")
            except Exception as e:
                logger.error(f"Error in Python clustering: {e}")
                return False

            # Store results in volatility regimes history
            if not hasattr(self, 'volatility_regimes_history'):
                self.volatility_regimes_history = {}

            timestamp = datetime.now(MARKET_TIMEZONE)
            regime_data = {
                'regime_assignments': regime_result.regime_assignments,
                'regime_centroids': regime_result.regime_centroids,
                'dates': regime_result.dates,
                'n_regimes': regime_result.n_regimes,
                'inertia': regime_result.inertia,
                'profiles': profiles
            }

            # Store in history
            self.volatility_regimes_history[timestamp] = regime_data

            # Update current volatility regimes for getter methods
            self.volatility_regimes = regime_data

            # Save to database if available
            if self.db_manager:
                try:
                    self.db_manager.save_volatility_regime_result(timestamp, regime_result)
                except Exception as e:
                    logger.warning(f"Failed to save volatility regime result to database: {e}")

            logger.info(f"Updated market volatility regimes: {n_clusters} regimes, {len(profiles)} market profiles, inertia: {regime_result.inertia:.4f}")

            # Update cache and mark as complete (inside lock)
            self._volatility_cache.update({
                'last_update': current_time,
                'num_days': num_days_history,
                'n_clusters': n_clusters,
                'in_progress': False,  # Mark as complete
                'result': True
            })

            return True

        except Exception as e:
            logger.error(f"Error updating volatility regimes: {e}")
            # Mark as no longer in progress on error
            if hasattr(self, '_volatility_cache'):
                self._volatility_cache['in_progress'] = False
            return False

    def _calculate_volatility_profiles_python(self, price_data: List[Any], symbols: List[str], window_minutes: int) -> List[Any]:
        """Python implementation of volatility profile calculation for debugging"""
        import math
        from collections import defaultdict
        from datetime import datetime

        logger.info(f"Starting Python volatility profiles calculation with {len(price_data)} data points, {len(symbols)} symbols, {window_minutes} minute windows")

        # Group data by time window and symbol
        time_window_data = defaultdict(lambda: defaultdict(list))

        for i, data_point in enumerate(price_data):
            # Create time window key (e.g., "2025-06-09_14:05" for M5)
            window_key = self._format_timestamp_to_window_python(data_point.timestamp, window_minutes)
            time_window_data[window_key][data_point.symbol].append(data_point.close)

            # Debug first few data points
            if i < 5:
                logger.info(f"Data point {i}: symbol={data_point.symbol}, timestamp={data_point.timestamp}, window_key={window_key}, close={data_point.close}")

        logger.info(f"Created {len(time_window_data)} time windows")

        # Debug window distribution
        sample_windows = list(time_window_data.keys())[:5]
        for window_key in sample_windows:
            symbol_data = time_window_data[window_key]
            logger.info(f"Window {window_key}: {len(symbol_data)} symbols, sample sizes: {[(sym, len(prices)) for sym, prices in list(symbol_data.items())[:3]]}")

        profiles = []
        windows_processed = 0
        windows_skipped = 0

        # Create market-wide volatility profiles (one per time window, not per symbol)
        for window_key, symbol_data in time_window_data.items():
            # Aggregate volatility across all symbols for this time window
            window_volatilities = []
            valid_symbols = 0

            for symbol in symbols:
                if symbol in symbol_data:
                    prices = symbol_data[symbol]
                    # For M5 windows, we expect exactly 5 data points per window
                    # Require at least 3 data points for basic volatility calculation
                    if len(prices) >= 3:
                        volatility_vector = self._calculate_simple_volatility_profile_python(prices)
                        avg_volatility = sum(volatility_vector) / len(volatility_vector)
                        window_volatilities.extend(volatility_vector)  # Add to market aggregate
                        valid_symbols += 1
                    else:
                        windows_skipped += 1
                        if windows_skipped <= 5:  # Only log first 5 to avoid spam
                            logger.info(f"Skipping window {window_key} for symbol {symbol} - only {len(prices)} data points")

            # Only create a market profile if we have data from multiple symbols
            if valid_symbols >= 10:  # Require at least 10 symbols for reliable market regime
                # Create market-wide volatility profile for this time window
                # Pad or truncate to exactly 24 features for consistency
                if len(window_volatilities) > 24:
                    # Take first 24 features if we have too many
                    market_volatility = window_volatilities[:24]
                elif len(window_volatilities) < 24:
                    # Pad with zeros if we have too few
                    market_volatility = window_volatilities + [0.0] * (24 - len(window_volatilities))
                else:
                    market_volatility = window_volatilities

                avg_market_volatility = sum(market_volatility) / len(market_volatility)

                # Create a single market profile for this time window
                profile = type('VolatilityProfile', (), {
                    'symbol': 'MARKET',  # Represents entire market, not individual symbol
                    'date': window_key,
                    'hourly_volatility': market_volatility,
                    'daily_volatility': avg_market_volatility,
                    'regime_id': None
                })()

                profiles.append(profile)
                windows_processed += 1
            else:
                logger.info(f"Skipping window {window_key} - only {valid_symbols} valid symbols (need >= 10)")
                windows_skipped += 1

        logger.info(f"Processed {windows_processed} market-wide windows, skipped {windows_skipped} windows, created {len(profiles)} market profiles")
        logger.info(f"Market-wide volatility regime approach: 1 regime per time window (not per symbol)")
        return profiles

    def _format_timestamp_to_window_python(self, timestamp: int, window_minutes: int) -> str:
        """Python implementation of timestamp to window formatting"""
        from datetime import datetime, timedelta

        # Convert timestamp to time window string (e.g., "2025-06-09_14:05" for 14:05)
        window_seconds = window_minutes * 60
        window_number = timestamp // window_seconds
        window_timestamp = window_number * window_seconds

        # Convert to datetime
        dt = datetime.fromtimestamp(window_timestamp)

        # Format as YYYY-MM-DD_HH:MM for M5 granularity
        return dt.strftime("%Y-%m-%d_%H:%M")

    def _calculate_simple_volatility_profile_python(self, prices: List[float]) -> List[float]:
        """Python implementation of volatility profile calculation"""
        import math

        TARGET_FEATURES = 24  # Always return exactly 24 features (representing 24 hours)

        if len(prices) < 3:
            return [0.0] * TARGET_FEATURES

        # Calculate returns first
        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] > 0.0 and prices[i] > 0.0:
                return_val = math.log(prices[i] / prices[i-1])
                if math.isfinite(return_val):
                    returns.append(return_val)

        if len(returns) < 1:
            return [0.0] * TARGET_FEATURES

        # Calculate volatility for each hour-equivalent window
        window_size = max(1, len(returns) // TARGET_FEATURES)
        volatility_profile = []

        for i in range(0, len(returns), window_size):
            chunk = returns[i:i + window_size]
            if len(chunk) >= 2:
                # Calculate standard deviation of returns for this window
                mean = sum(chunk) / len(chunk)
                variance = sum((x - mean) ** 2 for x in chunk) / len(chunk)
                volatility = math.sqrt(variance)

                # Annualize volatility (assuming 5-minute data, 288 periods per day)
                annualized_vol = volatility * math.sqrt(288.0)

                volatility_profile.append(annualized_vol)
            elif len(chunk) == 1:
                # For single return, use absolute return as proxy for volatility
                abs_return = abs(chunk[0])
                annualized_vol = abs_return * math.sqrt(288.0)
                volatility_profile.append(annualized_vol)
            else:
                volatility_profile.append(0.0)

            if len(volatility_profile) >= TARGET_FEATURES:
                break

        # Pad or truncate to exactly TARGET_FEATURES
        while len(volatility_profile) < TARGET_FEATURES:
            volatility_profile.append(0.0)

        volatility_profile = volatility_profile[:TARGET_FEATURES]

        return volatility_profile

    def _cluster_volatility_regimes_python(self, profiles: List[Any], n_clusters: int) -> List[Any]:
        """Python implementation of K-means clustering for volatility regimes"""
        try:
            from sklearn.cluster import KMeans
            import numpy as np
        except ImportError:
            logger.error("scikit-learn not available for Python clustering")
            return profiles

        if not profiles:
            logger.warning("No profiles to cluster")
            return profiles

        logger.info(f"Clustering {len(profiles)} profiles with {n_clusters} clusters")

        # Extract volatility vectors from profiles
        volatility_matrix = []
        for profile in profiles:
            volatility_matrix.append(profile.hourly_volatility)

        # Convert to numpy array
        X = np.array(volatility_matrix)
        logger.info(f"Created {X.shape[0]}x{X.shape[1]} volatility matrix")

        # Check for all-zero data
        data_min = X.min()
        data_max = X.max()
        data_mean = X.mean()
        logger.info(f"Data range: min={data_min:.6f}, max={data_max:.6f}, mean={data_mean:.6f}")

        if data_max == 0.0:
            logger.warning("All volatility data is zero - assigning all profiles to regime 1")
            for profile in profiles:
                profile.regime_id = 1  # Use 1-based indexing
            return profiles

        # Perform K-means clustering
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(X)

        # Assign regime IDs to profiles (convert to 1-based indexing)
        for i, profile in enumerate(profiles):
            profile.regime_id = int(cluster_labels[i]) + 1  # Convert 0-based to 1-based

        # Log cluster distribution
        cluster_counts = {}
        for label in cluster_labels:
            cluster_counts[label] = cluster_counts.get(label, 0) + 1

        logger.info(f"Cluster distribution: {cluster_counts}")
        logger.info(f"K-means inertia: {kmeans.inertia_:.6f}")

        # Create a result object that matches the expected Rust interface
        class VolatilityRegimeResult:
            def __init__(self, profiles, kmeans):
                self.regime_assignments = [profile.regime_id for profile in profiles]
                self.regime_centroids = kmeans.cluster_centers_.tolist()
                self.dates = [profile.date for profile in profiles]
                self.n_regimes = len(kmeans.cluster_centers_)
                self.inertia = kmeans.inertia_
                self.profiles = profiles

        return VolatilityRegimeResult(profiles, kmeans)

    def get_intraday_regime_match(self, daily_volatility_vector: List[float]) -> Optional[List[int]]:
        """
        Match an intraday volatility vector to regime archetypes

        Args:
            daily_volatility_vector: 24-hour volatility vector

        Returns:
            List of regime IDs for each hour, or None if no regimes available
        """
        try:
            import cluster_core

            if not hasattr(self, 'volatility_regimes_history') or not self.volatility_regimes_history:
                logger.warning("No volatility regimes available for matching")
                return None

            # Get the most recent regime centroids
            latest_timestamp = max(self.volatility_regimes_history.keys())
            latest_regimes = self.volatility_regimes_history[latest_timestamp]
            regime_centroids = latest_regimes['regime_centroids']

            # Match intraday volatility to regimes
            regime_matches = cluster_core.match_intraday_regimes(daily_volatility_vector, regime_centroids)

            return regime_matches

        except Exception as e:
            logger.error(f"Error matching intraday regimes: {e}")
            return None

    def get_volatility_regime_calendar_data(self, start_date: Optional[datetime] = None,
                                          end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Get volatility regime data formatted for calendar display

        Args:
            start_date: Start date for calendar data
            end_date: End date for calendar data

        Returns:
            Dictionary with calendar data including dates, regimes, and colors
        """
        try:
            if not hasattr(self, 'volatility_regimes_history') or not self.volatility_regimes_history:
                return {'dates': [], 'regimes': [], 'colors': [], 'regime_names': []}

            # Get the most recent regime data
            latest_timestamp = max(self.volatility_regimes_history.keys())
            latest_regimes = self.volatility_regimes_history[latest_timestamp]

            dates = latest_regimes['dates']
            regime_assignments = latest_regimes['regime_assignments']
            n_regimes = latest_regimes['n_regimes']

            # Filter to current day only to avoid overloading Dash
            current_date = datetime.now(MARKET_TIMEZONE).strftime('%Y-%m-%d')

            # Find regime transitions for current day
            current_day_data = []
            current_day_regimes = []

            for i, date_str in enumerate(dates):
                # Extract date from M5 timestamp format (YYYY-MM-DD_HH:MM)
                if '_' in date_str:
                    date_part = date_str.split('_')[0]
                else:
                    date_part = date_str

                if date_part == current_date:
                    current_day_data.append(date_str)
                    current_day_regimes.append(regime_assignments[i])

            # If no current day data, use most recent day
            if not current_day_data:
                # Get the most recent date
                recent_dates = set()
                for date_str in dates:
                    if '_' in date_str:
                        date_part = date_str.split('_')[0]
                    else:
                        date_part = date_str
                    recent_dates.add(date_part)

                if recent_dates:
                    most_recent_date = max(recent_dates)
                    for i, date_str in enumerate(dates):
                        if '_' in date_str:
                            date_part = date_str.split('_')[0]
                        else:
                            date_part = date_str

                        if date_part == most_recent_date:
                            current_day_data.append(date_str)
                            current_day_regimes.append(regime_assignments[i])

            # Detect regime transitions to reduce data points
            if current_day_data and current_day_regimes:
                transition_data = []
                transition_regimes = []

                # Always include first data point
                transition_data.append(current_day_data[0])
                transition_regimes.append(current_day_regimes[0])

                # Add points where regime changes
                for i in range(1, len(current_day_regimes)):
                    if current_day_regimes[i] != current_day_regimes[i-1]:
                        transition_data.append(current_day_data[i])
                        transition_regimes.append(current_day_regimes[i])

                # Always include last data point if different from last transition
                if len(transition_data) == 1 or current_day_data[-1] != transition_data[-1]:
                    transition_data.append(current_day_data[-1])
                    transition_regimes.append(current_day_regimes[-1])

                dates = transition_data
                regime_assignments = transition_regimes
            else:
                dates = []
                regime_assignments = []

            # Generate colors for regimes
            colors = self._generate_regime_colors(n_regimes)
            # Convert 1-based regime IDs to 0-based for color indexing
            regime_colors = [colors[regime_id - 1] for regime_id in regime_assignments]

            # Generate regime names
            regime_names = [f"Regime {i+1}" for i in range(n_regimes)]

            # Debug logging
            logger.info(f"Calendar data (current day transitions): {len(dates)} transition points")
            logger.info(f"Regime assignments: {regime_assignments}")
            logger.info(f"Unique regimes: {set(regime_assignments) if regime_assignments else set()}")
            logger.info(f"Transition times: {dates[:10] if dates else []}")  # Limit log output

            return {
                'dates': list(dates),
                'regimes': list(regime_assignments),
                'colors': regime_colors,
                'regime_names': regime_names,
                'regime_explanations': self.get_regime_explanations(),
                'n_regimes': n_regimes,
                'inertia': latest_regimes.get('inertia', 0.0)
            }

        except Exception as e:
            logger.error(f"Error getting volatility regime calendar data: {e}")
            return {'dates': [], 'regimes': [], 'colors': [], 'regime_names': []}

    def get_daily_volatility_detail(self, date_str: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed volatility information for a specific date

        Args:
            date_str: Date string in YYYY-MM-DD format

        Returns:
            Dictionary with detailed volatility data for the date
        """
        try:
            if not hasattr(self, 'volatility_regimes_history') or not self.volatility_regimes_history:
                return None

            # Get the most recent regime data
            latest_timestamp = max(self.volatility_regimes_history.keys())
            latest_regimes = self.volatility_regimes_history[latest_timestamp]

            # Find the profile for the requested date
            profiles = latest_regimes.get('profiles', [])
            date_profile = None

            for profile in profiles:
                if profile.date == date_str:
                    date_profile = profile
                    break

            if not date_profile:
                logger.warning(f"No volatility profile found for date: {date_str}")
                return None

            # Get regime assignment for this date
            dates = latest_regimes['dates']
            regime_assignments = latest_regimes['regime_assignments']

            regime_id = None
            if date_str in dates:
                date_index = dates.index(date_str)
                regime_id = regime_assignments[date_index]

            # Get intraday regime matches
            intraday_regimes = self.get_intraday_regime_match(date_profile.hourly_volatility)

            return {
                'date': date_str,
                'symbol': date_profile.symbol,
                'hourly_volatility': date_profile.hourly_volatility,
                'daily_volatility': date_profile.daily_volatility,
                'regime_id': regime_id,
                'intraday_regimes': intraday_regimes,
                'hours': list(range(24))
            }

        except Exception as e:
            logger.error(f"Error getting daily volatility detail for {date_str}: {e}")
            return None

    def _get_historical_price_data(self, num_days: int) -> List[Any]:
        """
        Get historical price data for volatility calculation using MT5 data

        Args:
            num_days: Number of days of historical data to retrieve

        Returns:
            List of FxPriceData objects
        """
        try:
            import cluster_core
            from datetime import datetime, timedelta
            import MetaTrader5 as mt5

            historical_data = []

            # Get symbols from current state
            symbols = list(self.current_state.symbols) if self.current_state else []
            if not symbols:
                logger.warning("No symbols available for historical data collection")
                return []

            # Calculate date range for historical data collection
            end_time = datetime.now(MARKET_TIMEZONE)
            start_time = end_time - timedelta(days=num_days)

            logger.info(f"Collecting {num_days} days of minute-level historical data from {start_time} to {end_time}")

            # Collect minute-level data for each symbol
            total_data_points = 0
            for symbol in symbols:
                try:
                    # Get M1 (1-minute) data from MT5
                    rates = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_M1, start_time, end_time)

                    if rates is not None and len(rates) > 0:
                        for rate in rates:
                            # Convert MT5 rate to our price data format
                            timestamp = datetime.fromtimestamp(rate['time'], tz=MARKET_TIMEZONE)

                            # Skip weekends (Saturday=5, Sunday=6) for forex data
                            if timestamp.weekday() >= 5:
                                continue

                            # Create price data point - MT5 timestamp is already in local time
                            # (converted in data_manager.py line 278)
                            price_data = cluster_core.FxPriceData(
                                symbol=symbol,
                                timestamp=int(rate['time']),  # Use MT5 timestamp directly (already local time)
                                open=float(rate['open']),
                                high=float(rate['high']),
                                low=float(rate['low']),
                                close=float(rate['close']),
                                volume=float(rate['tick_volume'])
                            )
                            historical_data.append(price_data)
                            total_data_points += 1

                        logger.info(f"Collected {len(rates)} M1 data points for {symbol}")
                    else:
                        logger.warning(f"No historical data available for {symbol}")

                except Exception as e:
                    logger.warning(f"Failed to collect data for {symbol}: {e}")
                    # Fallback to mock data for this symbol
                    self._generate_mock_data_for_symbol(symbol, start_time, end_time, historical_data)

            logger.info(f"Total historical data points collected: {total_data_points}")
            return historical_data

        except Exception as e:
            logger.error(f"Error collecting historical price data: {e}")
            # Fallback to mock data generation
            return self._generate_fallback_mock_data(num_days)

    def _generate_mock_data_for_symbol(self, symbol: str, start_time: datetime, end_time: datetime, historical_data: List[Any]):
        """Generate mock minute-level data for a single symbol"""
        try:
            import cluster_core

            current_time = start_time
            base_price = 1.0 + hash(symbol) % 100 / 10000  # Generate base price from symbol

            while current_time < end_time:
                # Skip weekends
                if current_time.weekday() < 5:  # Monday=0 to Friday=4
                    # Generate realistic minute-level price movement
                    price_change = np.random.normal(0, 0.0001)  # Small random movements
                    base_price += price_change

                    # Create price data point - use local timestamp
                    local_timestamp = current_time.timestamp()
                    price_data = cluster_core.FxPriceData(
                        symbol=symbol,
                        timestamp=int(local_timestamp),
                        open=base_price,
                        high=base_price + abs(np.random.normal(0, 0.00005)),
                        low=base_price - abs(np.random.normal(0, 0.00005)),
                        close=base_price,
                        volume=float(np.random.randint(100, 1000))
                    )
                    historical_data.append(price_data)

                current_time += timedelta(minutes=1)

        except Exception as e:
            logger.error(f"Error generating mock data for {symbol}: {e}")

    def _generate_fallback_mock_data(self, num_days: int) -> List[Any]:
        """Generate fallback mock data when MT5 data collection fails"""
        try:
            import cluster_core

            historical_data = []
            symbols = list(self.current_state.symbols) if self.current_state else ['EURUSD', 'GBPUSD', 'USDJPY']

            end_time = datetime.now(MARKET_TIMEZONE)
            start_time = end_time - timedelta(days=num_days)

            logger.info(f"Generating fallback mock data for {len(symbols)} symbols over {num_days} days")

            for symbol in symbols:
                self._generate_mock_data_for_symbol(symbol, start_time, end_time, historical_data)

            return historical_data

        except Exception as e:
            logger.error(f"Error generating fallback mock data: {e}")
            return []

    def _generate_regime_colors(self, n_regimes: int) -> List[str]:
        """
        Generate distinct colors for volatility regimes

        Args:
            n_regimes: Number of regimes to generate colors for

        Returns:
            List of color strings
        """
        # Use distinct colors that represent volatility characteristics
        regime_colors = {
            0: '#1f77b4',  # Blue - Low Volatility (Calm markets)
            1: '#ff7f0e',  # Orange - Medium-Low Volatility (Steady movement)
            2: '#2ca02c',  # Green - Medium Volatility (Normal trading)
            3: '#d62728',  # Red - High Volatility (Active markets)
            4: '#9467bd',  # Purple - Very High Volatility (Extreme movements)
            5: '#8c564b',  # Brown - Ultra High Volatility (Crisis/News events)
            6: '#e377c2',  # Pink - Regime 6
            7: '#7f7f7f',  # Gray - Regime 7
            8: '#bcbd22',  # Olive - Regime 8
            9: '#17becf'   # Cyan - Regime 9
        }

        colors = []
        for i in range(n_regimes):
            colors.append(regime_colors.get(i, '#cccccc'))  # Default gray for unknown regimes

        return colors

    def get_regime_colors(self, n_regimes: int) -> List[str]:
        """Get colors for volatility regimes (alias for _generate_regime_colors)"""
        return self._generate_regime_colors(n_regimes)

    def get_regime_explanations(self) -> Dict[int, str]:
        """Get explanations for each volatility regime"""
        return {
            1: "Low Volatility - Calm markets with minimal price movements",
            2: "Medium-Low Volatility - Steady movement with controlled fluctuations",
            3: "Medium Volatility - Normal trading conditions with moderate price action",
            4: "High Volatility - Active markets with significant price movements",
            5: "Very High Volatility - Extreme movements, often during major events"
        }

    def get_intraday_regime_patterns(self):
        """Get hourly volatility patterns for each regime (aggregated from 5-minute windows to hourly)"""
        logger.info(f"Getting intraday patterns. Volatility regimes: {bool(self.volatility_regimes)}")
        if self.volatility_regimes:
            logger.info(f"Profiles available: {len(self.volatility_regimes.get('profiles', []))}")

        if not self.volatility_regimes or not self.volatility_regimes.get('profiles'):
            return {}

        # Get all 5-minute profiles and aggregate them into hourly data
        all_profiles = self.volatility_regimes.get('profiles', [])
        current_date = datetime.now(MARKET_TIMEZONE).strftime('%Y-%m-%d')

        # Group 5-minute profiles by date, hour, and regime for aggregation
        hourly_regime_data = {}  # {date: {hour: {regime: [volatility_values]}}}

        logger.info(f"Aggregating {len(all_profiles)} 5-minute profiles into hourly regime patterns")

        for profile in all_profiles:
            # Parse date and time from profile.date (format: "2025-07-07_03:05")
            if '_' not in profile.date:
                continue

            date_part, time_part = profile.date.split('_')

            # Skip current day (incomplete trading data)
            if date_part == current_date:
                continue

            try:
                hour = int(time_part.split(':')[0])
                regime = profile.regime_id if profile.regime_id is not None else 0

                # Initialize nested structure
                if date_part not in hourly_regime_data:
                    hourly_regime_data[date_part] = {}
                if hour not in hourly_regime_data[date_part]:
                    hourly_regime_data[date_part][hour] = {}
                if regime not in hourly_regime_data[date_part][hour]:
                    hourly_regime_data[date_part][hour][regime] = []

                # Calculate total volatility for this 5-minute window (sum of 24-hour profile)
                if hasattr(profile, 'hourly_volatility') and isinstance(profile.hourly_volatility, list):
                    total_volatility = sum(profile.hourly_volatility)
                    hourly_regime_data[date_part][hour][regime].append(total_volatility)

            except (ValueError, IndexError) as e:
                logger.warning(f"Error parsing profile date {profile.date}: {e}")
                continue

        # Intraday patterns functionality removed - return empty dict
        logger.info("Intraday patterns functionality has been removed")
        return {}

    def get_regime_transitions(self):
        """Get regime transition data over time (current day only)"""
        logger.info(f"Getting regime transitions. Volatility regimes: {bool(self.volatility_regimes)}")
        if self.volatility_regimes:
            logger.info(f"Profiles available: {len(self.volatility_regimes.get('profiles', []))}")

        if not self.volatility_regimes or not self.volatility_regimes.get('profiles'):
            return []

        # Filter to current day only
        current_date = datetime.now(MARKET_TIMEZONE).strftime('%Y-%m-%d')
        current_day_profiles = []

        logger.info(f"Looking for current date: {current_date}")
        for profile in self.volatility_regimes.get('profiles', []):
            # Extract date from M5 timestamp format (YYYY-MM-DD_HH:MM)
            profile_date = profile.date.split('_')[0] if '_' in profile.date else profile.date
            if profile_date == current_date:
                current_day_profiles.append(profile)

        logger.info(f"Found {len(current_day_profiles)} profiles for current day")

        # If no current day data, use most recent day
        if not current_day_profiles:
            # Get the most recent date
            all_dates = set()
            for profile in self.volatility_regimes.get('profiles', []):
                profile_date = profile.date.split('_')[0] if '_' in profile.date else profile.date
                all_dates.add(profile_date)

            if all_dates:
                most_recent_date = max(all_dates)
                for profile in self.volatility_regimes.get('profiles', []):
                    profile_date = profile.date.split('_')[0] if '_' in profile.date else profile.date
                    if profile_date == most_recent_date:
                        current_day_profiles.append(profile)

        # Sort current day profiles by date to track transitions
        profiles = sorted(current_day_profiles, key=lambda x: x.date)

        transitions = []
        prev_regime = None

        for profile in profiles:
            # Access attributes directly, not as dictionary keys
            current_regime = profile.regime_id if profile.regime_id is not None else 0
            date = profile.date

            if prev_regime is not None and prev_regime != current_regime:
                transitions.append({
                    'date': date,
                    'from_regime': prev_regime,
                    'to_regime': current_regime,
                    'symbol': profile.symbol
                })

            prev_regime = current_regime

        logger.info(f"Current day regime transitions: {len(transitions)} transitions from {len(current_day_profiles)} profiles")
        return transitions

    def shutdown(self):
        """Shutdown the state manager and cleanup resources"""
        try:
            # Stop alert system
            if self.enable_alerts and self.alert_system:
                self.alert_system.stop()
                logger.info("Alert system stopped")

            # Close database connection
            if self.use_database and self.db_manager:
                self.db_manager.disconnect()
                logger.info("Database connection closed")

            logger.info("StateManager shutdown complete")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

    def __del__(self):
        """Destructor to ensure cleanup"""
        try:
            self.shutdown()
        except:
            pass
