# Correlation Clustering Explained for MPT Portfolio Construction

## Overview

This document explains how the Dynamic FX Clustering Application calculates correlations and what they mean for Modern Portfolio Theory (MPT) applications.

## How Correlation is Calculated

### 1. Log Returns Calculation

The system calculates **logarithmic returns** for each currency pair:

```
log_return(t) = ln(Price(t) / Price(t-1))
```

**Key Point**: Log returns represent the **percentage change** in the currency pair's value, regardless of which currency is base or quote.

### 2. Correlation Matrix Computation

The correlation coefficient between two currency pairs is calculated using the **Pearson correlation**:

```
correlation(X,Y) = Σ[(X_i - X̄)(Y_i - Ȳ)] / √[Σ(X_i - X̄)² × Σ(Y_i - Ȳ)²]
```

Where X and Y are the log return series for two currency pairs.

## Understanding Correlation Values

### Positive Correlation (0 < r ≤ 1)
- **Meaning**: The currency pairs tend to move in the **same direction**
- **Example**: EURCHF and CHFJPY with correlation 0.8
  - When EUR strengthens against CHF, CHF typically weakens against JPY
  - Both pairs move in the same directional trend

### Negative Correlation (-1 ≤ r < 0)
- **Meaning**: The currency pairs tend to move in **opposite directions**
- **Example**: EURUSD and USDCHF typically have negative correlation
  - When EUR strengthens against USD, USD typically weakens against CHF
  - The pairs move in opposite directions

### Zero Correlation (r ≈ 0)
- **Meaning**: No linear relationship between the movements
- **Example**: Independent currency pair movements

## Base/Quote Currency Impact

### Important Clarification

**The correlation value already accounts for the base/quote relationship automatically.**

Let's examine your example:
- **EURCHF**: EUR is base, CHF is quote
- **CHFJPY**: CHF is base, JPY is quote
- **Correlation**: 0.8 (positive)

### What This Means:

1. **EURCHF rises** (EUR strengthens vs CHF) → CHF weakens
2. **CHFJPY falls** (CHF weakens vs JPY) → CHF weakens
3. **Result**: Both log returns move in the same direction (positive correlation)

The correlation captures the **net effect** of currency movements, not just the pair directions.

## Implications for MPT Portfolio Construction

### 1. Diversification Benefits

```python
# High positive correlation (r > 0.7)
# - Limited diversification benefit
# - Similar risk-return profiles
cluster_1 = ["EURCHF", "CHFJPY", "EURJPY"]  # CHF weakness cluster

# Low/negative correlation (r < 0.3)
# - Strong diversification benefit
# - Different risk-return profiles
diversified_pairs = ["EURUSD", "USDJPY", "GBPAUD"]
```

### 2. Risk Calculation

For a portfolio with weights w₁, w₂ and correlations ρ₁₂:

```
Portfolio_Variance = w₁²σ₁² + w₂²σ₂² + 2w₁w₂σ₁σ₂ρ₁₂
```

**High correlation** → Higher portfolio risk
**Low correlation** → Lower portfolio risk (diversification benefit)

### 3. Cluster-Based Portfolio Construction

#### Strategy 1: Intra-Cluster Concentration
```python
# Select one representative from each cluster
portfolio = {
    "cluster_1_rep": "EURCHF",    # CHF weakness theme
    "cluster_2_rep": "GBPUSD",    # USD strength theme  
    "cluster_3_rep": "AUDJPY",    # Risk-on theme
    # ... etc
}
```

#### Strategy 2: Inter-Cluster Diversification
```python
# Avoid pairs from the same cluster
def build_diversified_portfolio(clusters, max_pairs_per_cluster=1):
    portfolio = []
    for cluster in clusters:
        # Select best risk-adjusted return from each cluster
        best_pair = select_best_sharpe_ratio(cluster)
        portfolio.append(best_pair)
    return portfolio
```

### 4. Dynamic Rebalancing

The clustering updates every 30 seconds, allowing for:

```python
# Monitor cluster stability
if cluster_stability < threshold:
    # Rebalance portfolio
    new_weights = optimize_portfolio(current_clusters)
    rebalance_positions(new_weights)
```

## Practical Example

### Scenario: EURCHF and CHFJPY (correlation = 0.8)

**Market Event**: ECB announces dovish policy

1. **EURCHF**: EUR weakens → EURCHF falls → negative log return
2. **CHFJPY**: CHF strengthens → CHFJPY rises → positive log return
3. **Correlation**: Both respond to CHF strength, but in opposite price directions
4. **Log Returns**: Move in same direction (both reflect CHF strength)

### MPT Application:

```python
# These pairs provide limited diversification
weights = {
    "EURCHF": 0.3,
    "CHFJPY": 0.7
}

# Portfolio risk will be higher due to positive correlation
# Better to combine with uncorrelated pairs:
diversified_weights = {
    "EURCHF": 0.2,
    "CHFJPY": 0.2, 
    "GBPAUD": 0.3,  # Different cluster
    "USDCAD": 0.3   # Different cluster
}
```

## Key Takeaways for MPT

1. **Correlation values are ready-to-use** for MPT calculations
2. **High correlation** (>0.7) = limited diversification benefit
3. **Low correlation** (<0.3) = strong diversification benefit
4. **Cluster membership** indicates similar market drivers
5. **Dynamic clustering** enables adaptive portfolio optimization

## Code Integration Example

```python
# Get current clustering results
correlation_matrix = clustering_engine.get_correlation_matrix()
cluster_assignments = clustering_engine.get_cluster_assignments()

# Build MPT-optimized portfolio
def build_mpt_portfolio(correlation_matrix, expected_returns, risk_tolerance):
    # Use correlation matrix directly in MPT optimization
    portfolio_weights = optimize_portfolio(
        correlation_matrix=correlation_matrix,
        expected_returns=expected_returns,
        risk_tolerance=risk_tolerance
    )
    return portfolio_weights

# Monitor and rebalance
def monitor_portfolio(current_weights, new_correlation_matrix):
    if correlation_structure_changed(new_correlation_matrix):
        new_weights = reoptimize_portfolio(new_correlation_matrix)
        return new_weights
    return current_weights
```

This correlation clustering provides a solid foundation for MPT portfolio construction with real-time market regime awareness.
