"""
Unit tests for StateManager
"""

import pytest
import os
import json
import tempfile
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

from clustering.state_manager import StateManager
from clustering.advanced_analytics import AdvancedAnalytics
from clustering.alert_system import AlertSystem


@pytest.mark.unit
class TestStateManager:
    """Test cases for StateManager class"""
    
    def test_initialization_default(self, temp_data_dir):
        """Test default initialization"""
        state_manager = StateManager(persistence_dir=temp_data_dir)
        
        assert state_manager.persistence_dir == temp_data_dir
        assert state_manager.current_state is None
        assert state_manager.historical_states == []
        assert state_manager.performance_metrics == {}
    
    def test_initialization_with_analytics(self, temp_data_dir):
        """Test initialization with advanced analytics enabled"""
        state_manager = StateManager(
            persistence_dir=temp_data_dir,
            enable_advanced_analytics=True,
            enable_alerts=True
        )
        
        assert state_manager.advanced_analytics is not None
        assert state_manager.alert_system is not None
    
    def test_update_state(self, mock_state_manager, sample_clustering_result):
        """Test state update functionality"""
        mock_state_manager.update_state(sample_clustering_result)
        
        assert mock_state_manager.current_state == sample_clustering_result
        assert len(mock_state_manager.historical_states) == 1
    
    def test_update_state_with_event_detection(self, mock_state_manager, sample_clustering_result):
        """Test state update with event detection"""
        # Setup previous state
        previous_state = {
            'cluster_assignments': [0, 0, 0, 1, 1, 1, 2, 2, 2, 2],
            'silhouette_score': 0.6,
            'timestamp': datetime.now() - timedelta(minutes=5)
        }
        mock_state_manager.current_state = previous_state
        
        mock_state_manager.update_state(sample_clustering_result)
        
        # Should detect significant change
        assert mock_state_manager.current_state == sample_clustering_result
    
    def test_get_current_state(self, mock_state_manager, sample_clustering_result):
        """Test getting current state"""
        mock_state_manager.current_state = sample_clustering_result
        
        state = mock_state_manager.get_current_state()
        
        assert state == sample_clustering_result
    
    def test_get_current_state_empty(self, mock_state_manager):
        """Test getting current state when empty"""
        state = mock_state_manager.get_current_state()
        
        assert state is None
    
    def test_get_historical_states(self, mock_state_manager):
        """Test getting historical states"""
        # Add some historical states
        states = [
            {'timestamp': datetime.now() - timedelta(hours=1), 'clusters': 3},
            {'timestamp': datetime.now() - timedelta(hours=2), 'clusters': 4},
            {'timestamp': datetime.now() - timedelta(hours=25), 'clusters': 5}  # Older than 24h
        ]
        mock_state_manager.historical_states = states
        
        recent_states = mock_state_manager.get_historical_states(hours_back=24)
        
        assert len(recent_states) == 2  # Only states within 24 hours
    
    def test_get_performance_metrics(self, mock_state_manager):
        """Test getting performance metrics"""
        mock_metrics = {
            'avg_clustering_time': 1.5,
            'avg_silhouette_score': 0.7,
            'total_analyses': 100
        }
        mock_state_manager.performance_metrics = mock_metrics
        
        metrics = mock_state_manager.get_performance_metrics()
        
        assert metrics == mock_metrics
    
    def test_update_performance_metrics(self, mock_state_manager):
        """Test updating performance metrics"""
        new_metrics = {
            'clustering_time': 2.0,
            'silhouette_score': 0.8,
            'data_quality': 0.9
        }
        
        mock_state_manager.update_performance_metrics(new_metrics)
        
        assert 'clustering_time' in mock_state_manager.performance_metrics
        assert mock_state_manager.performance_metrics['clustering_time'] == 2.0
    
    def test_persistence_save_state(self, temp_data_dir):
        """Test saving state to disk"""
        state_manager = StateManager(persistence_dir=temp_data_dir)
        test_state = {'clusters': 3, 'quality': 0.8}
        
        state_manager.current_state = test_state
        state_manager._save_state()
        
        # Check file was created
        state_file = os.path.join(temp_data_dir, 'current_state.json')
        assert os.path.exists(state_file)
        
        # Check content
        with open(state_file, 'r') as f:
            saved_state = json.load(f)
        assert saved_state['clusters'] == 3
        assert saved_state['quality'] == 0.8
    
    def test_persistence_load_state(self, temp_data_dir):
        """Test loading state from disk"""
        # Create test state file
        test_state = {'clusters': 4, 'quality': 0.9}
        state_file = os.path.join(temp_data_dir, 'current_state.json')
        
        with open(state_file, 'w') as f:
            json.dump(test_state, f)
        
        state_manager = StateManager(persistence_dir=temp_data_dir)
        state_manager._load_state()
        
        assert state_manager.current_state['clusters'] == 4
        assert state_manager.current_state['quality'] == 0.9
    
    def test_persistence_save_historical_states(self, temp_data_dir):
        """Test saving historical states to disk"""
        state_manager = StateManager(persistence_dir=temp_data_dir)
        test_states = [
            {'timestamp': '2025-01-01T12:00:00', 'clusters': 3},
            {'timestamp': '2025-01-01T13:00:00', 'clusters': 4}
        ]
        
        state_manager.historical_states = test_states
        state_manager._save_historical_states()
        
        # Check file was created
        history_file = os.path.join(temp_data_dir, 'historical_states.json')
        assert os.path.exists(history_file)
    
    def test_persistence_load_historical_states(self, temp_data_dir):
        """Test loading historical states from disk"""
        # Create test history file
        test_states = [
            {'timestamp': '2025-01-01T12:00:00', 'clusters': 3},
            {'timestamp': '2025-01-01T13:00:00', 'clusters': 4}
        ]
        history_file = os.path.join(temp_data_dir, 'historical_states.json')
        
        with open(history_file, 'w') as f:
            json.dump(test_states, f)
        
        state_manager = StateManager(persistence_dir=temp_data_dir)
        state_manager._load_historical_states()
        
        assert len(state_manager.historical_states) == 2
        assert state_manager.historical_states[0]['clusters'] == 3
    
    def test_cleanup_old_states(self, mock_state_manager):
        """Test cleanup of old historical states"""
        # Add states with various ages
        now = datetime.now()
        states = [
            {'timestamp': now - timedelta(days=1), 'clusters': 3},
            {'timestamp': now - timedelta(days=5), 'clusters': 4},
            {'timestamp': now - timedelta(days=10), 'clusters': 5},
            {'timestamp': now - timedelta(days=35), 'clusters': 6}  # Older than 30 days
        ]
        mock_state_manager.historical_states = states
        
        mock_state_manager._cleanup_old_states(max_age_days=30)
        
        # Should keep only states within 30 days
        assert len(mock_state_manager.historical_states) == 3
    
    def test_get_state_statistics(self, mock_state_manager):
        """Test getting state statistics"""
        # Add some historical states
        states = [
            {'silhouette_score': 0.7, 'n_clusters': 3},
            {'silhouette_score': 0.8, 'n_clusters': 4},
            {'silhouette_score': 0.6, 'n_clusters': 3}
        ]
        mock_state_manager.historical_states = states
        
        stats = mock_state_manager.get_state_statistics()
        
        assert 'avg_silhouette_score' in stats
        assert 'avg_clusters' in stats
        assert 'total_states' in stats
        assert stats['total_states'] == 3
    
    def test_detect_regime_change(self, mock_state_manager):
        """Test regime change detection"""
        # Setup current and previous states
        current_state = {'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0]}
        previous_state = {'cluster_assignments': [0, 0, 0, 1, 1, 1, 2, 2, 2, 2]}
        
        mock_state_manager.current_state = previous_state
        
        change_detected = mock_state_manager._detect_regime_change(current_state)
        
        assert change_detected is True
    
    def test_detect_regime_change_no_change(self, mock_state_manager):
        """Test regime change detection with no change"""
        # Setup identical states
        state = {'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0]}
        
        mock_state_manager.current_state = state
        
        change_detected = mock_state_manager._detect_regime_change(state)
        
        assert change_detected is False
    
    def test_advanced_analytics_integration(self, temp_data_dir):
        """Test integration with advanced analytics"""
        state_manager = StateManager(
            persistence_dir=temp_data_dir,
            enable_advanced_analytics=True
        )
        
        assert state_manager.advanced_analytics is not None
        assert hasattr(state_manager, 'get_advanced_analytics_summary')
    
    def test_alert_system_integration(self, temp_data_dir):
        """Test integration with alert system"""
        state_manager = StateManager(
            persistence_dir=temp_data_dir,
            enable_alerts=True
        )
        
        assert state_manager.alert_system is not None
        assert hasattr(state_manager, 'get_alert_history')
    
    def test_cleanup(self, mock_state_manager):
        """Test cleanup functionality"""
        mock_state_manager.cleanup()
        
        # Should save current state before cleanup
        assert mock_state_manager.current_state is not None or True  # Allow for empty state
