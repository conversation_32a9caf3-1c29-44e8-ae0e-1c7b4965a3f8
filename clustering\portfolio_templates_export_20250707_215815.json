{"metadata": {"export_timestamp": "2025-07-07T21:58:15.557229", "source_project": "Dynamic FX Clustering", "target_project": "MPT Portfolio Optimization", "template_count": 5, "symbols": ["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", "USDCAD", "NZDUSD", "EURGBP", "EURJPY", "GBPJPY"]}, "templates": [{"id": "conservative_1751914695", "name": "Conservative Risk-Parity Portfolio", "strategy": "conservative", "description": "Low-risk portfolio using risk parity principles with equal risk contribution from all assets", "allocation": {"weights": {"EURUSD": 0.08834607148917553, "GBPUSD": 0.16411519097398394, "USDJPY": 0.11804161763883388, "USDCHF": 0.12239759730407922, "AUDUSD": 0.15356943039737234, "USDCAD": 0.050202569610975425, "NZDUSD": 0.08717931868226082, "EURGBP": 0.05987314176993313, "EURJPY": 0.05952223817393438, "GBPJPY": 0.09675282395945134}, "weight_sum": 1.0, "non_zero_positions": 10}, "risk_metrics": {"expected_return": 0.03, "expected_volatility": 0.007551156958208423, "sharpe_ratio": 0.5, "max_drawdown_estimate": 0.018877892395521057, "diversification_ratio": 1.2, "risk_concentration": 0.10001935426499868}, "risk_budgets": {"EURUSD": 0.10001935426499868, "GBPUSD": 0.09999253941362174, "USDJPY": 0.09997076563101637, "USDCHF": 0.10000485390645172, "AUDUSD": 0.09999733840590466, "USDCAD": 0.10000083819441574, "NZDUSD": 0.10000644193044007, "EURGBP": 0.0999994302856244, "EURJPY": 0.09999949675373172, "GBPJPY": 0.10000894121379486}, "rebalancing": {"frequency": "quarterly", "thresholds": {"volatility": 0.5, "correlation": 0.3, "drift": 0.05}, "minimum_interval_days": 60, "emergency_triggers": ["volatility_spike", "correlation_breakdown"]}, "constraints": {"cluster_constraints": null, "position_limits": {"min_weight": 0.050202569610975425, "max_weight": 0.16411519097398394}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles"], "notes": "Focus on capital preservation with steady returns. Suitable for risk-averse investors.", "estimated_turnover": 0.1}, "creation_info": {"timestamp": "2025-07-07T21:58:15.534414", "optimization_success": true}}, {"id": "balanced_1751914695", "name": "Balanced Cluster-Diversified Portfolio", "strategy": "balanced", "description": "Balanced portfolio using cluster analysis for optimal diversification across currency groups", "allocation": {"weights": {"EURUSD": 0.08333333333333333, "AUDUSD": 0.08333333333333333, "EURJPY": 0.08333333333333333, "GBPUSD": 0.08333333333333333, "USDCAD": 0.08333333333333333, "GBPJPY": 0.08333333333333333, "USDJPY": 0.125, "NZDUSD": 0.125, "USDCHF": 0.125, "EURGBP": 0.125}, "weight_sum": 1.0, "non_zero_positions": 10}, "risk_metrics": {"expected_return": 0.03123267388542995, "expected_volatility": 0.008896759930097673, "sharpe_ratio": 1.2625578270837563, "max_drawdown_estimate": 0.017793519860195346, "diversification_ratio": 1.5, "risk_concentration": 0.3}, "risk_budgets": {"EURUSD": 0.08333333333333333, "GBPUSD": 0.08333333333333333, "USDJPY": 0.125, "USDCHF": 0.125, "AUDUSD": 0.08333333333333333, "USDCAD": 0.08333333333333333, "NZDUSD": 0.125, "EURGBP": 0.125, "EURJPY": 0.08333333333333333, "GBPJPY": 0.08333333333333333}, "rebalancing": {"frequency": "monthly", "thresholds": {"volatility": 0.4, "correlation": 0.25, "drift": 0.08}, "minimum_interval_days": 30, "emergency_triggers": ["cluster_change", "volatility_regime_shift"]}, "constraints": {"cluster_constraints": {"0": 0.25, "1": 0.25, "2": 0.25, "3": 0.25}, "position_limits": {"min_weight": 0.08333333333333333, "max_weight": 0.125}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles", "cluster_assignments"], "notes": "Balances risk and return using cluster-based diversification. Suitable for moderate risk tolerance.", "estimated_turnover": 0.15}, "creation_info": {"timestamp": "2025-07-07T21:58:15.534492", "optimization_success": true}}, {"id": "aggressive_1751914695", "name": "Aggressive Maximum-<PERSON>", "strategy": "aggressive", "description": "High-return seeking portfolio optimized for maximum risk-adjusted returns", "allocation": {"weights": {"EURUSD": 0.010000000000000057, "GBPUSD": 0.010000000000000429, "USDJPY": 0.29720026398475485, "USDCHF": 0.010000000000000002, "AUDUSD": 0.010000000000000004, "USDCAD": 0.131000819769967, "NZDUSD": 0.01, "EURGBP": 0.19820932419783524, "EURJPY": 0.2516054292072527, "GBPJPY": 0.07198416284019}, "weight_sum": 1.0000000000000002, "non_zero_positions": 10}, "risk_metrics": {"expected_return": 0.03953259295951047, "expected_volatility": 0.011236788813662555, "sharpe_ratio": 1.7382717859537624, "max_drawdown_estimate": 0.03371036644098767, "diversification_ratio": 1.8, "risk_concentration": 0.5}, "risk_budgets": {"EURUSD": 0.010000000000000057, "GBPUSD": 0.010000000000000429, "USDJPY": 0.29720026398475485, "USDCHF": 0.010000000000000002, "AUDUSD": 0.010000000000000004, "USDCAD": 0.131000819769967, "NZDUSD": 0.01, "EURGBP": 0.19820932419783524, "EURJPY": 0.2516054292072527, "GBPJPY": 0.07198416284019}, "rebalancing": {"frequency": "monthly", "thresholds": {"volatility": 0.3, "correlation": 0.2, "drift": 0.12}, "minimum_interval_days": 15, "emergency_triggers": ["sharp_drawdown", "correlation_breakdown", "volatility_spike"]}, "constraints": {"cluster_constraints": null, "position_limits": {"min_weight": 0.01, "max_weight": 0.29720026398475485}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles", "expected_returns"], "notes": "Targets maximum Sharpe ratio. Suitable for high risk tolerance investors seeking optimal risk-adjusted returns.", "estimated_turnover": 0.25}, "creation_info": {"timestamp": "2025-07-07T21:58:15.542101", "optimization_success": true}}, {"id": "regime_adaptive_1751914695", "name": "Regime-Adaptive Dynamic Portfolio", "strategy": "regime_adaptive", "description": "Dynamic portfolio that adapts to market regimes. Currently in high volatility regime - defensive positioning", "allocation": {"weights": {"EURUSD": 0.16666666666666666, "GBPUSD": 0.16666666666666666, "USDJPY": 0.16666666666666666, "USDCHF": 0.16666666666666666, "AUDUSD": 0.16666666666666666, "USDCAD": 0.0, "NZDUSD": 0.0, "EURGBP": 0.0, "EURJPY": 0.0, "GBPJPY": 0.16666666666666666}, "weight_sum": 1.0, "non_zero_positions": 6}, "risk_metrics": {"expected_return": 0.023354701472843572, "expected_volatility": 0.006941613303684444, "sharpe_ratio": 0.4832740353114996, "max_drawdown_estimate": 0.015271549268105778, "diversification_ratio": 1.6, "risk_concentration": 0.35}, "risk_budgets": {"EURUSD": 0.16666666666666666, "GBPUSD": 0.16666666666666666, "USDJPY": 0.16666666666666666, "USDCHF": 0.16666666666666666, "AUDUSD": 0.16666666666666666, "USDCAD": 0.0, "NZDUSD": 0.0, "EURGBP": 0.0, "EURJPY": 0.0, "GBPJPY": 0.16666666666666666}, "rebalancing": {"frequency": "regime_based", "thresholds": {"volatility": 0.2, "correlation": 0.15, "drift": 0.06}, "minimum_interval_days": 7, "emergency_triggers": ["volatility_regime_shift", "correlation_regime_shift", "cluster_instability"]}, "constraints": {"cluster_constraints": null, "position_limits": {"min_weight": 0.0, "max_weight": 0.16666666666666666}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles", "cluster_assignments", "regime_classification"], "notes": "Adapts allocation based on volatility and correlation regimes. Requires active monitoring and frequent rebalancing.", "estimated_turnover": 0.3}, "creation_info": {"timestamp": "2025-07-07T21:58:15.542206", "optimization_success": true}}, {"id": "equal_risk_contribution_1751914695", "name": "Equal Risk Contribution (ERC) Portfolio", "strategy": "equal_risk_contribution", "description": "Portfolio where each asset contributes equally to total portfolio risk", "allocation": {"weights": {"EURUSD": 0.08834607148917553, "GBPUSD": 0.16411519097398394, "USDJPY": 0.11804161763883388, "USDCHF": 0.12239759730407922, "AUDUSD": 0.15356943039737234, "USDCAD": 0.050202569610975425, "NZDUSD": 0.08717931868226082, "EURGBP": 0.05987314176993313, "EURJPY": 0.05952223817393438, "GBPJPY": 0.09675282395945134}, "weight_sum": 1.0, "non_zero_positions": 10}, "risk_metrics": {"expected_return": 0.04, "expected_volatility": 0.007551156958208423, "sharpe_ratio": 2.6486007522673947, "max_drawdown_estimate": 0.015102313916416846, "diversification_ratio": 1.4, "risk_concentration": 0.10001935426499868}, "risk_budgets": {"EURUSD": 0.10001935426499868, "GBPUSD": 0.09999253941362174, "USDJPY": 0.09997076563101637, "USDCHF": 0.10000485390645172, "AUDUSD": 0.09999733840590466, "USDCAD": 0.10000083819441574, "NZDUSD": 0.10000644193044007, "EURGBP": 0.0999994302856244, "EURJPY": 0.09999949675373172, "GBPJPY": 0.10000894121379486}, "rebalancing": {"frequency": "quarterly", "thresholds": {"volatility": 0.4, "correlation": 0.3, "drift": 0.07}, "minimum_interval_days": 45, "emergency_triggers": ["risk_budget_breach", "volatility_spike"]}, "constraints": {"cluster_constraints": null, "position_limits": {"min_weight": 0.050202569610975425, "max_weight": 0.16411519097398394}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles"], "notes": "True risk parity approach ensuring equal risk contribution from all assets. Good for diversification-focused strategies.", "estimated_turnover": 0.12}, "creation_info": {"timestamp": "2025-07-07T21:58:15.551367", "optimization_success": true}}]}