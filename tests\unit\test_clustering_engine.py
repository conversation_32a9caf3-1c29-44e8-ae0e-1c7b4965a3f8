"""
Unit tests for ClusteringEngine
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

from clustering.clustering_engine import ClusteringEngine
from clustering.data_manager import ClusteringDataManager
from clustering.state_manager import StateManager


@pytest.mark.unit
class TestClusteringEngine:
    """Test cases for ClusteringEngine class"""
    
    def test_initialization_default(self):
        """Test default initialization"""
        engine = ClusteringEngine()
        
        assert engine.symbols is not None
        assert len(engine.symbols) > 0
        assert engine.event_threshold == 0.8
        assert engine.min_data_quality == 0.7
        assert engine.state_manager is not None
        assert engine.data_manager is not None
    
    def test_initialization_custom(self, test_symbols, temp_data_dir):
        """Test custom initialization"""
        engine = ClusteringEngine(
            symbols=test_symbols,
            event_threshold=0.6,
            min_data_quality=0.5,
            persistence_dir=temp_data_dir
        )
        
        assert engine.symbols == test_symbols
        assert engine.event_threshold == 0.6
        assert engine.min_data_quality == 0.5
    
    def test_connect_to_data_sources(self, mock_clustering_engine):
        """Test data source connection"""
        # Mock successful connection
        mock_clustering_engine.data_manager.connect.return_value = True
        
        result = mock_clustering_engine.connect_to_data_sources()
        
        assert result is True
        mock_clustering_engine.data_manager.connect.assert_called_once()
    
    def test_connect_to_data_sources_failure(self, mock_clustering_engine):
        """Test data source connection failure"""
        # Mock failed connection
        mock_clustering_engine.data_manager.connect.return_value = False
        
        result = mock_clustering_engine.connect_to_data_sources()
        
        assert result is False
    
    @patch('clustering.clustering_engine.cluster_core')
    def test_run_clustering_analysis_success(self, mock_cluster_core, mock_clustering_engine, sample_market_data):
        """Test successful clustering analysis"""
        # Setup mocks
        mock_clustering_engine.data_manager.fetch_data.return_value = sample_market_data['prices']
        mock_cluster_core.cluster_currencies.return_value = {
            'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
            'silhouette_score': 0.75,
            'inertia': 0.25,
            'n_clusters': 3
        }
        
        result = mock_clustering_engine.run_clustering_analysis()
        
        assert result is not None
        assert 'cluster_assignments' in result
        assert 'silhouette_score' in result
        assert result['silhouette_score'] == 0.75
    
    def test_run_clustering_analysis_no_data(self, mock_clustering_engine):
        """Test clustering analysis with no data"""
        # Mock no data returned
        mock_clustering_engine.data_manager.fetch_data.return_value = None
        
        result = mock_clustering_engine.run_clustering_analysis()
        
        assert result is None
    
    def test_run_clustering_analysis_insufficient_quality(self, mock_clustering_engine, sample_market_data):
        """Test clustering analysis with insufficient data quality"""
        # Setup mock with low quality data
        low_quality_data = sample_market_data['prices'][:5]  # Very small dataset
        mock_clustering_engine.data_manager.fetch_data.return_value = low_quality_data
        
        result = mock_clustering_engine.run_clustering_analysis()
        
        assert result is None
    
    def test_calculate_data_quality(self, mock_clustering_engine):
        """Test data quality calculation"""
        # Test with good quality data
        good_data = np.random.rand(100, 10)  # 100 periods, 10 symbols
        quality = mock_clustering_engine._calculate_data_quality(good_data)
        assert 0.0 <= quality <= 1.0
        assert quality > 0.5  # Should be reasonably high
        
        # Test with poor quality data
        poor_data = np.random.rand(10, 10)  # Only 10 periods
        quality = mock_clustering_engine._calculate_data_quality(poor_data)
        assert quality < 0.5  # Should be low
    
    def test_detect_market_events(self, mock_clustering_engine, sample_clustering_result):
        """Test market event detection"""
        # Test with significant change
        previous_result = {
            'cluster_assignments': [0, 0, 0, 1, 1, 1, 2, 2, 2, 2],
            'silhouette_score': 0.6
        }
        mock_clustering_engine.state_manager.get_current_state.return_value = previous_result
        
        event = mock_clustering_engine._detect_market_events(sample_clustering_result)
        
        assert event is not None
        assert 'event_type' in event
        assert 'confidence' in event
    
    def test_detect_market_events_no_change(self, mock_clustering_engine, sample_clustering_result):
        """Test market event detection with no significant change"""
        # Test with minimal change
        mock_clustering_engine.state_manager.get_current_state.return_value = sample_clustering_result
        
        event = mock_clustering_engine._detect_market_events(sample_clustering_result)
        
        assert event is None
    
    def test_get_current_state(self, mock_clustering_engine, sample_clustering_result):
        """Test getting current clustering state"""
        mock_clustering_engine.state_manager.get_current_state.return_value = sample_clustering_result
        
        state = mock_clustering_engine.get_current_state()
        
        assert state == sample_clustering_result
        mock_clustering_engine.state_manager.get_current_state.assert_called_once()
    
    def test_get_historical_states(self, mock_clustering_engine):
        """Test getting historical clustering states"""
        mock_states = [
            {'timestamp': datetime.now() - timedelta(hours=1), 'clusters': 3},
            {'timestamp': datetime.now() - timedelta(hours=2), 'clusters': 4}
        ]
        mock_clustering_engine.state_manager.get_historical_states.return_value = mock_states
        
        states = mock_clustering_engine.get_historical_states(hours_back=24)
        
        assert states == mock_states
        mock_clustering_engine.state_manager.get_historical_states.assert_called_once_with(hours_back=24)
    
    def test_get_performance_metrics(self, mock_clustering_engine):
        """Test getting performance metrics"""
        mock_metrics = {
            'avg_clustering_time': 1.5,
            'avg_silhouette_score': 0.7,
            'total_analyses': 100
        }
        mock_clustering_engine.state_manager.get_performance_metrics.return_value = mock_metrics
        
        metrics = mock_clustering_engine.get_performance_metrics()
        
        assert metrics == mock_metrics
        mock_clustering_engine.state_manager.get_performance_metrics.assert_called_once()
    
    def test_cleanup(self, mock_clustering_engine):
        """Test cleanup functionality"""
        mock_clustering_engine.cleanup()
        
        # Verify cleanup methods are called
        mock_clustering_engine.data_manager.disconnect.assert_called_once()
        mock_clustering_engine.state_manager.cleanup.assert_called_once()
    
    def test_is_connected(self, mock_clustering_engine):
        """Test connection status check"""
        mock_clustering_engine.data_manager.is_connected.return_value = True
        
        assert mock_clustering_engine.is_connected() is True
        
        mock_clustering_engine.data_manager.is_connected.return_value = False
        assert mock_clustering_engine.is_connected() is False
    
    def test_get_symbols(self, mock_clustering_engine, test_symbols):
        """Test getting monitored symbols"""
        symbols = mock_clustering_engine.get_symbols()
        
        assert symbols == test_symbols
    
    def test_error_handling_data_fetch_failure(self, mock_clustering_engine):
        """Test error handling when data fetch fails"""
        mock_clustering_engine.data_manager.fetch_data.side_effect = Exception("Connection error")
        
        result = mock_clustering_engine.run_clustering_analysis()
        
        assert result is None
    
    @patch('clustering.clustering_engine.cluster_core')
    def test_error_handling_clustering_failure(self, mock_cluster_core, mock_clustering_engine, sample_market_data):
        """Test error handling when clustering fails"""
        mock_clustering_engine.data_manager.fetch_data.return_value = sample_market_data['prices']
        mock_cluster_core.cluster_currencies.side_effect = Exception("Clustering error")
        
        result = mock_clustering_engine.run_clustering_analysis()
        
        assert result is None
