"""
Advanced Analytics for Dynamic FX Clustering Application
======================================================

Provides advanced clustering validation metrics, regime classification,
and sophisticated event detection capabilities.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum
from sklearn.metrics import silhouette_score, davies_bouldin_score, calinski_harabasz_score
from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score

from config import MARKET_TIMEZONE

logger = logging.getLogger(__name__)


class RegimeType(Enum):
    """Market regime classifications"""
    UNIFIED = "unified"           # One big cluster - risk-on/calm periods
    BIFURCATED = "bifurcated"     # Two clusters - risk-off vs risk-on
    FRAGMENTED = "fragmented"     # Multiple small clusters - idiosyncratic moves
    OUTLIER_DRIVEN = "outlier_driven"  # One cluster plus outliers
    TRANSITIONAL = "transitional"  # Unstable clustering structure


@dataclass
class ClusteringMetrics:
    """Comprehensive clustering validation metrics"""
    timestamp: datetime
    silhouette_score: float
    davies_bouldin_score: float
    calinski_harabasz_score: float
    intra_cluster_distance: float
    inter_cluster_distance: float
    cluster_count: int
    largest_cluster_size: int
    smallest_cluster_size: int
    cluster_size_entropy: float
    stability_score: float


@dataclass
class RegimeClassification:
    """Market regime classification result"""
    timestamp: datetime
    regime_type: RegimeType
    confidence: float
    description: str
    cluster_distribution: Dict[int, int]
    dominant_cluster_ratio: float
    fragmentation_index: float


@dataclass
class AdvancedEvent:
    """Advanced event detection result"""
    timestamp: datetime
    event_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    affected_symbols: List[str]
    metrics: Dict[str, float]
    regime_change: Optional[RegimeClassification]


class AdvancedAnalytics:
    """
    Advanced analytics engine for clustering validation and regime detection
    
    Provides:
    - Comprehensive clustering validation metrics
    - Market regime classification
    - Advanced event detection
    - Trend analysis and forecasting
    """
    
    def __init__(self, history_length: int = 200):
        """
        Initialize advanced analytics engine
        
        Args:
            history_length: Number of historical metrics to maintain
        """
        self.history_length = history_length
        self.metrics_history: List[ClusteringMetrics] = []
        self.regime_history: List[RegimeClassification] = []
        self.event_history: List[AdvancedEvent] = []
        
    def calculate_clustering_metrics(
        self,
        features: np.ndarray,
        cluster_labels: np.ndarray,
        symbols: List[str],
        timestamp: datetime = None
    ) -> ClusteringMetrics:
        """
        Calculate comprehensive clustering validation metrics
        
        Args:
            features: Feature matrix used for clustering
            cluster_labels: Cluster assignments
            symbols: Currency pair symbols
            timestamp: Timestamp for metrics
            
        Returns:
            ClusteringMetrics object with all validation scores
        """
        if timestamp is None:
            timestamp = datetime.now(MARKET_TIMEZONE)
            
        try:
            # Basic validation metrics
            silhouette = silhouette_score(features, cluster_labels) if len(set(cluster_labels)) > 1 else 0.0
            davies_bouldin = davies_bouldin_score(features, cluster_labels) if len(set(cluster_labels)) > 1 else 0.0
            calinski_harabasz = calinski_harabasz_score(features, cluster_labels) if len(set(cluster_labels)) > 1 else 0.0
            
            # Cluster distribution analysis
            unique_labels, counts = np.unique(cluster_labels, return_counts=True)
            cluster_count = len(unique_labels)
            largest_cluster_size = np.max(counts)
            smallest_cluster_size = np.min(counts)
            
            # Cluster size entropy (measure of balance)
            cluster_probs = counts / len(cluster_labels)
            cluster_size_entropy = -np.sum(cluster_probs * np.log2(cluster_probs + 1e-10))
            
            # Distance analysis
            intra_cluster_dist, inter_cluster_dist = self._calculate_cluster_distances(features, cluster_labels)
            
            # Stability score (based on recent history)
            stability_score = self._calculate_stability_score(cluster_labels)
            
            metrics = ClusteringMetrics(
                timestamp=timestamp,
                silhouette_score=silhouette,
                davies_bouldin_score=davies_bouldin,
                calinski_harabasz_score=calinski_harabasz,
                intra_cluster_distance=intra_cluster_dist,
                inter_cluster_distance=inter_cluster_dist,
                cluster_count=cluster_count,
                largest_cluster_size=largest_cluster_size,
                smallest_cluster_size=smallest_cluster_size,
                cluster_size_entropy=cluster_size_entropy,
                stability_score=stability_score
            )
            
            # Store in history
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.history_length:
                self.metrics_history.pop(0)
                
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating clustering metrics: {e}")
            # Return default metrics
            return ClusteringMetrics(
                timestamp=timestamp,
                silhouette_score=0.0,
                davies_bouldin_score=0.0,
                calinski_harabasz_score=0.0,
                intra_cluster_distance=0.0,
                inter_cluster_distance=0.0,
                cluster_count=1,
                largest_cluster_size=len(cluster_labels),
                smallest_cluster_size=len(cluster_labels),
                cluster_size_entropy=0.0,
                stability_score=0.0
            )
    
    def classify_regime(
        self,
        cluster_labels: np.ndarray,
        symbols: List[str],
        correlation_matrix: np.ndarray = None,
        timestamp: datetime = None
    ) -> RegimeClassification:
        """
        Classify current market regime based on clustering structure
        
        Args:
            cluster_labels: Current cluster assignments
            symbols: Currency pair symbols
            correlation_matrix: Optional correlation matrix for additional analysis
            timestamp: Timestamp for classification
            
        Returns:
            RegimeClassification object
        """
        if timestamp is None:
            timestamp = datetime.now(MARKET_TIMEZONE)
            
        try:
            unique_labels, counts = np.unique(cluster_labels, return_counts=True)
            cluster_count = len(unique_labels)
            total_pairs = len(cluster_labels)
            
            # Calculate cluster distribution
            cluster_distribution = {int(label): int(count) for label, count in zip(unique_labels, counts)}
            dominant_cluster_ratio = np.max(counts) / total_pairs
            
            # Calculate fragmentation index
            fragmentation_index = 1.0 - dominant_cluster_ratio
            
            # Regime classification logic
            regime_type, confidence, description = self._classify_regime_type(
                cluster_count, dominant_cluster_ratio, fragmentation_index, correlation_matrix
            )
            
            classification = RegimeClassification(
                timestamp=timestamp,
                regime_type=regime_type,
                confidence=confidence,
                description=description,
                cluster_distribution=cluster_distribution,
                dominant_cluster_ratio=dominant_cluster_ratio,
                fragmentation_index=fragmentation_index
            )
            
            # Store in history
            self.regime_history.append(classification)
            if len(self.regime_history) > self.history_length:
                self.regime_history.pop(0)
                
            return classification
            
        except Exception as e:
            logger.error(f"Error classifying regime: {e}")
            return RegimeClassification(
                timestamp=timestamp,
                regime_type=RegimeType.TRANSITIONAL,
                confidence=0.0,
                description="Error in regime classification",
                cluster_distribution={0: len(cluster_labels)},
                dominant_cluster_ratio=1.0,
                fragmentation_index=0.0
            )
    
    def detect_advanced_events(
        self,
        current_metrics: ClusteringMetrics,
        current_regime: RegimeClassification,
        symbols: List[str]
    ) -> List[AdvancedEvent]:
        """
        Detect advanced market events based on clustering analysis
        
        Args:
            current_metrics: Current clustering metrics
            current_regime: Current regime classification
            symbols: Currency pair symbols
            
        Returns:
            List of detected events
        """
        events = []
        
        try:
            # Check for regime changes
            if len(self.regime_history) >= 2:
                prev_regime = self.regime_history[-2]
                if prev_regime.regime_type != current_regime.regime_type:
                    events.append(self._create_regime_change_event(prev_regime, current_regime, symbols))
            
            # Check for clustering quality degradation
            if len(self.metrics_history) >= 5:
                recent_silhouette = [m.silhouette_score for m in self.metrics_history[-5:]]
                if self._detect_trend_change(recent_silhouette, threshold=-0.2):
                    events.append(self._create_quality_degradation_event(current_metrics, symbols))
            
            # Check for cluster fragmentation
            if current_regime.fragmentation_index > 0.7:
                events.append(self._create_fragmentation_event(current_regime, symbols))
            
            # Check for sudden cluster count changes
            if len(self.metrics_history) >= 2:
                prev_count = self.metrics_history[-2].cluster_count
                current_count = current_metrics.cluster_count
                if abs(current_count - prev_count) >= 2:
                    events.append(self._create_cluster_count_change_event(prev_count, current_count, symbols))
            
            # Store events in history
            for event in events:
                self.event_history.append(event)
                if len(self.event_history) > self.history_length:
                    self.event_history.pop(0)
            
            return events
            
        except Exception as e:
            logger.error(f"Error detecting advanced events: {e}")
            return []
    
    def _calculate_cluster_distances(self, features: np.ndarray, cluster_labels: np.ndarray) -> Tuple[float, float]:
        """Calculate average intra-cluster and inter-cluster distances"""
        try:
            unique_labels = np.unique(cluster_labels)
            
            # Calculate intra-cluster distances
            intra_distances = []
            for label in unique_labels:
                cluster_mask = cluster_labels == label
                cluster_features = features[cluster_mask]
                if len(cluster_features) > 1:
                    # Calculate pairwise distances within cluster
                    from scipy.spatial.distance import pdist
                    distances = pdist(cluster_features)
                    intra_distances.extend(distances)
            
            avg_intra_distance = np.mean(intra_distances) if intra_distances else 0.0
            
            # Calculate inter-cluster distances (centroids)
            inter_distances = []
            centroids = []
            for label in unique_labels:
                cluster_mask = cluster_labels == label
                centroid = np.mean(features[cluster_mask], axis=0)
                centroids.append(centroid)
            
            if len(centroids) > 1:
                from scipy.spatial.distance import pdist
                inter_distances = pdist(np.array(centroids))
                avg_inter_distance = np.mean(inter_distances)
            else:
                avg_inter_distance = 0.0
            
            return avg_intra_distance, avg_inter_distance
            
        except Exception as e:
            logger.error(f"Error calculating cluster distances: {e}")
            return 0.0, 0.0
    
    def _calculate_stability_score(self, current_labels: np.ndarray) -> float:
        """Calculate clustering stability based on recent history"""
        try:
            if len(self.metrics_history) < 2:
                return 1.0
            
            # Get recent cluster labels from history (would need to store these)
            # For now, use a simplified stability measure based on metrics consistency
            recent_counts = [m.cluster_count for m in self.metrics_history[-5:]]
            recent_silhouettes = [m.silhouette_score for m in self.metrics_history[-5:]]
            
            # Stability is higher when cluster count and quality are consistent
            count_stability = 1.0 - (np.std(recent_counts) / (np.mean(recent_counts) + 1e-10))
            quality_stability = 1.0 - np.std(recent_silhouettes)
            
            return (count_stability + quality_stability) / 2.0
            
        except Exception as e:
            logger.error(f"Error calculating stability score: {e}")
            return 0.0
    
    def _classify_regime_type(
        self,
        cluster_count: int,
        dominant_ratio: float,
        fragmentation: float,
        correlation_matrix: np.ndarray = None
    ) -> Tuple[RegimeType, float, str]:
        """Classify regime type based on clustering characteristics"""
        
        # Unified regime: One dominant cluster
        if cluster_count == 1 or dominant_ratio > 0.8:
            confidence = min(dominant_ratio, 1.0)
            return RegimeType.UNIFIED, confidence, f"Unified market with {dominant_ratio:.1%} in main cluster"
        
        # Bifurcated regime: Two balanced clusters
        elif cluster_count == 2 and 0.3 <= dominant_ratio <= 0.7:
            confidence = 1.0 - abs(0.5 - dominant_ratio)
            return RegimeType.BIFURCATED, confidence, "Market split into two main groups"
        
        # Fragmented regime: Many small clusters
        elif cluster_count >= 4 or fragmentation > 0.6:
            confidence = min(fragmentation, 1.0)
            return RegimeType.FRAGMENTED, confidence, f"Fragmented market with {cluster_count} clusters"
        
        # Outlier-driven: One cluster plus outliers
        elif cluster_count >= 2 and dominant_ratio > 0.6:
            confidence = dominant_ratio
            return RegimeType.OUTLIER_DRIVEN, confidence, "Main cluster with outlier currencies"
        
        # Transitional: Unclear structure
        else:
            confidence = 0.5
            return RegimeType.TRANSITIONAL, confidence, "Transitional or unclear regime structure"

    def _detect_trend_change(self, values: List[float], threshold: float) -> bool:
        """Detect significant trend change in a series of values"""
        if len(values) < 3:
            return False

        # Simple trend detection: compare recent average to earlier average
        mid_point = len(values) // 2
        early_avg = np.mean(values[:mid_point])
        recent_avg = np.mean(values[mid_point:])

        change = (recent_avg - early_avg) / (abs(early_avg) + 1e-10)
        return change < threshold

    def _create_regime_change_event(
        self,
        prev_regime: RegimeClassification,
        current_regime: RegimeClassification,
        symbols: List[str]
    ) -> AdvancedEvent:
        """Create regime change event"""
        severity = "high" if prev_regime.regime_type == RegimeType.UNIFIED else "medium"

        return AdvancedEvent(
            timestamp=current_regime.timestamp,
            event_type="regime_change",
            severity=severity,
            description=f"Regime changed from {prev_regime.regime_type.value} to {current_regime.regime_type.value}",
            affected_symbols=symbols,
            metrics={
                "prev_confidence": prev_regime.confidence,
                "current_confidence": current_regime.confidence,
                "fragmentation_change": current_regime.fragmentation_index - prev_regime.fragmentation_index
            },
            regime_change=current_regime
        )

    def _create_quality_degradation_event(
        self,
        current_metrics: ClusteringMetrics,
        symbols: List[str]
    ) -> AdvancedEvent:
        """Create clustering quality degradation event"""
        return AdvancedEvent(
            timestamp=current_metrics.timestamp,
            event_type="quality_degradation",
            severity="medium",
            description=f"Clustering quality degraded (silhouette: {current_metrics.silhouette_score:.3f})",
            affected_symbols=symbols,
            metrics={
                "silhouette_score": current_metrics.silhouette_score,
                "davies_bouldin_score": current_metrics.davies_bouldin_score,
                "stability_score": current_metrics.stability_score
            },
            regime_change=None
        )

    def _create_fragmentation_event(
        self,
        current_regime: RegimeClassification,
        symbols: List[str]
    ) -> AdvancedEvent:
        """Create market fragmentation event"""
        return AdvancedEvent(
            timestamp=current_regime.timestamp,
            event_type="market_fragmentation",
            severity="high",
            description=f"High market fragmentation detected (index: {current_regime.fragmentation_index:.3f})",
            affected_symbols=symbols,
            metrics={
                "fragmentation_index": current_regime.fragmentation_index,
                "cluster_count": len(current_regime.cluster_distribution),
                "dominant_ratio": current_regime.dominant_cluster_ratio
            },
            regime_change=current_regime
        )

    def _create_cluster_count_change_event(
        self,
        prev_count: int,
        current_count: int,
        symbols: List[str]
    ) -> AdvancedEvent:
        """Create cluster count change event"""
        severity = "high" if abs(current_count - prev_count) >= 3 else "medium"
        direction = "increased" if current_count > prev_count else "decreased"

        return AdvancedEvent(
            timestamp=datetime.now(MARKET_TIMEZONE),
            event_type="cluster_count_change",
            severity=severity,
            description=f"Cluster count {direction} from {prev_count} to {current_count}",
            affected_symbols=symbols,
            metrics={
                "prev_count": prev_count,
                "current_count": current_count,
                "change": current_count - prev_count
            },
            regime_change=None
        )

    def get_metrics_summary(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get summary of clustering metrics over specified time period"""
        cutoff_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=hours_back)
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]

        if not recent_metrics:
            return {"error": "No metrics available for specified time period"}

        return {
            "period_hours": hours_back,
            "total_measurements": len(recent_metrics),
            "avg_silhouette_score": np.mean([m.silhouette_score for m in recent_metrics]),
            "avg_cluster_count": np.mean([m.cluster_count for m in recent_metrics]),
            "avg_stability_score": np.mean([m.stability_score for m in recent_metrics]),
            "cluster_count_range": [
                min([m.cluster_count for m in recent_metrics]),
                max([m.cluster_count for m in recent_metrics])
            ],
            "quality_trend": self._calculate_quality_trend(recent_metrics),
            "regime_distribution": self._get_regime_distribution(hours_back)
        }

    def _calculate_quality_trend(self, metrics: List[ClusteringMetrics]) -> str:
        """Calculate overall quality trend"""
        if len(metrics) < 5:
            return "insufficient_data"

        silhouette_scores = [m.silhouette_score for m in metrics]
        early_avg = np.mean(silhouette_scores[:len(silhouette_scores)//2])
        recent_avg = np.mean(silhouette_scores[len(silhouette_scores)//2:])

        change = (recent_avg - early_avg) / (abs(early_avg) + 1e-10)

        if change > 0.1:
            return "improving"
        elif change < -0.1:
            return "degrading"
        else:
            return "stable"

    def _get_regime_distribution(self, hours_back: int) -> Dict[str, int]:
        """Get distribution of regime types over time period"""
        cutoff_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=hours_back)
        recent_regimes = [r for r in self.regime_history if r.timestamp >= cutoff_time]

        distribution = {}
        for regime in recent_regimes:
            regime_name = regime.regime_type.value
            distribution[regime_name] = distribution.get(regime_name, 0) + 1

        return distribution
