"""
MT5 Data Manager for Dynamic FX Clustering Application
Handles MetaTrader 5 connection, data fetching, and weekend support
Based on proven matrix_QP patterns with clustering-specific adaptations
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import time
import logging
import pytz
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple, Any
import psutil

from config import (
    CURRENCY_PAIRS, TIMEFRAME, MT5_TIMEOUT, MAX_RETRIES, RETRY_DELAY,
    MARKET_TIMEZONE, START_OF_DAY_HOUR, DATA_BUFFER_HOURS,
    get_market_start_time, get_market_end_time, is_market_open
)
from weekend_utils import (
    should_use_friday_data, get_last_friday_end, cache_friday_data,
    get_cached_friday_data, log_weekend_status
)

# Configure logging
logger = logging.getLogger(__name__)


class ClusteringDataManager:
    """
    MT5 Data Manager specifically designed for clustering applications
    Provides robust data fetching with weekend support and Rust integration
    """
    
    def __init__(self):
        """Initialize clustering data manager"""
        self.connected = False
        self.last_connection_time = None
        self.last_data_fetch = None
        self.cached_data = {}
        
    def connect(self, retries: int = MAX_RETRIES) -> bool:
        """
        Connect to MetaTrader 5 terminal
        
        Args:
            retries: Number of connection attempts
            
        Returns:
            bool: True if connection successful, False otherwise
        """
        for attempt in range(retries):
            try:
                if mt5.initialize(path="E:\\icdemomt5\\terminal64.exe", portable=True):
                    self.connected = True
                    self.last_connection_time = datetime.now()
                    logger.info("ClusteringDataManager: Successfully connected to MetaTrader 5")
                    
                    # Log account information
                    account_info = mt5.account_info()
                    if account_info:
                        logger.info(f"Connected to account: {account_info.login}")
                        logger.info(f"Server: {account_info.server}")
                        logger.info(f"Currency: {account_info.currency}")
                    
                    return True
                else:
                    error_code = mt5.last_error()
                    logger.warning(f"MT5 connection failed, error code: {error_code}. Attempt {attempt + 1}/{retries}")
                    
            except Exception as e:
                logger.error(f"Exception during MT5 connection attempt {attempt + 1}: {str(e)}")
            
            if attempt < retries - 1:
                logger.info(f"Retrying connection in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)
        
        logger.error("Failed to connect to MetaTrader 5 after all attempts")
        self.connected = False
        return False
    
    def disconnect(self):
        """Disconnect from MetaTrader 5"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            logger.info("ClusteringDataManager: Disconnected from MetaTrader 5")
    
    def is_connected(self) -> bool:
        """Check if MT5 connection is active"""
        if not self.connected:
            return False
        
        # Test connection with a simple call
        try:
            mt5.terminal_info()
            return True
        except:
            self.connected = False
            return False
    
    def fetch_clustering_data(
        self,
        pairs: Optional[List[str]] = None,
        hours_back: int = 240,
        use_weekend_fallback: bool = True
    ) -> Dict[str, pd.DataFrame]:
        """
        Fetch market data optimized for clustering analysis
        
        Args:
            pairs: List of currency pairs (defaults to config CURRENCY_PAIRS)
            hours_back: Hours of historical data to fetch
            use_weekend_fallback: Whether to use Friday data during weekends
            
        Returns:
            Dict mapping pair names to DataFrames with OHLCV data
        """
        if pairs is None:
            pairs = CURRENCY_PAIRS
        
        # Ensure connection
        if not self.is_connected():
            if not self.connect():
                logger.error("Cannot fetch data: MT5 connection failed")
                return {}
        
        # Weekend handling
        if use_weekend_fallback and should_use_friday_data():
            return self._fetch_weekend_data(pairs, hours_back)
        
        # Regular weekday data fetching
        return self._fetch_regular_data(pairs, hours_back)
    
    def _fetch_weekend_data(self, pairs: List[str], hours_back: int) -> Dict[str, pd.DataFrame]:
        """
        Fetch Friday data during weekends (following matrix_QP pattern)
        
        Args:
            pairs: List of currency pairs
            hours_back: Hours of historical data
            
        Returns:
            Dict mapping pair names to DataFrames
        """
        log_weekend_status()
        
        # Try to get cached Friday data first
        cached_data = get_cached_friday_data('clustering_data')
        if cached_data:
            logger.info("Using cached Friday data for weekend clustering")
            return cached_data
        
        # Fetch fresh Friday data
        friday_end = get_last_friday_end()
        friday_start = friday_end - timedelta(hours=hours_back)
        
        logger.info(f"Weekend mode: Fetching Friday data from {friday_start} to {friday_end}")
        
        data = self._fetch_data_range(pairs, friday_start, friday_end)
        
        # Cache the Friday data
        if data:
            cache_friday_data('clustering_data', data)
            logger.info("Cached Friday data for future weekend use")
        
        return data
    
    def _fetch_regular_data(self, pairs: List[str], hours_back: int) -> Dict[str, pd.DataFrame]:
        """
        Fetch regular weekday data
        
        Args:
            pairs: List of currency pairs
            hours_back: Hours of historical data
            
        Returns:
            Dict mapping pair names to DataFrames
        """
        end_time = datetime.now(MARKET_TIMEZONE)
        start_time = end_time - timedelta(hours=hours_back)
        
        logger.info(f"Fetching regular data from {start_time} to {end_time}")
        
        return self._fetch_data_range(pairs, start_time, end_time)
    
    def _fetch_data_range(
        self,
        pairs: List[str],
        start_time: datetime,
        end_time: datetime
    ) -> Dict[str, pd.DataFrame]:
        """
        Fetch data for specified time range
        
        Args:
            pairs: List of currency pairs
            start_time: Start time for data fetch
            end_time: End time for data fetch
            
        Returns:
            Dict mapping pair names to DataFrames
        """
        logger.info(f"Fetching data for {len(pairs)} pairs from {start_time} to {end_time}")
        
        # Check CPU usage and throttle if necessary
        current_cpu = psutil.cpu_percent(interval=0.1)
        if current_cpu > 80:
            time.sleep(0.5)
        elif current_cpu > 70:
            time.sleep(0.2)
        
        data = {}
        failed_pairs = []
        
        for pair in pairs:
            try:
                df = self._fetch_pair_data(pair, start_time, end_time)
                if df is not None and not df.empty:
                    data[pair] = df
                else:
                    failed_pairs.append(pair)
                    logger.warning(f"No data retrieved for {pair}")
                    
            except Exception as e:
                failed_pairs.append(pair)
                logger.error(f"Error fetching data for {pair}: {str(e)}")
        
        if failed_pairs:
            logger.warning(f"Failed to fetch data for {len(failed_pairs)} pairs: {failed_pairs}")
        
        logger.info(f"Successfully fetched data for {len(data)} pairs")
        self.last_data_fetch = datetime.now()
        
        return data
    
    def _fetch_pair_data(
        self,
        pair: str,
        start_time: datetime,
        end_time: datetime
    ) -> Optional[pd.DataFrame]:
        """
        Fetch data for a single currency pair
        
        Args:
            pair: Currency pair symbol
            start_time: Start time for data fetch
            end_time: End time for data fetch
            
        Returns:
            DataFrame with OHLCV data or None if failed
        """
        try:
            # MT5 timezone handling (following matrix_QP pattern)
            offset_hours = start_time.utcoffset().total_seconds() / 3600
            start_adjusted = start_time + timedelta(hours=offset_hours)
            end_adjusted = end_time + timedelta(hours=offset_hours)

            start_timestamp = int(start_adjusted.astimezone(pytz.UTC).timestamp())
            end_timestamp = int(end_adjusted.astimezone(pytz.UTC).timestamp())

            # Fetch rates from MT5
            rates = mt5.copy_rates_range(pair, TIMEFRAME, start_timestamp, end_timestamp)
            
            if rates is None or len(rates) == 0:
                logger.warning(f"No rates returned for {pair}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(rates)
            
            # Convert time to datetime and set timezone
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df['time'] = df['time'].dt.tz_localize('UTC').dt.tz_convert(MARKET_TIMEZONE)
            
            # Set time as index
            df.set_index('time', inplace=True)
            
            # Ensure we have the required columns for clustering
            required_columns = ['open', 'high', 'low', 'close', 'tick_volume']
            if not all(col in df.columns for col in required_columns):
                logger.error(f"Missing required columns for {pair}: {df.columns.tolist()}")
                return None

            return df
            
        except Exception as e:
            logger.error(f"Error fetching data for {pair}: {str(e)}")
            return None
    
    def prepare_data_for_rust(self, data: Dict[str, pd.DataFrame]) -> List[List[float]]:
        """
        Convert fetched data to format suitable for Rust clustering engine
        
        Args:
            data: Dict mapping pair names to DataFrames
            
        Returns:
            List of lists containing close prices for each pair
        """
        if not data:
            logger.warning("No data to prepare for Rust")
            return []
        
        try:
            # Get all timestamps and sort them
            all_timestamps = set()
            for df in data.values():
                all_timestamps.update(df.index)
            
            sorted_timestamps = sorted(all_timestamps)
            
            # Create aligned price matrix
            price_matrix = []
            pair_names = sorted(data.keys())  # Ensure consistent ordering
            
            for timestamp in sorted_timestamps:
                row = []
                for pair in pair_names:
                    if pair in data and timestamp in data[pair].index:
                        # Use close price for clustering
                        close_price = data[pair].loc[timestamp, 'close']
                        row.append(float(close_price))
                    else:
                        # Forward fill missing data with last known value
                        if row:
                            row.append(row[-1])  # Use last value in row
                        else:
                            row.append(1.0)  # Default value if no previous data
                
                price_matrix.append(row)
            
            logger.info(f"Prepared {len(price_matrix)} x {len(pair_names)} price matrix for Rust")
            return price_matrix
            
        except Exception as e:
            logger.error(f"Error preparing data for Rust: {str(e)}")
            return []
    
    def get_pair_names(self) -> List[str]:
        """Get the list of currency pairs being tracked"""
        return CURRENCY_PAIRS.copy()
    
    def get_data_summary(self, data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Get summary statistics for fetched data
        
        Args:
            data: Dict mapping pair names to DataFrames
            
        Returns:
            Dict with summary statistics
        """
        if not data:
            return {}
        
        summary = {
            'pairs_count': len(data),
            'total_records': sum(len(df) for df in data.values()),
            'time_range': {},
            'data_completeness': {}
        }
        
        # Calculate time range
        all_start_times = [df.index.min() for df in data.values() if not df.empty]
        all_end_times = [df.index.max() for df in data.values() if not df.empty]
        
        if all_start_times and all_end_times:
            summary['time_range'] = {
                'start': min(all_start_times),
                'end': max(all_end_times),
                'duration_hours': (max(all_end_times) - min(all_start_times)).total_seconds() / 3600
            }
        
        # Calculate data completeness for each pair
        for pair, df in data.items():
            if not df.empty:
                summary['data_completeness'][pair] = {
                    'records': len(df),
                    'start_time': df.index.min(),
                    'end_time': df.index.max()
                }
        
        return summary
