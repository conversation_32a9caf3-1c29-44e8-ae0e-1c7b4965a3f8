"""
Portfolio Analytics Demonstration Script

This script demonstrates the advanced portfolio analytics capabilities
for cluster-based diversification, risk optimization, and dynamic rebalancing.
"""

import numpy as np
import pandas as pd
from typing import Dict, List
import logging
from datetime import datetime
import json

from portfolio_analytics import PortfolioAnalytics
from clustering_engine import ClusteringEngine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_portfolio_analysis_demo():
    """
    Comprehensive demonstration of portfolio analytics features
    """
    print("=" * 80)
    print("ADVANCED PORTFOLIO ANALYTICS DEMONSTRATION")
    print("=" * 80)
    
    # Initialize with 28 major FX pairs
    symbols = [
        'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
        'EURGBP', 'EURJPY', 'EURCHF', 'EURAUD', 'EURCAD', 'EURNZD',
        'GBPJPY', 'GBPC<PERSON>', 'GBPAUD', 'GBPCAD', 'GBPNZD',
        'CHFJPY', 'AUDCHF', 'CADCHF', 'NZDCHF',
        'AUDJPY', 'CADJPY', 'NZDJPY',
        'AUDCAD', 'AUDNZD', 'CADNZD'
    ]
    
    # Initialize portfolio analytics
    portfolio_analytics = PortfolioAnalytics(symbols)
    
    # Generate sample correlation matrix and cluster assignments
    # (In real usage, these would come from the clustering engine)
    correlation_matrix, cluster_assignments, volatility_profiles = generate_sample_data(symbols)
    
    print(f"\n📊 ANALYZING {len(symbols)} CURRENCY PAIRS")
    print(f"Unique clusters detected: {len(set(cluster_assignments))}")
    
    # 1. CLUSTER ANALYSIS
    print("\n" + "="*60)
    print("1. CLUSTER ANALYSIS")
    print("="*60)
    
    cluster_analyses = portfolio_analytics.analyze_clusters(
        correlation_matrix, cluster_assignments, volatility_profiles
    )
    
    print(f"\nCluster breakdown:")
    for cluster_id, analysis in cluster_analyses.items():
        print(f"  Cluster {cluster_id}: {analysis.cluster_size} pairs")
        print(f"    Representative: {analysis.representative_symbol}")
        print(f"    Avg Correlation: {analysis.avg_correlation:.3f}")
        print(f"    Symbols: {', '.join(analysis.symbols[:3])}{'...' if len(analysis.symbols) > 3 else ''}")
    
    # 2. DIVERSIFICATION ANALYSIS
    print("\n" + "="*60)
    print("2. DIVERSIFICATION ANALYSIS")
    print("="*60)
    
    diversification_matrix = portfolio_analytics.calculate_diversification_matrix(
        correlation_matrix, cluster_assignments
    )
    
    best_pairs = portfolio_analytics.get_best_diversification_pairs(5)
    print(f"\nTop 5 diversification opportunities:")
    for i, pair in enumerate(best_pairs, 1):
        c1, c2 = pair.cluster_pair
        print(f"  {i}. Clusters {c1} ↔ {c2}")
        print(f"     Inter-cluster correlation: {pair.inter_cluster_correlation:.3f}")
        print(f"     Diversification ratio: {pair.diversification_ratio:.3f}")
        print(f"     Risk reduction potential: {pair.risk_reduction_potential:.1%}")
    
    # 3. PORTFOLIO TEMPLATES
    print("\n" + "="*60)
    print("3. PORTFOLIO TEMPLATE GENERATION")
    print("="*60)
    
    templates = portfolio_analytics.generate_portfolio_templates(
        correlation_matrix, cluster_assignments, volatility_profiles
    )
    
    print(f"\nGenerated {len(templates)} portfolio templates:")
    for template in templates:
        print(f"\n📋 {template.name} Portfolio")
        print(f"   Strategy: {template.strategy}")
        print(f"   Expected Risk: {template.expected_risk:.3f}")
        print(f"   Diversification Score: {template.diversification_score:.3f}")
        print(f"   Rebalancing: {template.rebalancing_frequency}")
        print(f"   Description: {template.description}")
        
        # Show top 5 positions
        sorted_weights = sorted(template.weights.items(), key=lambda x: x[1], reverse=True)
        print(f"   Top positions:")
        for symbol, weight in sorted_weights[:5]:
            print(f"     {symbol}: {weight:.1%}")
        
        # Show cluster distribution
        print(f"   Cluster distribution:")
        for cluster_id, weight in template.cluster_distribution.items():
            print(f"     Cluster {cluster_id}: {weight:.1%}")
    
    # 4. RISK ANALYSIS
    print("\n" + "="*60)
    print("4. RISK ANALYSIS COMPARISON")
    print("="*60)
    
    print(f"\nRisk comparison across strategies:")
    for template in templates:
        risk = template.expected_risk
        div_score = template.diversification_score
        print(f"  {template.name:20s}: Risk={risk:.3f}, Diversification={div_score:.3f}")
    
    # 5. REBALANCING SIMULATION
    print("\n" + "="*60)
    print("5. DYNAMIC REBALANCING SIMULATION")
    print("="*60)
    
    # Simulate market regime change
    print("\nSimulating market regime change...")
    
    # Create "new" market conditions
    new_correlation_matrix = correlation_matrix * 1.2  # Increase correlations
    new_correlation_matrix = np.clip(new_correlation_matrix, -1, 1)
    np.fill_diagonal(new_correlation_matrix, 1.0)
    
    # Simulate cluster changes
    new_cluster_assignments = [(c + 1) % max(cluster_assignments) for c in cluster_assignments]
    
    # Use conservative portfolio as current allocation
    current_allocation = templates[0].weights  # Conservative portfolio
    
    signals = portfolio_analytics.detect_rebalancing_signals(
        current_allocation=current_allocation,
        new_correlation_matrix=new_correlation_matrix,
        new_cluster_assignments=new_cluster_assignments,
        new_volatility_profiles=volatility_profiles,
        previous_correlation_matrix=correlation_matrix,
        previous_cluster_assignments=cluster_assignments
    )
    
    print(f"\nRebalancing signals detected: {len(signals)}")
    for signal in signals:
        print(f"\n🚨 {signal.trigger_type.upper()} SIGNAL")
        print(f"   Urgency: {signal.urgency}")
        print(f"   Reason: {signal.reason}")
        print(f"   Expected improvement: {signal.expected_improvement:.1%}")
        
        # Show allocation changes
        print(f"   Key allocation changes:")
        current = signal.current_allocation
        recommended = signal.recommended_allocation
        
        changes = []
        for symbol in symbols[:10]:  # Show first 10 for brevity
            current_weight = current.get(symbol, 0.0)
            new_weight = recommended.get(symbol, 0.0)
            change = new_weight - current_weight
            if abs(change) > 0.01:  # Only show significant changes
                changes.append((symbol, current_weight, new_weight, change))
        
        changes.sort(key=lambda x: abs(x[3]), reverse=True)
        for symbol, curr, new, change in changes[:5]:
            print(f"     {symbol}: {curr:.1%} → {new:.1%} ({change:+.1%})")
    
    # 6. EXPORT FOR MPT
    print("\n" + "="*60)
    print("6. MPT EXPORT")
    print("="*60)
    
    export_success = portfolio_analytics.export_portfolio_templates_for_mpt(
        "exports/portfolio_templates_for_mpt.json"
    )
    
    if export_success:
        print("✅ Portfolio templates exported successfully!")
        print("   File: exports/portfolio_templates_for_mpt.json")
        print("   Ready for import into MPT project")
    else:
        print("❌ Export failed")
    
    # 7. SUMMARY
    print("\n" + "="*60)
    print("7. ANALYTICS SUMMARY")
    print("="*60)
    
    summary = portfolio_analytics.get_portfolio_summary()
    print(f"\n📈 Portfolio Analytics Summary:")
    print(f"   Clusters analyzed: {summary['cluster_count']}")
    print(f"   Templates generated: {summary['template_count']}")
    print(f"   Rebalancing signals: {summary['rebalancing_signals_count']}")
    print(f"   Best diversification pairs: {len(summary['best_diversification_pairs'])}")
    
    print("\n" + "="*80)
    print("DEMONSTRATION COMPLETE")
    print("="*80)
    
    return portfolio_analytics


def generate_sample_data(symbols: List[str]):
    """Generate sample correlation matrix and cluster data for demonstration"""
    n_symbols = len(symbols)
    
    # Generate realistic correlation matrix
    np.random.seed(42)  # For reproducible results
    
    # Create base correlation matrix
    correlation_matrix = np.random.uniform(0.1, 0.8, (n_symbols, n_symbols))
    correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2  # Make symmetric
    np.fill_diagonal(correlation_matrix, 1.0)
    
    # Generate cluster assignments (7 clusters as per user preference)
    cluster_assignments = []
    cluster_size = n_symbols // 7
    for i in range(n_symbols):
        cluster_id = min(i // cluster_size, 6)  # Ensure max 7 clusters
        cluster_assignments.append(cluster_id)
    
    # Generate volatility profiles
    volatility_profiles = {}
    for symbol in symbols:
        # Realistic FX volatility range
        volatility = np.random.uniform(0.008, 0.025)  # 0.8% to 2.5% daily volatility
        volatility_profiles[symbol] = volatility
    
    return correlation_matrix, cluster_assignments, volatility_profiles


if __name__ == "__main__":
    # Ensure exports directory exists
    import os
    os.makedirs("exports", exist_ok=True)
    
    # Run the demonstration
    analytics = run_portfolio_analysis_demo()
