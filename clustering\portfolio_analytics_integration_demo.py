"""
Comprehensive Portfolio Analytics Integration Demo

This script demonstrates the complete portfolio analytics workflow:
1. Cluster analysis and diversification metrics
2. Risk optimization and portfolio construction
3. Dynamic rebalancing strategies
4. Portfolio template generation
5. Export for MPT project integration

This serves as both a demonstration and integration test for all components.
"""

import numpy as np
import pandas as pd
import json
from datetime import datetime, timedelta
import logging

# Import all portfolio analytics components
from portfolio_analytics import PortfolioAnalytics
from risk_optimization import RiskOptimizer
from dynamic_rebalancing import DynamicRebalancer, RebalancingThresholds
from portfolio_templates import PortfolioTemplateGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def generate_sample_market_data(symbols, n_periods=100):
    """Generate realistic sample market data for demonstration"""
    np.random.seed(42)
    
    # Generate price returns with realistic correlation structure
    n_assets = len(symbols)
    
    # Create base correlation matrix with some structure
    correlation_matrix = np.eye(n_assets)
    
    # Add some realistic correlations (currency pairs often have regional correlations)
    for i in range(n_assets):
        for j in range(i+1, n_assets):
            # Higher correlation for similar currency regions
            base_corr = 0.1 + np.random.exponential(0.2)
            correlation_matrix[i, j] = correlation_matrix[j, i] = min(base_corr, 0.8)
    
    # Generate volatility profiles (realistic FX volatilities)
    volatility_profiles = {}
    for symbol in symbols:
        # FX volatilities typically range from 0.5% to 2% daily
        vol = 0.005 + np.random.exponential(0.008)
        volatility_profiles[symbol] = min(vol, 0.025)
    
    # Generate cluster assignments (simulate hierarchical clustering results)
    n_clusters = min(4, n_assets // 2)
    cluster_assignments = []
    for i in range(n_assets):
        cluster_assignments.append(i % n_clusters)
    
    # Generate expected returns (risk premium model)
    expected_returns = []
    for symbol in symbols:
        vol = volatility_profiles[symbol]
        # Higher volatility assets get higher expected returns
        expected_return = 0.01 + (vol * 1.5) + np.random.normal(0, 0.005)
        expected_returns.append(max(expected_return, 0.005))  # Minimum 0.5% return
    
    return {
        'correlation_matrix': correlation_matrix,
        'volatility_profiles': volatility_profiles,
        'cluster_assignments': cluster_assignments,
        'expected_returns': np.array(expected_returns),
        'symbols': symbols
    }


def run_comprehensive_portfolio_analytics_demo():
    """Run comprehensive demonstration of all portfolio analytics components"""
    
    print("=" * 80)
    print("COMPREHENSIVE PORTFOLIO ANALYTICS INTEGRATION DEMO")
    print("=" * 80)
    print()
    
    # Define currency pairs for analysis
    symbols = [
        'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 
        'USDCAD', 'NZDUSD', 'EURGBP', 'EURJPY', 'GBPJPY'
    ]
    
    print(f"Analyzing {len(symbols)} currency pairs:")
    print(f"Symbols: {symbols}")
    print()
    
    # Generate sample market data
    print("1. GENERATING SAMPLE MARKET DATA")
    print("-" * 40)
    
    market_data = generate_sample_market_data(symbols)
    
    print(f"Generated correlation matrix: {market_data['correlation_matrix'].shape}")
    print(f"Volatility profiles: {len(market_data['volatility_profiles'])} assets")
    print(f"Cluster assignments: {market_data['cluster_assignments']}")
    print(f"Expected returns range: {min(market_data['expected_returns']):.2%} to {max(market_data['expected_returns']):.2%}")
    print()
    
    # Initialize all components
    print("2. INITIALIZING PORTFOLIO ANALYTICS COMPONENTS")
    print("-" * 40)
    
    portfolio_analytics = PortfolioAnalytics(symbols)
    risk_optimizer = RiskOptimizer(symbols)
    rebalancer = DynamicRebalancer(symbols)
    template_generator = PortfolioTemplateGenerator(symbols)
    
    print("✓ Portfolio Analytics Engine initialized")
    print("✓ Risk Optimization Engine initialized")
    print("✓ Dynamic Rebalancing Engine initialized")
    print("✓ Portfolio Template Generator initialized")
    print()
    
    # Perform cluster analysis
    print("3. CLUSTER ANALYSIS AND DIVERSIFICATION METRICS")
    print("-" * 40)
    
    cluster_analyses = portfolio_analytics.analyze_clusters(
        market_data['correlation_matrix'],
        market_data['cluster_assignments'],
        market_data['volatility_profiles']
    )

    print(f"Cluster Analysis Results:")
    print(f"  Number of clusters: {len(cluster_analyses)}")

    # Calculate overall statistics
    all_correlations = []
    for cluster_analysis in cluster_analyses.values():
        all_correlations.append(cluster_analysis.avg_correlation)

    if all_correlations:
        avg_intra_cluster_corr = np.mean(all_correlations)
        print(f"  Average intra-cluster correlation: {avg_intra_cluster_corr:.3f}")
    print()

    # Show cluster summaries
    for cluster_id, analysis in cluster_analyses.items():
        print(f"  Cluster {cluster_id}: {len(analysis.symbols)} assets, "
              f"avg correlation: {analysis.avg_correlation:.3f}")
        print(f"    Symbols: {', '.join(analysis.symbols)}")
        print(f"    Representative: {analysis.representative_symbol}")
    print()
    
    # Calculate diversification matrix
    diversification_metrics = portfolio_analytics.calculate_diversification_matrix(
        market_data['correlation_matrix'],
        market_data['cluster_assignments']
    )

    print(f"Diversification Analysis:")
    print(f"  Cluster pair combinations analyzed: {len(diversification_metrics)}")

    if diversification_metrics:
        print(f"  Best diversification opportunities:")
        # Sort by diversification ratio (higher is better)
        sorted_metrics = sorted(diversification_metrics.items(),
                              key=lambda x: x[1].diversification_ratio, reverse=True)

        for (cluster1, cluster2), metrics in sorted_metrics[:3]:
            print(f"    Clusters {cluster1}-{cluster2}: "
                  f"correlation {metrics.inter_cluster_correlation:.3f}, "
                  f"diversification ratio {metrics.diversification_ratio:.2f}")

    # Find best asset pairs by correlation
    print(f"  Best asset diversification pairs (lowest correlation):")
    n_assets = len(symbols)
    best_pairs = []
    for i in range(n_assets):
        for j in range(i+1, n_assets):
            correlation = market_data['correlation_matrix'][i, j]
            best_pairs.append((symbols[i], symbols[j], correlation))

    best_pairs.sort(key=lambda x: x[2])
    for pair in best_pairs[:3]:
        print(f"    {pair[0]} - {pair[1]}: {pair[2]:.3f}")
    print()
    
    # Risk optimization
    print("4. RISK OPTIMIZATION AND PORTFOLIO CONSTRUCTION")
    print("-" * 40)
    
    covariance_matrix = risk_optimizer.calculate_covariance_matrix(
        market_data['correlation_matrix'],
        market_data['volatility_profiles']
    )
    
    # Test different optimization methods
    optimization_results = {}
    
    # Minimum variance portfolio
    min_var_result = risk_optimizer.optimize_minimum_variance(covariance_matrix)
    optimization_results['minimum_variance'] = min_var_result
    
    # Maximum Sharpe ratio portfolio
    max_sharpe_result = risk_optimizer.optimize_maximum_sharpe(
        covariance_matrix, market_data['expected_returns']
    )
    optimization_results['maximum_sharpe'] = max_sharpe_result
    
    # Risk parity portfolio
    risk_parity_result = risk_optimizer.optimize_risk_parity(covariance_matrix)
    optimization_results['risk_parity'] = risk_parity_result
    
    print("Optimization Results:")
    for method, result in optimization_results.items():
        if result.success:
            print(f"  {method.replace('_', ' ').title()}:")
            print(f"    Expected Return: {result.expected_return:.2%}")
            print(f"    Expected Risk: {result.expected_risk:.2%}")
            print(f"    Sharpe Ratio: {result.sharpe_ratio:.2f}")
            
            # Show top 3 positions
            sorted_weights = sorted(result.weights.items(), key=lambda x: x[1], reverse=True)[:3]
            print(f"    Top positions: {', '.join([f'{s}: {w:.1%}' for s, w in sorted_weights])}")
        else:
            print(f"  {method.replace('_', ' ').title()}: FAILED - {result.message}")
        print()
    
    # Dynamic rebalancing analysis
    print("5. DYNAMIC REBALANCING ANALYSIS")
    print("-" * 40)
    
    # Use the minimum variance portfolio as current allocation
    if min_var_result.success:
        current_weights = min_var_result.weights
        
        # Simulate some market changes
        # Slightly modify correlation matrix and volatilities
        modified_correlation = market_data['correlation_matrix'] + np.random.normal(0, 0.05, market_data['correlation_matrix'].shape)
        np.fill_diagonal(modified_correlation, 1.0)
        modified_correlation = np.clip(modified_correlation, -0.99, 0.99)
        
        modified_volatilities = {
            symbol: vol * (1 + np.random.normal(0, 0.2))
            for symbol, vol in market_data['volatility_profiles'].items()
        }
        
        # Evaluate rebalancing need
        rebalancing_actions = rebalancer.evaluate_rebalancing_need(
            current_weights=current_weights,
            correlation_matrix=modified_correlation,
            cluster_assignments=market_data['cluster_assignments'],
            volatility_profiles=modified_volatilities,
            regime_classification="normal"
        )
        
        print(f"Rebalancing Analysis:")
        print(f"  Current portfolio: Minimum Variance")
        print(f"  Market changes simulated: ✓")
        print(f"  Rebalancing actions recommended: {len(rebalancing_actions)}")
        print()
        
        for i, action in enumerate(rebalancing_actions):
            print(f"  Action {i+1}:")
            print(f"    Trigger: {action.trigger.value}")
            print(f"    Urgency: {action.urgency.value}")
            print(f"    Reason: {action.reason}")
            print(f"    Expected Cost: {action.expected_cost:.4f}")
            print(f"    Expected Benefit: {action.expected_benefit:.4f}")
            print(f"    Confidence: {action.confidence:.2f}")
            print()
    
    # Portfolio template generation
    print("6. PORTFOLIO TEMPLATE GENERATION")
    print("-" * 40)
    
    templates = template_generator.generate_all_templates(
        correlation_matrix=market_data['correlation_matrix'],
        volatility_profiles=market_data['volatility_profiles'],
        cluster_assignments=market_data['cluster_assignments'],
        expected_returns=market_data['expected_returns']
    )
    
    print(f"Generated {len(templates)} portfolio templates:")
    print()
    
    # Generate and display summary
    summary_df = template_generator.generate_template_summary(templates)
    print("Portfolio Template Summary:")
    print(summary_df.to_string(index=False))
    print()
    
    # Export templates for MPT project
    print("7. EXPORT FOR MPT PROJECT")
    print("-" * 40)
    
    export_filename = f"portfolio_templates_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    export_success = template_generator.export_templates_for_mpt(templates, export_filename)
    
    if export_success:
        print(f"✓ Templates exported successfully to: {export_filename}")
        
        # Show export file size and structure
        try:
            with open(export_filename, 'r') as f:
                export_data = json.load(f)
            
            print(f"  Export file size: {len(json.dumps(export_data))} characters")
            print(f"  Templates included: {export_data['metadata']['template_count']}")
            print(f"  Export timestamp: {export_data['metadata']['export_timestamp']}")
            print(f"  Target project: {export_data['metadata']['target_project']}")
        except Exception as e:
            print(f"  Could not read export file: {str(e)}")
    else:
        print("✗ Template export failed")
    print()
    
    # Performance summary
    print("8. PERFORMANCE SUMMARY")
    print("-" * 40)
    
    print("Portfolio Analytics Integration Demo completed successfully!")
    print()
    print("Key Results:")
    print(f"  • Analyzed {len(symbols)} currency pairs")
    print(f"  • Identified {len(set(market_data['cluster_assignments']))} correlation clusters")
    print(f"  • Generated {len([r for r in optimization_results.values() if r.success])} successful optimizations")
    print(f"  • Created {len(templates)} portfolio templates")
    print(f"  • Evaluated {len(rebalancing_actions) if 'rebalancing_actions' in locals() else 0} rebalancing recommendations")
    print()
    print("All components integrated successfully and ready for production use!")
    print()
    print("Next Steps:")
    print("  1. Integrate with real market data feeds")
    print("  2. Connect to volatility regime detection system")
    print("  3. Implement dashboard visualizations")
    print("  4. Set up automated rebalancing alerts")
    print("  5. Deploy templates to MPT project")
    print()
    print("=" * 80)


if __name__ == "__main__":
    run_comprehensive_portfolio_analytics_demo()
