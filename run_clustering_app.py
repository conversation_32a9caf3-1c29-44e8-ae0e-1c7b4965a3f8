"""
Dynamic FX Clustering Application - Main Dashboard
==================================================

Interactive 4-panel Dash dashboard for real-time forex market regime detection
with dendrogram visualization, Sankey diagrams, statistics panel, and event log.

Panels:
1. Top-Left: Interactive Dendrogram (cluster structure)
2. Top-Right: Sankey Diagram (cluster evolution over time)
3. Bottom-Left: Statistics Panel (metrics and performance)
4. Bottom-Right: Event Log (regime changes and alerts)
"""

# Fix OpenBLAS threading issue before importing numpy/sklearn
import os
os.environ['OPENBLAS_NUM_THREADS'] = '8'
os.environ['MKL_NUM_THREADS'] = '8'
os.environ['NUMEXPR_NUM_THREADS'] = '8'
os.environ['OMP_NUM_THREADS'] = '8'

import dash
from dash import dcc, html, Input, Output, State, callback_context, ALL
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import threading
import time
from typing import Dict, List, Optional, Any

# Import clustering components
from clustering.clustering_engine import ClusteringEngine
from clustering.state_manager import ClusteringState, ClusteringEvent
from clustering.dendrogram_utils import create_interactive_dendrogram, create_cluster_scatter_plot, calculate_cluster_statistics, create_cluster_visualization
from clustering.sankey_utils import (
    extract_cluster_evolution_data,
    create_cluster_evolution_sankey,
    create_cluster_timeline_chart,
    get_cluster_evolution_summary
)


from config import (
    CURRENCY_PAIRS, DATA_UPDATE_INTERVAL, CHART_UPDATE_INTERVAL,
    CHART_THEME, CHART_HEIGHT, CHART_WIDTH, MARKET_TIMEZONE
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import portfolio analytics components
try:
    from clustering.portfolio_analytics import PortfolioAnalytics
    from clustering.risk_optimization import RiskOptimizer
    from clustering.dynamic_rebalancing import DynamicRebalancer
    from clustering.portfolio_templates import PortfolioTemplateGenerator
    PORTFOLIO_ANALYTICS_AVAILABLE = True
    logger.info("Portfolio analytics modules loaded successfully")
except ImportError as e:
    PORTFOLIO_ANALYTICS_AVAILABLE = False
    logger.warning(f"Portfolio analytics modules not available: {e}")
    PortfolioAnalytics = None
    RiskOptimizer = None
    DynamicRebalancer = None
    PortfolioTemplateGenerator = None

# Import Rust clustering functions
try:
    import cluster_core
    RUST_AVAILABLE = True
    logger.info("Rust cluster_core module loaded successfully")
except ImportError as e:
    RUST_AVAILABLE = False
    logger.warning(f"Rust cluster_core module not available: {e}")
    logger.warning("Falling back to Python-only clustering")

# =============================================================================
# APPLICATION INITIALIZATION
# =============================================================================

# Initialize Dash app with Dark Bootstrap theme
app = dash.Dash(
    __name__,
    external_stylesheets=[dbc.themes.DARKLY, dbc.icons.FONT_AWESOME],
    title="Dynamic FX Clustering Dashboard",
    update_title="Updating...",
    suppress_callback_exceptions=True
)

# Set Plotly dark theme globally
import plotly.io as pio
pio.templates.default = "plotly_dark"

# Custom CSS for dark theme dropdown
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <style>
            .Select-control {
                background-color: #495057 !important;
                border-color: #6c757d !important;
                color: white !important;
            }
            .Select-placeholder, .Select--single > .Select-control .Select-value {
                color: white !important;
            }
            .Select-menu-outer {
                background-color: #495057 !important;
                border-color: #6c757d !important;
            }
            .Select-option {
                background-color: #495057 !important;
                color: white !important;
            }
            .Select-option:hover {
                background-color: #6c757d !important;
                color: white !important;
            }
            .Select-option.is-selected {
                background-color: #17a2b8 !important;
                color: white !important;
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

# Initialize clustering engine
clustering_engine = ClusteringEngine(
    symbols=CURRENCY_PAIRS,
    event_threshold=0.7,
    min_data_quality=0.8,
    persistence_dir="data/clustering"
)

# Initialize portfolio analytics components
portfolio_analytics = None
risk_optimizer = None
dynamic_rebalancer = None
portfolio_template_generator = None

if PORTFOLIO_ANALYTICS_AVAILABLE:
    try:
        portfolio_analytics = PortfolioAnalytics(CURRENCY_PAIRS)
        risk_optimizer = RiskOptimizer(CURRENCY_PAIRS)
        dynamic_rebalancer = DynamicRebalancer(CURRENCY_PAIRS)
        portfolio_template_generator = PortfolioTemplateGenerator(CURRENCY_PAIRS)
        logger.info("Portfolio analytics components initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize portfolio analytics components: {e}")
        PORTFOLIO_ANALYTICS_AVAILABLE = False

# Global state for dashboard
dashboard_state = {
    'last_update': None,
    'update_count': 0,
    'connection_status': 'disconnected',
    'auto_update': True,
    'selected_timeframe': '24h'
}

# Auto-connect on startup
logger.info("Attempting auto-connect to MT5...")
try:
    if clustering_engine.connect():
        dashboard_state['connection_status'] = 'connected'
        logger.info("Auto-connect successful")
    else:
        logger.warning("Auto-connect failed - user will need to connect manually")
except Exception as e:
    logger.error(f"Auto-connect error: {str(e)}")

# =============================================================================
# LAYOUT COMPONENTS
# =============================================================================

def create_header():
    """Create dashboard header with title and controls"""
    return dbc.Row([
        dbc.Col([
            html.H1("Dynamic FX Clustering Dashboard", className="text-primary mb-0"),
            html.P("Real-time forex market regime detection", className="text-muted")
        ], width=8),
        dbc.Col([
            dbc.ButtonGroup([
                dbc.Button("Connect", id="btn-connect", color="success", size="sm"),
                dbc.Button("Disconnect", id="btn-disconnect", color="danger", size="sm", disabled=True),
                dbc.Button("Refresh", id="btn-refresh", color="primary", size="sm")
            ], className="mb-2"),
            html.Div([
                dbc.Badge("Disconnected", id="status-badge", color="danger", className="me-2"),
                html.Small(id="last-update-text", className="text-muted")
            ])
        ], width=4, className="text-end")
    ], className="mb-4")

def create_control_panel():
    """Create control panel with timeframe and update settings"""
    return dbc.Card([
        dbc.CardBody([
            # First row - Main controls
            dbc.Row([
                dbc.Col([
                    html.Label("Timeframe:", className="form-label"),
                    dcc.Dropdown(
                        id="timeframe-dropdown",
                        options=[
                            {'label': 'Last 1 Hour', 'value': '1h'},
                            {'label': 'Last 6 Hours', 'value': '6h'},
                            {'label': 'Today (since 00:00)', 'value': '24h'},
                            {'label': 'Last 3 Days', 'value': '3d'},
                            {'label': 'Last Week', 'value': '7d'}
                        ],
                        value='24h',
                        clearable=False,
                        style={
                            'backgroundColor': '#495057',
                            'color': 'white'
                        }
                    )
                ], width=3),
                dbc.Col([
                    html.Label("Auto Update:", className="form-label"),
                    dbc.Switch(
                        id="auto-update-switch",
                        value=True,
                        className="mt-2"
                    )
                ], width=2),
                dbc.Col([
                    html.Label("Cluster Count:", className="form-label"),
                    html.H4(id="cluster-count-display", children="0", className="text-primary")
                ], width=2),
                dbc.Col([
                    html.Label("Data Quality:", className="form-label"),
                    html.H4(id="data-quality-display", children="0.0%", className="text-info")
                ], width=2),
                dbc.Col([
                    html.Label("Regime Stability:", className="form-label"),
                    html.H4(id="stability-display", children="0.0%", className="text-success")
                ], width=3)
            ], className="mb-3"),

            # Second row - Time Navigation
            dbc.Row([
                dbc.Col([
                    html.Div([
                        html.Label("Time Navigation:", className="form-label mb-2"),
                        html.Div([
                            dbc.ButtonGroup([
                                dbc.Button("Live", id="btn-live-mode", color="success", size="sm", active=True),
                                dbc.Button("Historical", id="btn-historical-mode", color="secondary", size="sm")
                            ], className="mb-2"),
                            html.Div(id="time-scrubber-container", children=[
                                dcc.RangeSlider(
                                    id="time-scrubber",
                                    min=0,
                                    max=100,
                                    value=[0, 100],
                                    marks={},
                                    step=1,
                                    disabled=True,
                                    tooltip={"placement": "bottom", "always_visible": False},
                                    className="mb-2"
                                ),
                                html.Div([
                                    html.Small(id="time-scrubber-start", className="text-muted me-3"),
                                    html.Small(id="time-scrubber-current", className="text-info me-3"),
                                    html.Small(id="time-scrubber-end", className="text-muted")
                                ], className="d-flex justify-content-between")
                            ], style={'display': 'none'})
                        ])
                    ])
                ], width=12)
            ])
        ])
    ], className="mb-3")

def create_correlation_clustering_panels():
    """Create the 4-panel correlation clustering layout"""
    return html.Div([
        dbc.Row([
            # Top row
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Cluster Dendrogram", className="mb-0"),
                        dbc.Badge("Interactive", color="info", className="ms-2")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            dcc.Graph(
                                id="dendrogram-chart",
                                style={'height': f'{CHART_HEIGHT}px'},
                                config={'displayModeBar': True, 'displaylogo': False}
                            ),
                            type="circle"
                        )
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Cluster Evolution", className="mb-0"),
                        dbc.Badge("Sankey Flow", color="warning", className="ms-2")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            dcc.Graph(
                                id="sankey-chart",
                                style={'height': f'{CHART_HEIGHT}px'},
                                config={'displayModeBar': True, 'displaylogo': False}
                            ),
                            type="circle"
                        )
                    ])
                ])
            ], width=6)
        ], className="mb-3"),
        dbc.Row([
            # Bottom row
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Performance Statistics", className="mb-0"),
                        dbc.Badge("Real-time", color="success", className="ms-2")
                    ]),
                    dbc.CardBody([
                        html.Div(id="statistics-panel", style={'height': f'{CHART_HEIGHT}px', 'overflow-y': 'auto'})
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Event Log", className="mb-0"),
                        dbc.Badge(id="event-count-badge", children="0 Events", color="secondary", className="ms-2")
                    ]),
                    dbc.CardBody([
                        html.Div(id="event-log-panel", style={'height': f'{CHART_HEIGHT}px', 'overflow-y': 'auto'})
                    ])
                ])
            ], width=6)
        ])
    ])

def create_volatility_regimes_panels():
    """Create the volatility regimes layout with calendar and analysis"""
    return html.Div([
        dbc.Row([
            # Top row - Calendar and regime overview
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Volatility Regime Calendar", className="mb-0"),
                        dbc.Badge("Historical", color="info", className="ms-2")
                    ]),
                    dbc.CardBody([
                        html.Div([
                            html.P("Interactive calendar showing daily volatility regimes", className="text-muted mb-3"),
                            html.Div(id="volatility-calendar", style={'height': f'{CHART_HEIGHT}px'})
                        ])
                    ])
                ])
            ], width=8),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Regime Statistics", className="mb-0"),
                        dbc.Badge("Analysis", color="warning", className="ms-2")
                    ]),
                    dbc.CardBody([
                        html.Div(id="regime-statistics-panel", style={'height': f'{CHART_HEIGHT}px', 'overflow-y': 'auto'})
                    ])
                ])
            ], width=4)
        ], className="mb-3"),
        dbc.Row([
            # Bottom row - Regime transitions (full width)
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Regime Transitions", className="mb-0"),
                        dbc.Badge("Flow", color="danger", className="ms-2")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            dcc.Graph(
                                id="regime-transition-chart",
                                style={'height': f'{CHART_HEIGHT}px'},
                                config={'displayModeBar': True, 'displaylogo': False}
                            ),
                            type="circle"
                        )
                    ])
                ])
            ], width=12)
        ])
    ])

def create_advanced_analytics_panels():
    """Create advanced analytics dashboard panels"""
    return html.Div([
        # Analytics Overview Row
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("📊 Clustering Quality Metrics", className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="clustering-metrics-content")
                    ])
                ], className="mb-3")
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("🎯 Market Regime Classification", className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="regime-classification-content")
                    ])
                ], className="mb-3")
            ], width=6)
        ]),

        # Advanced Events and Alerts Row
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("🚨 Real-time Alerts & Events", className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="alerts-events-content")
                    ])
                ], className="mb-3")
            ], width=8),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("📈 Performance Trends", className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="performance-trends-content")
                    ])
                ], className="mb-3")
            ], width=4)
        ]),

        # Portfolio Analytics Row
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("📈 Portfolio Analytics & MPT Integration", className="mb-0"),
                        dbc.Badge("Live Analytics", color="success", className="ms-2")
                    ]),
                    dbc.CardBody([
                        html.Div(id="portfolio-analytics-content")
                    ])
                ], className="mb-3")
            ], width=12)
        ]),

        # Export and Configuration Row
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("💾 Data Export & Configuration", className="mb-0")
                    ]),
                    dbc.CardBody([
                        html.Div(id="export-config-content")
                    ])
                ], className="mb-3")
            ], width=12)
        ])
    ])

def create_main_panels():
    """Create the tabbed main dashboard layout"""
    return dcc.Tabs(
        id="main-tabs",
        value="correlation-clustering",
        children=[
            dcc.Tab(
                label="Correlation Clustering",
                value="correlation-clustering",
                children=[
                    html.Div([
                        create_correlation_clustering_panels()
                    ], style={'padding': '20px 0'})
                ],
                style={'backgroundColor': '#343a40', 'color': '#e1e5e9'},
                selected_style={'backgroundColor': '#495057', 'color': '#ffffff', 'borderTop': '3px solid #007bff'}
            ),
            dcc.Tab(
                label="Volatility Regimes",
                value="volatility-regimes",
                children=[
                    html.Div([
                        create_volatility_regimes_panels()
                    ], style={'padding': '20px 0'})
                ],
                style={'backgroundColor': '#343a40', 'color': '#e1e5e9'},
                selected_style={'backgroundColor': '#495057', 'color': '#ffffff', 'borderTop': '3px solid #007bff'}
            ),
            dcc.Tab(
                label="Advanced Analytics",
                value="advanced-analytics",
                children=[
                    html.Div([
                        create_advanced_analytics_panels()
                    ], style={'padding': '20px 0'})
                ],
                style={'backgroundColor': '#343a40', 'color': '#e1e5e9'},
                selected_style={'backgroundColor': '#495057', 'color': '#ffffff', 'borderTop': '3px solid #007bff'}
            )
        ],
        style={
            'backgroundColor': '#212529',
            'borderBottom': '1px solid #495057'
        },
        className="mb-3"
    )

# =============================================================================
# MAIN LAYOUT
# =============================================================================

app.layout = dbc.Container([
    # Header
    create_header(),

    # Control Panel
    create_control_panel(),

    # Main Panels (Fixed function)
    create_main_panels(),

    # Event Comparison Modal
    dbc.Modal([
        dbc.ModalHeader(dbc.ModalTitle("Event Comparison: Before vs After")),
        dbc.ModalBody([
            html.Div(id="event-comparison-content")
        ]),
        dbc.ModalFooter([
            dbc.Button("Close", id="close-event-modal", className="ms-auto", n_clicks=0)
        ])
    ], id="event-comparison-modal", is_open=False, size="xl"),

    # Hidden components for data storage
    dcc.Store(id="clustering-data-store"),
    dcc.Store(id="dashboard-state-store", data=dashboard_state),
    dcc.Store(id="selected-cluster-store", data=None),
    dcc.Store(id="selected-event-store", data=None),
    dcc.Store(id="time-navigation-store", data={'mode': 'live', 'selected_time': None, 'time_range': None}),
    dcc.Store(id="tab-state-store", data={'active_tab': 'correlation-clustering'}),
    dcc.Store(id="volatility-regime-store", data={}),  # Shared volatility regime data store


    # Interval components
    dcc.Interval(
        id="data-update-interval",
        interval=DATA_UPDATE_INTERVAL,
        n_intervals=0,
        disabled=False
    ),
    dcc.Interval(
        id="chart-update-interval",
        interval=CHART_UPDATE_INTERVAL,
        n_intervals=0,
        disabled=False
    )
], fluid=True)

# =============================================================================
# CALLBACK FUNCTIONS
# =============================================================================

# Time Navigation Callbacks
@app.callback(
    [Output("time-scrubber-container", "style"),
     Output("btn-live-mode", "active"),
     Output("btn-historical-mode", "active"),
     Output("time-navigation-store", "data")],
    [Input("btn-live-mode", "n_clicks"),
     Input("btn-historical-mode", "n_clicks")],
    [State("time-navigation-store", "data")]
)
def toggle_time_navigation_mode(live_clicks, historical_clicks, nav_data):
    """Toggle between live and historical navigation modes"""
    ctx = callback_context

    # Initialize nav_data if None
    if nav_data is None:
        nav_data = {'mode': 'live', 'selected_time': None, 'time_range': None}

    if not ctx.triggered:
        return {'display': 'none'}, True, False, nav_data

    button_id = ctx.triggered[0]['prop_id'].split('.')[0]

    if button_id == "btn-live-mode":
        # Switch to live mode
        nav_data['mode'] = 'live'
        nav_data['selected_time'] = None
        return {'display': 'none'}, True, False, nav_data
    elif button_id == "btn-historical-mode":
        # Switch to historical mode
        nav_data['mode'] = 'historical'
        return {'display': 'block'}, False, True, nav_data

    logger.warning(f"Unknown button clicked: {button_id}")
    return {'display': 'none'}, True, False, nav_data


@app.callback(
    [Output("time-scrubber", "min"),
     Output("time-scrubber", "max"),
     Output("time-scrubber", "value"),
     Output("time-scrubber", "marks"),
     Output("time-scrubber", "disabled"),
     Output("time-scrubber-start", "children"),
     Output("time-scrubber-end", "children")],
    [Input("time-navigation-store", "data"),
     Input("clustering-data-store", "data")],
    [State("timeframe-dropdown", "value")]
)
def update_time_scrubber(nav_data, clustering_data, timeframe):
    """Update time scrubber range and marks based on available historical data"""

    if not nav_data or nav_data.get('mode') != 'historical':
        # Return disabled state for live mode
        return 0, 100, [90, 100], {}, True, "", ""

    try:
        # Get historical states from state manager
        state_history = clustering_engine.state_manager.get_state_history(limit=200)

        if len(state_history) < 2:
            return 0, 100, [90, 100], {}, True, "No historical data", "No historical data"

        # Sort by timestamp
        state_history.sort(key=lambda x: x.timestamp)

        # Create time range
        start_time = state_history[0].timestamp
        end_time = state_history[-1].timestamp

        # Calculate time steps (use indices for simplicity)
        min_val = 0
        max_val = len(state_history) - 1

        # Default to showing recent data (last 20% of available data)
        recent_start = max(0, int(max_val * 0.8))
        current_value = [recent_start, max_val]

        # Create marks for key time points
        marks = {}
        step = max(1, max_val // 10)  # Show ~10 marks
        for i in range(0, max_val + 1, step):
            if i < len(state_history):
                timestamp = state_history[i].timestamp
                marks[i] = {
                    'label': timestamp.strftime('%H:%M'),
                    'style': {'color': '#6c757d', 'fontSize': '10px'}
                }

        # Add end mark
        if max_val not in marks:
            marks[max_val] = {
                'label': end_time.strftime('%H:%M'),
                'style': {'color': '#6c757d', 'fontSize': '10px'}
            }

        # Format start/end labels
        start_label = start_time.strftime('%Y-%m-%d %H:%M')
        end_label = end_time.strftime('%Y-%m-%d %H:%M')

        return min_val, max_val, current_value, marks, False, start_label, end_label

    except Exception as e:
        logger.error(f"Error updating time scrubber: {str(e)}")
        return 0, 100, [90, 100], {}, True, "Error loading data", "Error loading data"


@app.callback(
    Output("time-scrubber-current", "children"),
    [Input("time-scrubber", "value")],
    [State("time-navigation-store", "data")]
)
def update_time_scrubber_current_display(scrubber_value, nav_data):
    """Update the current time display for the scrubber"""

    if not nav_data or nav_data.get('mode') != 'historical' or not scrubber_value:
        return ""

    try:
        state_history = clustering_engine.state_manager.get_state_history(limit=200)
        if not state_history:
            return ""

        state_history.sort(key=lambda x: x.timestamp)

        # Get the selected time range
        start_idx, end_idx = scrubber_value
        start_idx = max(0, min(start_idx, len(state_history) - 1))
        end_idx = max(0, min(end_idx, len(state_history) - 1))

        if start_idx == end_idx:
            selected_time = state_history[start_idx].timestamp
            return f"Selected: {selected_time.strftime('%H:%M:%S')}"
        else:
            start_time = state_history[start_idx].timestamp
            end_time = state_history[end_idx].timestamp
            return f"Range: {start_time.strftime('%H:%M')} - {end_time.strftime('%H:%M')}"

    except Exception as e:
        logger.error(f"Error updating time display: {str(e)}")
        return "Error"

@app.callback(
    [Output("status-badge", "children"),
     Output("status-badge", "color"),
     Output("btn-connect", "disabled"),
     Output("btn-disconnect", "disabled"),
     Output("last-update-text", "children")],
    [Input("btn-connect", "n_clicks"),
     Input("btn-disconnect", "n_clicks"),
     Input("data-update-interval", "n_intervals")],
    [State("dashboard-state-store", "data")]
)
def update_connection_status(connect_clicks, disconnect_clicks, n_intervals, state_data):
    """Update connection status and control buttons"""
    ctx = callback_context

    if not ctx.triggered:
        # Check actual connection status on initial load
        if dashboard_state['connection_status'] == 'connected':
            return "Connected", "success", True, False, "Auto-connected on startup"
        else:
            return "Disconnected", "danger", False, True, "Never updated"

    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]

    if trigger_id == "btn-connect":
        try:
            if clustering_engine.connect():
                dashboard_state['connection_status'] = 'connected'
                return "Connected", "success", True, False, f"Connected at {datetime.now().strftime('%H:%M:%S')}"
            else:
                return "Connection Failed", "warning", False, True, "Connection attempt failed"
        except Exception as e:
            logger.error(f"Connection error: {str(e)}")
            return "Error", "danger", False, True, f"Error: {str(e)[:50]}"

    elif trigger_id == "btn-disconnect":
        try:
            clustering_engine.disconnect()
            dashboard_state['connection_status'] = 'disconnected'
            return "Disconnected", "danger", False, True, f"Disconnected at {datetime.now().strftime('%H:%M:%S')}"
        except Exception as e:
            logger.error(f"Disconnection error: {str(e)}")
            return "Error", "danger", False, True, f"Error: {str(e)[:50]}"

    # Regular status check
    if dashboard_state['connection_status'] == 'connected':
        last_update = dashboard_state.get('last_update')
        if last_update:
            return "Connected", "success", True, False, f"Last update: {last_update}"
        else:
            return "Connected", "success", True, False, "Connected - No data yet"
    else:
        return "Disconnected", "danger", False, True, "Not connected"


@app.callback(
    [Output("clustering-data-store", "data"),
     Output("cluster-count-display", "children"),
     Output("data-quality-display", "children"),
     Output("stability-display", "children")],
    [Input("data-update-interval", "n_intervals"),
     Input("btn-refresh", "n_clicks")],
    [State("auto-update-switch", "value"),
     State("timeframe-dropdown", "value")]
)
def update_clustering_data(n_intervals, refresh_clicks, auto_update, timeframe):
    """Fetch and update clustering data"""
    ctx = callback_context

    # Skip update if auto-update is disabled and not a manual refresh
    if not auto_update and not (ctx.triggered and 'btn-refresh' in ctx.triggered[0]['prop_id']):
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # Skip if not connected
    if dashboard_state['connection_status'] != 'connected':
        return {}, "0", "0.0%", "0.0%"

    try:
        # Parse timeframe - special handling for 24h (since 00:00)
        if timeframe == '24h':
            # Calculate hours since 00:00 today
            now = datetime.now(MARKET_TIMEZONE)
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            timeframe_hours = int((now - today_start).total_seconds() / 3600)
            # Ensure minimum of 1 hour for data availability
            timeframe_hours = max(1, timeframe_hours)
        else:
            timeframe_hours = {
                '1h': 1, '6h': 6, '3d': 72, '7d': 168
            }.get(timeframe, 24)

        # Run clustering analysis
        event = clustering_engine.run_clustering_analysis(
            hours_back=timeframe_hours,
            use_weekend_fallback=True
        )

        # Generate historical states for Sankey if we don't have enough
        if len(clustering_engine.state_manager.get_state_history()) < 3:
            _generate_historical_states_for_demo(clustering_engine, timeframe_hours)

        # Get current status
        status = clustering_engine.get_current_status()
        current_state = clustering_engine.state_manager.get_current_state()

        # Update dashboard state
        dashboard_state['last_update'] = datetime.now().strftime('%H:%M:%S')
        dashboard_state['update_count'] += 1

        # Prepare data for storage
        clustering_data = {
            'status': status,
            'current_state': {
                'timestamp': current_state.timestamp.isoformat() if current_state else None,
                'cluster_count': current_state.cluster_count if current_state else 0,
                'cluster_assignments': current_state.cluster_assignments if current_state else [],
                'symbols': current_state.symbols if current_state else [],
                'regime_stability': current_state.regime_stability if current_state else 0.0,
                'data_quality_score': current_state.data_quality_score if current_state else 0.0,
                'correlation_matrix': current_state.correlation_matrix.tolist() if current_state and current_state.correlation_matrix is not None else []
            } if current_state else None,
            'recent_events': [
                {
                    'timestamp': event.timestamp.isoformat(),
                    'event_type': event.event_type,
                    'description': event.description,
                    'significance_score': event.significance_score,
                    'affected_pairs': event.affected_pairs
                }
                for event in clustering_engine.state_manager.get_recent_events(hours_back=timeframe_hours)
            ],
            'last_update': dashboard_state['last_update']
        }

        # Format display values
        cluster_count = current_state.cluster_count if current_state else 0
        data_quality = f"{(current_state.data_quality_score * 100):.1f}%" if current_state else "0.0%"
        stability = f"{(current_state.regime_stability * 100):.1f}%" if current_state else "0.0%"

        logger.info(f"Data updated: {cluster_count} clusters, quality={data_quality}, stability={stability}")

        return clustering_data, str(cluster_count), data_quality, stability

    except Exception as e:
        logger.error(f"Error updating clustering data: {str(e)}")
        return {}, "Error", "Error", "Error"


@app.callback(
    Output("dendrogram-chart", "figure"),
    [Input("clustering-data-store", "data"),
     Input("chart-update-interval", "n_intervals"),
     Input("time-scrubber", "value"),
     Input("time-navigation-store", "data")]
)
def update_dendrogram(clustering_data, n_intervals, scrubber_value, nav_data):
    """Update dendrogram visualization with enhanced hierarchical clustering"""

    # Determine data source based on navigation mode
    if nav_data and nav_data.get('mode') == 'historical' and scrubber_value:
        # Historical mode - get data from selected time
        try:
            state_history = clustering_engine.state_manager.get_state_history(limit=200)
            if state_history:
                state_history.sort(key=lambda x: x.timestamp)

                # Get selected time index (use end of range for single point)
                selected_idx = scrubber_value[1] if isinstance(scrubber_value, list) else scrubber_value
                selected_idx = max(0, min(selected_idx, len(state_history) - 1))

                selected_state = state_history[selected_idx]

                # Convert to format expected by visualization
                current_state = {
                    'symbols': selected_state.symbols,
                    'cluster_assignments': selected_state.cluster_assignments,
                    'correlation_matrix': selected_state.correlation_matrix.tolist(),
                    'timestamp': selected_state.timestamp.isoformat(),
                    'cluster_count': selected_state.cluster_count
                }

                title_suffix = f" (Historical: {selected_state.timestamp.strftime('%H:%M:%S')})"
            else:
                # No historical data available
                return create_cluster_scatter_plot(
                    symbols=[],
                    cluster_assignments=[],
                    title="Dendrogram - No Historical Data Available",
                    height=CHART_HEIGHT
                )
        except Exception as e:
            logger.error(f"Error accessing historical data: {str(e)}")
            # Fall back to current data
            if not clustering_data or not clustering_data.get('current_state'):
                return create_cluster_scatter_plot(
                    symbols=[],
                    cluster_assignments=[],
                    title="Dendrogram - No Data Available",
                    height=CHART_HEIGHT
                )
            current_state = clustering_data['current_state']
            title_suffix = " (Live - Historical Error)"
    else:
        # Live mode - use current clustering data
        if not clustering_data or not clustering_data.get('current_state'):
            return create_cluster_scatter_plot(
                symbols=[],
                cluster_assignments=[],
                title="Dendrogram - No Data Available",
                height=CHART_HEIGHT
            )
        current_state = clustering_data['current_state']
        title_suffix = " (Live)"

    try:
        symbols = current_state.get('symbols', [])
        cluster_assignments = current_state.get('cluster_assignments', [])
        correlation_matrix = np.array(current_state.get('correlation_matrix', []))

        if len(symbols) == 0 or len(correlation_matrix) == 0:
            return create_cluster_scatter_plot(
                symbols=[],
                cluster_assignments=[],
                title="Dendrogram - Insufficient Data",
                height=CHART_HEIGHT
            )

        # Get linkage matrix if available from clustering engine
        linkage_matrix = None
        if RUST_AVAILABLE and hasattr(clustering_engine, 'last_linkage_matrix'):
            linkage_matrix = getattr(clustering_engine, 'last_linkage_matrix', None)

        # Try to create interactive dendrogram first
        try:
            if len(symbols) >= 3 and correlation_matrix.shape[0] >= 3:
                # Create true dendrogram visualization
                fig = create_interactive_dendrogram(
                    correlation_matrix=correlation_matrix,
                    symbols=symbols,
                    linkage_matrix=linkage_matrix,
                    cluster_assignments=cluster_assignments,
                    title=f"Currency Clustering Dendrogram ({len(set(cluster_assignments))} clusters){title_suffix}",
                    height=CHART_HEIGHT
                )

                # Add cluster statistics as annotations
                if len(cluster_assignments) > 0:
                    cluster_stats = calculate_cluster_statistics(symbols, cluster_assignments, correlation_matrix)
                    stats_text = []
                    for cluster_id, stats in cluster_stats.items():
                        stats_text.append(f"Cluster {cluster_id}: {stats['size']} pairs (avg corr: {stats['avg_correlation']:.2f})")

                    if stats_text:
                        fig.add_annotation(
                            text="<br>".join(stats_text[:5]),  # Show first 5 clusters
                            xref="paper", yref="paper",
                            x=0.02, y=0.98,
                            xanchor='left', yanchor='top',
                            showarrow=False,
                            font=dict(size=9, color="lightgray"),
                            bgcolor="rgba(0,0,0,0.5)",
                            bordercolor="gray",
                            borderwidth=1
                        )

                # Force remove legend
                fig.update_layout(showlegend=False)
                for trace in fig.data:
                    trace.showlegend = False
                return fig

        except Exception as dendrogram_error:
            logger.warning(f"Could not create dendrogram, falling back to scatter plot: {dendrogram_error}")

        # Fallback to cluster scatter plot
        fig = create_cluster_scatter_plot(
            symbols=symbols,
            cluster_assignments=cluster_assignments,
            correlation_matrix=correlation_matrix,
            title=f"Currency Pair Clusters ({len(set(cluster_assignments))} clusters)",
            height=CHART_HEIGHT
        )

        # Force remove legend
        fig.update_layout(showlegend=False)
        for trace in fig.data:
            trace.showlegend = False
        return fig

    except Exception as e:
        logger.error(f"Error creating dendrogram: {str(e)}")
        return create_cluster_scatter_plot(
            symbols=[],
            cluster_assignments=[],
            title=f"Dendrogram - Error: {str(e)[:50]}",
            height=CHART_HEIGHT
        )


@app.callback(
    Output("sankey-chart", "figure"),
    [Input("clustering-data-store", "data"),
     Input("timeframe-dropdown", "value"),
     Input("time-scrubber", "value"),
     Input("time-navigation-store", "data")]
)
def update_sankey_diagram(clustering_data, timeframe, scrubber_value, nav_data):
    """Update Sankey diagram showing cluster evolution over time"""
    if not clustering_data:
        fig = go.Figure()
        fig.update_layout(
            title="Cluster Evolution - No Data Available",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            font=dict(color="white")
        )
        return fig

    try:
        # Determine data source and time range based on navigation mode
        if nav_data and nav_data.get('mode') == 'historical' and scrubber_value:
            # Historical mode - use selected time range from scrubber
            state_history = clustering_engine.state_manager.get_state_history(limit=200)
            if not state_history:
                fig = go.Figure()
                fig.update_layout(
                    title="Cluster Evolution - No Historical Data",
                    template=CHART_THEME,
                    height=CHART_HEIGHT,
                    paper_bgcolor="rgba(0,0,0,0)",
                    plot_bgcolor="rgba(0,0,0,0)",
                    font=dict(color="white")
                )
                return fig

            state_history.sort(key=lambda x: x.timestamp)

            # Get selected time range
            start_idx, end_idx = scrubber_value if isinstance(scrubber_value, list) else [scrubber_value, scrubber_value]
            start_idx = max(0, min(start_idx, len(state_history) - 1))
            end_idx = max(start_idx, min(end_idx, len(state_history) - 1))

            # Use selected range for evolution analysis
            selected_states = state_history[start_idx:end_idx + 1]
            title_suffix = f" (Historical: {selected_states[0].timestamp.strftime('%H:%M')} - {selected_states[-1].timestamp.strftime('%H:%M')})"

        else:
            # Live mode - use timeframe-based filtering
            # Get timeframe parameters - special handling for 24h (since 00:00)
            if timeframe == '24h':
                # Calculate hours since 00:00 today
                now = datetime.now(MARKET_TIMEZONE)
                today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
                timeframe_hours = int((now - today_start).total_seconds() / 3600)
                # Ensure minimum of 1 hour for data availability
                timeframe_hours = max(1, timeframe_hours)
            else:
                timeframe_hours = {
                    '1h': 1, '6h': 6, '3d': 72, '7d': 168
                }.get(timeframe, 24)

            # Get historical states from state manager
            state_history = clustering_engine.state_manager.get_state_history(limit=200)

            # Filter by timeframe
            cutoff_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=timeframe_hours)
            selected_states = [state for state in state_history if state.timestamp >= cutoff_time]
            title_suffix = f" (Live - {timeframe})"

        if len(selected_states) < 3:
            # Not enough historical data - create informational chart
            fig = go.Figure()
            fig.add_annotation(
                text=f"Collecting data...<br>Need at least 3 states for evolution analysis<br>Current: {len(selected_states)} states",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False,
                font=dict(size=16, color="white"),
                bgcolor="rgba(255,255,255,0.1)",
                bordercolor="white",
                borderwidth=1
            )
            fig.update_layout(
                title=f"Cluster Evolution - Collecting Data{title_suffix}",
                template=CHART_THEME,
                height=CHART_HEIGHT,
                paper_bgcolor="rgba(0,0,0,0)",
                plot_bgcolor="rgba(0,0,0,0)",
                font=dict(color="white")
            )
            return fig

        # Use selected states for evolution analysis
        recent_states = selected_states[-10:] if len(selected_states) > 10 else selected_states

        # Extract cluster evolution data
        # Adjust time window based on mode
        if nav_data and nav_data.get('mode') == 'historical':
            # For historical mode, use smaller windows to capture selected range
            time_window_minutes = max(30, len(selected_states) * 5)
        else:
            # For live mode, use timeframe-based windows
            timeframe_hours = {
                '1h': 1, '6h': 6, '24h': 24, '3d': 72, '7d': 168
            }.get(timeframe, 24)
            time_window_minutes = max(120, timeframe_hours * 60 // 4)

        evolution_data = extract_cluster_evolution_data(
            recent_states,
            time_window_minutes=time_window_minutes,
            min_states=2
        )

        if evolution_data is None:
            # Fallback to timeline chart if Sankey data insufficient
            logger.info("Insufficient data for Sankey, creating timeline chart")
            fig = create_cluster_timeline_chart(
                {'time_windows': [{'window_start': s.timestamp, 'cluster_count': s.cluster_count,
                                  'avg_stability': s.regime_stability, 'avg_quality': s.data_quality_score}
                                 for s in recent_states]},
                title=f"Cluster Timeline{title_suffix}",
                height=CHART_HEIGHT
            )
            # Force remove legend
            fig.update_layout(showlegend=False)
            for trace in fig.data:
                trace.showlegend = False
            return fig

        # Create enhanced Sankey diagram
        fig = create_cluster_evolution_sankey(
            evolution_data,
            title=f"Currency Cluster Evolution{title_suffix}",
            height=CHART_HEIGHT,
            width=CHART_WIDTH
        )

        # Add evolution summary as annotation
        summary = get_cluster_evolution_summary(evolution_data)
        summary_text = (
            f"Windows: {summary.get('time_windows', 0)} | "
            f"Avg Clusters: {summary.get('avg_cluster_count', 0):.1f} | "
            f"Transitions: {summary.get('total_transitions', 0)}"
        )

        fig.add_annotation(
            text=summary_text,
            xref="paper", yref="paper",
            x=0.02, y=0.98, xanchor='left', yanchor='top',
            showarrow=False,
            font=dict(size=10, color="white"),
            bgcolor="rgba(0,0,0,0.7)",
            bordercolor="white",
            borderwidth=1
        )

        # Force remove legend (Sankey traces don't support showlegend property)
        fig.update_layout(showlegend=False)
        for trace in fig.data:
            if hasattr(trace, 'showlegend'):
                trace.showlegend = False
        return fig

    except Exception as e:
        logger.error(f"Error creating Sankey diagram: {str(e)}")
        fig = go.Figure()
        fig.update_layout(
            title=f"Cluster Evolution - Error: {str(e)[:50]}",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            font=dict(color="white")
        )
        return fig


@app.callback(
    Output("selected-cluster-store", "data"),
    [Input("dendrogram-chart", "clickData"),
     Input("sankey-chart", "clickData")],
    [State("clustering-data-store", "data")]
)
def handle_cluster_selection(dendrogram_click, sankey_click, clustering_data):
    """Handle cluster selection from dendrogram or Sankey diagram clicks"""
    ctx = callback_context

    if not ctx.triggered or not clustering_data:
        logger.debug("No trigger or clustering data in cluster selection")
        return None

    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
    logger.info(f"Cluster selection triggered by: {trigger_id}")
    logger.debug(f"Dendrogram click: {dendrogram_click}")
    logger.debug(f"Sankey click: {sankey_click}")

    try:
        selected_cluster = None

        if trigger_id == "dendrogram-chart" and dendrogram_click:
            # Extract cluster information from dendrogram click
            logger.info(f"Dendrogram click data: {dendrogram_click}")
            point_data = dendrogram_click['points'][0]
            logger.info(f"Point data: {point_data}")

            # For dendrogram, we need to map the clicked point to a cluster
            # This requires the current cluster assignments
            current_state = clustering_data.get('current_state', {})
            symbols = current_state.get('symbols', [])
            cluster_assignments = current_state.get('cluster_assignments', [])
            logger.info(f"Available symbols: {len(symbols)}, cluster assignments: {len(cluster_assignments)}")

            if 'text' in point_data:
                # Find the symbol that was clicked (from symbol scatter trace)
                clicked_symbol = point_data['text']
                logger.info(f"Clicked symbol: {clicked_symbol}")
                if clicked_symbol in symbols:
                    symbol_index = symbols.index(clicked_symbol)
                    if symbol_index < len(cluster_assignments):
                        cluster_id = cluster_assignments[symbol_index]
                        selected_cluster = {
                            'cluster_id': cluster_id,
                            'source': 'dendrogram',
                            'clicked_symbol': clicked_symbol
                        }
                        logger.info(f"Selected cluster: {selected_cluster}")
                    else:
                        logger.warning(f"Symbol index {symbol_index} out of range for cluster assignments")
                else:
                    logger.warning(f"Clicked symbol '{clicked_symbol}' not found in symbols list")
            elif 'x' in point_data:
                # Handle clicks on dendrogram tree structure
                x_pos = point_data['x']
                y_pos = point_data.get('y', 0)
                curve_number = point_data.get('curveNumber', -1)

                logger.info(f"Clicked on dendrogram at position ({x_pos}, {y_pos}), curve {curve_number}")

                # Check if this is a click on the symbol trace (bottom of dendrogram)
                if y_pos <= 0.1:  # Symbol trace is at y=0
                    # Map x position to symbol index for symbol trace clicks
                    n_symbols = len(symbols)
                    if n_symbols > 0 and 0 <= x_pos < n_symbols:
                        symbol_index = int(round(x_pos))
                        symbol_index = max(0, min(symbol_index, n_symbols - 1))

                        clicked_symbol = symbols[symbol_index]
                        if symbol_index < len(cluster_assignments):
                            cluster_id = cluster_assignments[symbol_index]
                            selected_cluster = {
                                'cluster_id': cluster_id,
                                'source': 'dendrogram',
                                'clicked_symbol': clicked_symbol
                            }
                            logger.info(f"Selected cluster {cluster_id} for symbol {clicked_symbol} at position {symbol_index}")
                        else:
                            logger.warning(f"Symbol index {symbol_index} out of range for cluster assignments")
                    else:
                        logger.warning(f"Invalid symbol position: {x_pos}")
                else:
                    # This is a click on a dendrogram node - select all symbols in that cluster
                    # For now, map to the nearest symbol and select its cluster
                    # TODO: Implement proper dendrogram node traversal for subtree selection
                    n_symbols = len(symbols)
                    if n_symbols > 0:
                        # Simple mapping for now - map x position to symbol range
                        symbol_index = int(round(x_pos * n_symbols / 270))  # 270 is typical dendrogram width
                        symbol_index = max(0, min(symbol_index, n_symbols - 1))

                        clicked_symbol = symbols[symbol_index]
                        if symbol_index < len(cluster_assignments):
                            cluster_id = cluster_assignments[symbol_index]
                            selected_cluster = {
                                'cluster_id': cluster_id,
                                'source': 'dendrogram_node',
                                'clicked_symbol': clicked_symbol,
                                'node_position': (x_pos, y_pos)
                            }
                            logger.info(f"Selected cluster {cluster_id} via dendrogram node at ({x_pos}, {y_pos})")
                        else:
                            logger.warning(f"Symbol index {symbol_index} out of range for cluster assignments")
                    else:
                        logger.warning(f"No symbols available for dendrogram node mapping")
            else:
                logger.warning(f"No 'text' or 'x' field in point data: {point_data.keys()}")

        elif trigger_id == "sankey-chart" and sankey_click:
            # Extract cluster information from Sankey click
            point_data = sankey_click['points'][0]

            # For Sankey, extract cluster ID from the node label
            if 'label' in point_data:
                label = point_data['label']
                # Parse cluster ID from label (format: "Cluster X (Y pairs)")
                import re
                cluster_match = re.search(r'Cluster (\d+)', label)
                if cluster_match:
                    selected_cluster = {
                        'cluster_id': int(cluster_match.group(1)),
                        'source': 'sankey',
                        'node_label': label
                    }

        return selected_cluster

    except Exception as e:
        logger.error(f"Error handling cluster selection: {str(e)}")
        return None


@app.callback(
    Output("statistics-panel", "children"),
    [Input("clustering-data-store", "data"),
     Input("selected-cluster-store", "data")]
)
def update_statistics_panel(clustering_data, selected_cluster):
    """Update statistics panel with cluster-specific or general performance statistics"""
    logger.info(f"Updating statistics panel - selected_cluster: {selected_cluster}")

    if not clustering_data:
        return html.Div([
            html.H6("No Statistics Available", className="text-muted"),
            html.P("Connect to data source to view performance metrics.")
        ])

    # If a cluster is selected, show cluster-specific statistics
    if selected_cluster and selected_cluster.get('cluster_id') is not None:
        logger.info(f"Showing cluster-specific statistics for cluster {selected_cluster.get('cluster_id')}")
        return _create_cluster_specific_statistics(clustering_data, selected_cluster)

    # Otherwise show general performance statistics
    logger.info("Showing general performance statistics")
    return _create_general_performance_statistics(clustering_data)


def _create_cluster_specific_statistics(clustering_data, selected_cluster):
    """Create cluster-specific statistics display"""
    try:
        logger.info(f"Creating cluster-specific statistics for cluster {selected_cluster.get('cluster_id')}")

        # Simple fallback for testing
        cluster_id = selected_cluster.get('cluster_id', 'Unknown')

        # Get cluster data from current state
        current_state = clustering_data.get('current_state')
        if not current_state:
            return html.Div([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5(f"Cluster {cluster_id} Selected", className="mb-0 text-light"),
                        dbc.Badge("Selected", color="primary", className="ms-2")
                    ], style={'backgroundColor': '#2c3e50'}),
                    dbc.CardBody([
                        html.H6("No Data Available", className="card-title text-warning"),
                        html.P("Cluster data is not available.", className="mb-1 text-light"),
                        html.P("Click elsewhere to return to overview.", className="mb-0 text-muted small")
                    ], style={'backgroundColor': '#34495e', 'color': 'white'})
                ], className="mb-3", style={'border': '1px solid #495057'})
            ])

        # Extract cluster information
        symbols = current_state.get('symbols', [])
        cluster_assignments = current_state.get('cluster_assignments', [])
        correlation_matrix = current_state.get('correlation_matrix', [])

        # Calculate comprehensive cluster statistics
        import numpy as np
        cluster_stats = {}
        cluster_symbols = []

        if len(symbols) == len(cluster_assignments) and len(correlation_matrix) > 0:
            # Use the existing calculate_cluster_statistics function
            all_cluster_stats = calculate_cluster_statistics(symbols, cluster_assignments, np.array(correlation_matrix))
            cluster_stats = all_cluster_stats.get(cluster_id, {})
            cluster_symbols = cluster_stats.get('symbols', [])

        cluster_size = len(cluster_symbols)

        return html.Div([
            dbc.Card([
                dbc.CardHeader([
                    html.H5(f"Cluster {cluster_id} Details", className="mb-0 text-light"),
                    dbc.Badge(f"{cluster_size} pairs", color="info", className="ms-2")
                ], style={'backgroundColor': '#2c3e50'}),
                dbc.CardBody([
                    # Cluster Members Table
                    html.H6("Cluster Members", className="card-title text-light mb-3"),
                    dbc.Table([
                        html.Thead([
                            html.Tr([
                                html.Th("Currency Pair", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                html.Th("Position", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                            ])
                        ]),
                        html.Tbody([
                            html.Tr([
                                html.Td(symbol, style={'color': 'white', 'fontWeight': '500'}),
                                html.Td(f"#{i+1}", style={'color': '#6c757d'}),
                            ]) for i, symbol in enumerate(cluster_symbols)
                        ])
                    ], bordered=True, hover=True, responsive=True,
                       style={'backgroundColor': '#495057', 'border': '1px solid #6c757d', 'color': 'white'}),

                    html.Hr(style={'borderColor': '#6c757d'}),

                    # Cluster Statistics
                    html.H6("Statistics", className="card-title text-light mb-3"),
                    dbc.Row([
                        dbc.Col([
                            dbc.Card([
                                dbc.CardBody([
                                    html.H4(str(cluster_size), className="text-info mb-0"),
                                    html.P("Currency Pairs", className="text-muted small mb-0")
                                ])
                            ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                        ], width=6),
                        dbc.Col([
                            dbc.Card([
                                dbc.CardBody([
                                    html.H4(f"{(cluster_size/len(symbols)*100):.1f}%", className="text-success mb-0"),
                                    html.P("of Total Pairs", className="text-muted small mb-0")
                                ])
                            ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                        ], width=6),
                    ], className="mb-3"),

                    # Correlation Statistics
                    _create_correlation_statistics_section(cluster_stats, cluster_size),

                    # Volatility Statistics (if available)
                    _create_volatility_statistics_section(current_state, cluster_symbols),

                    # Cluster Lifespan (placeholder for now)
                    _create_lifespan_statistics_section(current_state, cluster_id),

                    html.P("Click elsewhere to return to overview.", className="mb-0 text-muted small")
                ], style={'backgroundColor': '#34495e', 'color': 'white'})
            ], className="mb-3", style={'border': '1px solid #495057'})
        ])

        # Cluster Volatility Card
        if 'volatility' in current_state:
            volatility_data = current_state['volatility']
            cluster_volatilities = [volatility_data.get(symbol, 0) for symbol in stats['symbols']]

            if cluster_volatilities:
                avg_volatility = np.mean(cluster_volatilities)
                min_volatility = min(cluster_volatilities)
                max_volatility = max(cluster_volatilities)

                stats_cards.append(
                    dbc.Card([
                        dbc.CardBody([
                            html.H6("Cluster Volatility", className="card-title"),
                            html.P(f"Average Volatility: {avg_volatility:.4f}", className="mb-1"),
                            html.P(f"Volatility Range: {min_volatility:.4f} - {max_volatility:.4f}", className="mb-0")
                        ])
                    ], className="mb-3")
                )

        # Cluster Lifespan Card (placeholder for now)
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Cluster Stability", className="card-title"),
                    html.P("Lifespan tracking: Coming soon", className="mb-1 text-muted"),
                    html.P(f"Current stability: {current_state.get('regime_stability', 0):.1%}", className="mb-0")
                ])
            ], className="mb-3")
        )

        # Selection Info Card
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Selection Info", className="card-title"),
                    html.P(f"Selected from: {selected_cluster['source'].title()}", className="mb-1"),
                    html.P(f"Click anywhere to return to overview", className="mb-0 text-muted small")
                ])
            ], className="mb-3")
        )

        return html.Div(stats_cards)

    except Exception as e:
        logger.error(f"Error creating cluster-specific statistics: {str(e)}")
        return html.Div([
            html.H6("Statistics Error", className="text-danger"),
            html.P(f"Error loading cluster statistics: {str(e)[:100]}")
        ])


def _create_correlation_statistics_section(cluster_stats, cluster_size):
    """Create correlation statistics section with proper handling for single-member clusters."""
    if cluster_size <= 1:
        # Single member cluster - no meaningful correlation statistics
        return dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4("N/A", className="text-info mb-0"),
                        html.P("Single Member", className="text-muted small mb-0"),
                        html.P("No intra-cluster correlation for single-member clusters", className="text-muted small")
                    ])
                ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
            ], width=12),
        ], className="mb-3")
    else:
        # Multi-member cluster - show correlation statistics
        avg_corr = cluster_stats.get('avg_correlation', 0)
        min_corr = cluster_stats.get('min_correlation', 0)
        max_corr = cluster_stats.get('max_correlation', 0)

        return dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4(f"{avg_corr:.3f}", className="text-warning mb-0"),
                        html.P("Avg Correlation", className="text-muted small mb-0")
                    ])
                ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
            ], width=4),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4(f"{min_corr:.3f}", className="text-danger mb-0"),
                        html.P("Min Correlation", className="text-muted small mb-0")
                    ])
                ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
            ], width=4),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4(f"{max_corr:.3f}", className="text-success mb-0"),
                        html.P("Max Correlation", className="text-muted small mb-0")
                    ])
                ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
            ], width=4),
        ], className="mb-3")


def _create_volatility_statistics_section(current_state, cluster_symbols):
    """Create volatility statistics section for cluster"""
    try:
        volatility_profiles = current_state.get('volatility_profiles', {})

        if not volatility_profiles or not cluster_symbols:
            return html.Div()  # Return empty div if no data

        # Calculate cluster volatility statistics
        cluster_volatilities = []
        for symbol in cluster_symbols:
            if symbol in volatility_profiles:
                cluster_volatilities.append(volatility_profiles[symbol])

        if not cluster_volatilities:
            return html.Div()

        avg_volatility = np.mean(cluster_volatilities)
        min_volatility = min(cluster_volatilities)
        max_volatility = max(cluster_volatilities)

        return html.Div([
            html.H6("Volatility Analysis", className="card-title text-light mb-3"),
            dbc.Row([
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4(f"{avg_volatility:.4f}", className="text-warning mb-0"),
                            html.P("Avg Volatility", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=4),
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4(f"{min_volatility:.4f}", className="text-success mb-0"),
                            html.P("Min Volatility", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=4),
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4(f"{max_volatility:.4f}", className="text-danger mb-0"),
                            html.P("Max Volatility", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=4),
            ], className="mb-3"),
        ])

    except Exception as e:
        logger.error(f"Error creating volatility statistics: {str(e)}")
        return html.Div()


def _create_lifespan_statistics_section(current_state, cluster_id):
    """Create lifespan statistics section for cluster"""
    try:
        # For now, show placeholder information
        # TODO: Implement proper cluster lifespan tracking
        regime_stability = current_state.get('regime_stability', 0.0)
        last_update = current_state.get('timestamp', 'Unknown')

        return html.Div([
            html.H6("Cluster Stability", className="card-title text-light mb-3"),
            dbc.Row([
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4(f"{regime_stability:.1%}", className="text-info mb-0"),
                            html.P("Stability Score", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=6),
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4("Coming Soon", className="text-muted mb-0"),
                            html.P("Lifespan Tracking", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=6),
            ], className="mb-3"),
        ])

    except Exception as e:
        logger.error(f"Error creating lifespan statistics: {str(e)}")
        return html.Div()


def _create_general_performance_statistics(clustering_data):
    """Create general performance statistics display"""
    try:
        status = clustering_data.get('status', {})
        current_state = clustering_data.get('current_state')

        # Create statistics cards
        stats_cards = []

        # Engine Statistics
        stats_cards.append(
            dbc.Card([
                dbc.CardHeader([
                    html.H6("Engine Status", className="mb-0 text-light")
                ], style={'backgroundColor': '#2c3e50'}),
                dbc.CardBody([
                    dbc.Table([
                        html.Tbody([
                            html.Tr([
                                html.Td("Status:", style={'color': '#17a2b8', 'fontWeight': 'bold', 'width': '40%'}),
                                html.Td(status.get('engine_status', 'Unknown'), style={'color': 'white'})
                            ]),
                            html.Tr([
                                html.Td("Updates:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                html.Td(str(status.get('update_count', 0)), style={'color': 'white'})
                            ]),
                            html.Tr([
                                html.Td("Errors:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                html.Td(str(status.get('error_count', 0)), style={'color': 'white'})
                            ]),
                            html.Tr([
                                html.Td("Error Rate:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                html.Td(f"{status.get('error_rate', 0):.1%}", style={'color': 'white'})
                            ])
                        ])
                    ], borderless=True, style={'marginBottom': '0'})
                ], style={'backgroundColor': '#34495e', 'color': 'white'})
            ], className="mb-3", style={'border': '1px solid #495057'})
        )

        # Current State Statistics
        if current_state:
            stats_cards.append(
                dbc.Card([
                    dbc.CardHeader([
                        html.H6("Current State", className="mb-0 text-light")
                    ], style={'backgroundColor': '#2c3e50'}),
                    dbc.CardBody([
                        dbc.Table([
                            html.Tbody([
                                html.Tr([
                                    html.Td("Clusters:", style={'color': '#17a2b8', 'fontWeight': 'bold', 'width': '40%'}),
                                    html.Td(str(current_state.get('cluster_count', 0)), style={'color': 'white'})
                                ]),
                                html.Tr([
                                    html.Td("Symbols:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                    html.Td(str(len(current_state.get('symbols', []))), style={'color': 'white'})
                                ]),
                                html.Tr([
                                    html.Td("Data Quality:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                    html.Td(f"{current_state.get('data_quality_score', 0):.1%}", style={'color': 'white'})
                                ]),
                                html.Tr([
                                    html.Td("Stability:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                    html.Td(f"{current_state.get('regime_stability', 0):.1%}", style={'color': 'white'})
                                ])
                            ])
                        ], borderless=True, style={'marginBottom': '0'})
                    ], style={'backgroundColor': '#34495e', 'color': 'white'})
                ], className="mb-3", style={'border': '1px solid #495057'})
            )

        # Performance Metrics
        perf_metrics = status.get('performance_metrics', {})
        if perf_metrics:
            stats_cards.append(
                dbc.Card([
                    dbc.CardBody([
                        html.H6("Performance", className="card-title"),
                        html.P(f"Total Updates: {perf_metrics.get('total_updates', 0)}", className="mb-1"),
                        html.P(f"Events Detected: {perf_metrics.get('events_detected', 0)}", className="mb-1"),
                        html.P(f"Avg Processing: {perf_metrics.get('average_processing_time', 0):.3f}s", className="mb-1"),
                        html.P(f"Uptime: {perf_metrics.get('uptime_hours', 0):.1f}h", className="mb-0")
                    ])
                ], className="mb-3")
            )

        # Recent Activity
        recent_events = clustering_data.get('recent_events', [])
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Recent Activity", className="card-title"),
                    html.P(f"Events (24h): {len(recent_events)}", className="mb-1"),
                    html.P(f"Last Update: {clustering_data.get('last_update', 'Never')}", className="mb-0")
                ])
            ], className="mb-3")
        )

        return html.Div(stats_cards)

    except Exception as e:
        logger.error(f"Error updating statistics panel: {str(e)}")
        return html.Div([
            html.H6("Statistics Error", className="text-danger"),
            html.P(f"Error: {str(e)[:100]}")
        ])


@app.callback(
    Output("selected-cluster-store", "data", allow_duplicate=True),
    [Input("statistics-panel", "n_clicks")],
    prevent_initial_call=True
)
def clear_cluster_selection(n_clicks):
    """Clear cluster selection when clicking on statistics panel"""
    if n_clicks:
        return None
    return dash.no_update


@app.callback(
    [Output("event-log-panel", "children"),
     Output("event-count-badge", "children")],
    [Input("clustering-data-store", "data")]
)
def update_event_log(clustering_data):
    """Update event log panel"""
    if not clustering_data:
        return html.Div([
            html.H6("No Events", className="text-muted"),
            html.P("Connect to data source to view market events.")
        ]), "0 Events"

    try:
        recent_events = clustering_data.get('recent_events', [])

        if not recent_events:
            return html.Div([
                html.H6("No Recent Events", className="text-muted"),
                html.P("No market regime changes detected in the selected timeframe.")
            ]), "0 Events"

        # Create event cards
        event_cards = []

        # Reverse order to show newest events first, then take last 10
        reversed_events = list(reversed(recent_events[-10:]))  # Show last 10 events, newest first
        for event_index, event in enumerate(reversed_events):
            # Determine event color based on type
            event_colors = {
                'regime_change': 'danger',
                'volatility_spike': 'warning',
                'correlation_shift': 'info'
            }
            color = event_colors.get(event.get('event_type', ''), 'secondary')

            # Format timestamp
            timestamp = datetime.fromisoformat(event['timestamp'])
            time_str = timestamp.strftime('%H:%M:%S')
            date_str = timestamp.strftime('%Y-%m-%d')

            event_cards.append(
                html.Div([
                    dbc.Card([
                        dbc.CardBody([
                            html.Div([
                                dbc.Badge(event.get('event_type', 'unknown').replace('_', ' ').title(),
                                        color=color, className="me-2"),
                                html.Small(f"{date_str} {time_str}", className="text-muted float-end")
                            ], className="mb-2"),
                            html.P(event.get('description', 'No description'), className="mb-1"),
                            html.Small(
                                f"Significance: {event.get('significance_score', 0):.3f} | "
                                f"Affected pairs: {len(event.get('affected_pairs', []))}",
                                className="text-muted"
                            ),
                            html.Small("Click to view before/after comparison", className="text-info d-block mt-1")
                        ])
                    ], className="mb-2")
                ], className="event-card-clickable", style={"cursor": "pointer"},
                   id={"type": "event-card", "index": event_index})
            )

        event_log_content = html.Div(event_cards)
        event_count_text = f"{len(recent_events)} Events"

        return event_log_content, event_count_text

    except Exception as e:
        logger.error(f"Error updating event log: {str(e)}")
        return html.Div([
            html.H6("Event Log Error", className="text-danger"),
            html.P(f"Error: {str(e)[:100]}")
        ]), "Error"




@app.callback(
    [Output("event-comparison-modal", "is_open"),
     Output("event-comparison-content", "children"),
     Output("selected-event-store", "data")],
    [Input({"type": "event-card", "index": ALL}, "n_clicks"),
     Input("close-event-modal", "n_clicks")],
    [State("event-comparison-modal", "is_open"),
     State("clustering-data-store", "data")]
)
def handle_event_comparison_modal(event_clicks, close_clicks, is_open, clustering_data):
    """Handle event card clicks and modal display"""
    ctx = callback_context

    if not ctx.triggered:
        return False, html.Div(), None

    trigger_id = ctx.triggered[0]['prop_id']
    logger.info(f"Event modal callback triggered: {trigger_id}, event_clicks: {event_clicks}")

    # Close modal
    if 'close-event-modal' in trigger_id:
        return False, html.Div(), None

    # Event card clicked via pattern-matching
    if 'event-card' in trigger_id and event_clicks:
        try:
            # Extract the clicked index from the trigger_id
            import json
            trigger_data = json.loads(trigger_id.split('.')[0])
            clicked_index = trigger_data.get('index')

            if clicked_index is None:
                return False, html.Div(), None

            logger.info(f"Processing event index: {clicked_index}")

            # Get event data from state manager (with full ClusteringEvent objects)
            recent_events_full = clustering_engine.state_manager.get_recent_events(hours_back=24)
            if not recent_events_full:
                return False, html.Div("No events available"), None

            # Get the clicked event (accounting for reversed order in display)
            reversed_events = list(reversed(recent_events_full[-10:]))
            if clicked_index >= len(reversed_events):
                return False, html.Div("Event not found"), None

            selected_event = reversed_events[clicked_index]

            # Create before/after comparison content
            comparison_content = create_event_comparison_content(selected_event, clustering_data)

            # Convert event to serializable format for storage
            event_data = {
                'timestamp': selected_event.timestamp.isoformat(),
                'event_type': selected_event.event_type,
                'description': selected_event.description,
                'significance_score': selected_event.significance_score,
                'affected_pairs': selected_event.affected_pairs,
                'rand_index': selected_event.rand_index
            }

            return True, comparison_content, event_data

        except Exception as e:
            logger.error(f"Error handling event comparison: {str(e)}")
            return False, html.Div(f"Error: {str(e)}"), None

    return is_open, dash.no_update, dash.no_update


def create_event_comparison_content(event, clustering_data):
    """Create before/after comparison content for an event"""
    try:
        # Event details (now using ClusteringEvent object)
        timestamp = event.timestamp
        event_type = event.event_type
        description = event.description
        significance = event.significance_score
        affected_pairs = event.affected_pairs

        # Create event summary
        event_summary = dbc.Card([
            dbc.CardHeader(html.H5(f"{event_type.replace('_', ' ').title()} Event")),
            dbc.CardBody([
                html.P(f"Time: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}", className="mb-1"),
                html.P(f"Description: {description}", className="mb-1"),
                html.P(f"Significance Score: {significance:.3f}", className="mb-1"),
                html.P(f"Affected Pairs: {len(affected_pairs)} pairs", className="mb-1"),
                html.Details([
                    html.Summary("Show affected pairs"),
                    html.P(", ".join(affected_pairs) if affected_pairs else "None specified")
                ])
            ])
        ], className="mb-3")

        # Use the actual before/after states from the event
        before_state = event.previous_state
        after_state = event.current_state

        # Create before/after comparison charts
        before_after_content = dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader(html.H6("Before Event")),
                    dbc.CardBody([
                        html.P("Cluster state 30 minutes before event", className="text-muted mb-2"),
                        dcc.Graph(
                            figure=_create_event_comparison_chart(before_state, "Before Event"),
                            style={'height': '350px'},
                            config={'displayModeBar': False}
                        )
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader(html.H6("After Event")),
                    dbc.CardBody([
                        html.P("Cluster state 30 minutes after event", className="text-muted mb-2"),
                        dcc.Graph(
                            figure=_create_event_comparison_chart(after_state, "After Event"),
                            style={'height': '350px'},
                            config={'displayModeBar': False}
                        )
                    ])
                ])
            ], width=6)
        ])

        # Statistical comparison with actual data
        stats_comparison = _create_statistical_comparison(before_state, after_state)

        return html.Div([
            event_summary,
            before_after_content,
            stats_comparison
        ])

    except Exception as e:
        logger.error(f"Error creating event comparison content: {str(e)}")
        return html.Div([
            html.H5("Error", className="text-danger"),
            html.P(f"Could not create comparison: {str(e)}")
        ])


def _generate_before_after_states(event_timestamp):
    """Generate clustering states for before/after event comparison"""
    try:
        # Calculate time windows (30 minutes before and after event)
        before_time = event_timestamp - timedelta(minutes=30)
        after_time = event_timestamp + timedelta(minutes=30)

        # Generate clustering state for before event
        before_state = _generate_clustering_state_for_time(before_time)

        # Generate clustering state for after event
        after_state = _generate_clustering_state_for_time(after_time)

        return before_state, after_state

    except Exception as e:
        logger.error(f"Error generating before/after states: {str(e)}")
        # Return None states if generation fails
        return None, None


def _generate_clustering_state_for_time(target_time):
    """Generate a clustering state for a specific time period"""
    try:
        # Ensure target_time is timezone-aware
        if target_time.tzinfo is None:
            target_time = target_time.replace(tzinfo=MARKET_TIMEZONE)

        # Calculate hours back from current time to target time
        current_time = datetime.now(MARKET_TIMEZONE)
        hours_back = max(1, int((current_time - target_time).total_seconds() / 3600))

        # Use the global clustering engine to generate state
        # Create a temporary data fetch for this specific time
        market_data = clustering_engine.data_manager.fetch_clustering_data(
            pairs=clustering_engine.symbols,
            hours_back=hours_back,
            use_weekend_fallback=True
        )

        if not market_data:
            logger.warning(f"No market data available for time {target_time}")
            return None

        # Prepare data for clustering
        price_matrix = clustering_engine.data_manager.prepare_data_for_rust(market_data)

        if not price_matrix or len(price_matrix) < 10:
            logger.warning(f"Insufficient data for clustering at time {target_time}")
            return None

        # Perform clustering analysis
        clustering_results = clustering_engine._perform_clustering(price_matrix)

        if not clustering_results:
            logger.warning(f"Clustering analysis failed for time {target_time}")
            return None

        correlation_matrix, cluster_assignments, volatility_profiles = clustering_results

        # Create a clustering state object
        from clustering.state_manager import ClusteringState
        state = ClusteringState(
            timestamp=target_time,
            correlation_matrix=correlation_matrix,
            cluster_assignments=cluster_assignments,
            cluster_count=len(set(cluster_assignments)),
            symbols=clustering_engine.symbols,
            volatility_profiles=volatility_profiles,
            regime_stability=0.5,  # Default value for comparison
            data_quality_score=0.8  # Default value for comparison
        )

        return state

    except Exception as e:
        logger.error(f"Error generating clustering state for time {target_time}: {str(e)}")
        return None


def _create_event_comparison_chart(state, title):
    """Create a cluster visualization chart for event comparison"""
    try:
        if state is None:
            # Create empty chart with message
            fig = go.Figure()
            fig.add_annotation(
                text="No data available for this time period",
                xref="paper", yref="paper",
                x=0.5, y=0.5,
                showarrow=False,
                font=dict(size=14, color="#666")
            )
            fig.update_layout(
                title=title,
                template=CHART_THEME,
                height=350,
                margin=dict(l=20, r=20, t=40, b=20),
                showlegend=False
            )
            return fig

        # Create cluster visualization using existing function
        fig = create_cluster_visualization(state.symbols, state.cluster_assignments)

        # Update layout for modal display
        fig.update_layout(
            title=f"{title} - {len(set(state.cluster_assignments))} Clusters",
            template=CHART_THEME,
            height=350,
            margin=dict(l=20, r=20, t=40, b=20),
            showlegend=False
        )

        # Force remove legend from all traces
        for trace in fig.data:
            trace.showlegend = False

        return fig

    except Exception as e:
        logger.error(f"Error creating event comparison chart: {str(e)}")
        # Return empty chart on error
        fig = go.Figure()
        fig.add_annotation(
            text=f"Error creating chart: {str(e)[:50]}",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=12, color="#d62728")
        )
        fig.update_layout(
            title=title,
            template=CHART_THEME,
            height=350,
            margin=dict(l=20, r=20, t=40, b=20),
            showlegend=False
        )
        return fig


def _create_statistical_comparison(before_state, after_state):
    """Create statistical comparison between before/after states"""
    try:
        # Extract statistics from states
        before_stats = _extract_state_statistics(before_state)
        after_stats = _extract_state_statistics(after_state)

        return dbc.Card([
            dbc.CardHeader(html.H6("Statistical Changes")),
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        html.H6("Before", className="text-center"),
                        html.P(f"Clusters: {before_stats['clusters']}", className="mb-1"),
                        html.P(f"Quality: {before_stats['quality']:.3f}", className="mb-1"),
                        html.P(f"Stability: {before_stats['stability']:.3f}", className="mb-1")
                    ], width=4),
                    dbc.Col([
                        html.Div("→", className="text-center", style={'font-size': '2rem', 'color': '#007bff'})
                    ], width=4, className="d-flex align-items-center justify-content-center"),
                    dbc.Col([
                        html.H6("After", className="text-center"),
                        html.P(f"Clusters: {after_stats['clusters']}", className="mb-1"),
                        html.P(f"Quality: {after_stats['quality']:.3f}", className="mb-1"),
                        html.P(f"Stability: {after_stats['stability']:.3f}", className="mb-1")
                    ], width=4)
                ])
            ])
        ], className="mt-3")

    except Exception as e:
        logger.error(f"Error creating statistical comparison: {str(e)}")
        return dbc.Card([
            dbc.CardHeader(html.H6("Statistical Changes")),
            dbc.CardBody([
                html.P("Error loading statistical comparison", className="text-danger")
            ])
        ], className="mt-3")


def _extract_state_statistics(state):
    """Extract key statistics from a clustering state"""
    if state is None:
        return {
            'clusters': 'N/A',
            'quality': 0.0,
            'stability': 0.0
        }

    return {
        'clusters': len(set(state.cluster_assignments)),
        'quality': state.data_quality_score,
        'stability': state.regime_stability
    }


def _generate_historical_states_for_demo(clustering_engine, timeframe_hours):
    """Generate historical states from Friday's data for Sankey demonstration"""
    try:
        logger.info("Generating historical states for Sankey demonstration...")

        # Get Friday's data in 4-hour chunks to simulate evolution
        time_chunks = [6, 12, 18, 24]  # 6am, 12pm, 6pm, midnight
        base_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=24)

        for i, hours_back in enumerate(time_chunks):
            # Always generate all time chunks for Sankey evolution, regardless of timeframe
            # This ensures we have enough historical states for cluster evolution visualization
            clustering_engine.run_clustering_analysis(
                hours_back=hours_back,
                use_weekend_fallback=True
            )

            # Adjust the timestamp of the generated state to simulate historical progression
            if clustering_engine.state_manager.current_state:
                # Set timestamp to represent the actual time period
                simulated_time = base_time + timedelta(hours=i * 6)  # 6-hour intervals
                clustering_engine.state_manager.current_state.timestamp = simulated_time

                # Update the state in history with the new timestamp
                if clustering_engine.state_manager.state_history:
                    clustering_engine.state_manager.state_history[-1].timestamp = simulated_time

            logger.info(f"Generated historical state for {hours_back}h timeframe at {simulated_time}")

        logger.info(f"Generated {len(time_chunks)} historical states for Sankey")

    except Exception as e:
        logger.error(f"Error generating historical states: {str(e)}")


# =============================================================================
# VOLATILITY REGIME CHART FUNCTIONS
# =============================================================================

def create_volatility_calendar_chart(calendar_data):
    """Create a calendar-style chart showing volatility regimes"""
    try:
        dates = calendar_data['dates']
        regimes = calendar_data['regimes']
        colors = calendar_data['colors']
        regime_names = calendar_data['regime_names']

        if not dates:
            # Return empty chart
            fig = go.Figure()
            fig.update_layout(
                title="No Volatility Regime Data Available",
                template=CHART_THEME,
                height=CHART_HEIGHT,
                showlegend=False
            )
            return fig

        # Convert dates to datetime objects
        # Handle M5 format: "YYYY-MM-DD_HH:MM" and fallback formats
        date_objects = []
        for date in dates:
            try:
                if '_' in date and ':' in date:
                    # M5 format: "2025-06-09_14:05"
                    date_part, time_part = date.split('_')
                    hour, minute = map(int, time_part.split(':'))
                    dt = datetime.strptime(date_part, '%Y-%m-%d').replace(hour=hour, minute=minute)
                elif '_' in date:
                    # Hourly format: "2025-06-09_14"
                    date_part, time_part = date.split('_')
                    hour = int(time_part)
                    dt = datetime.strptime(date_part, '%Y-%m-%d').replace(hour=hour)
                else:
                    # Daily format: "2025-06-09"
                    dt = datetime.strptime(date, '%Y-%m-%d')
                date_objects.append(dt)
            except ValueError as e:
                logger.warning(f"Could not parse date {date}: {e}")
                # Fallback to just date part
                date_part = date.split('_')[0] if '_' in date else date
                date_objects.append(datetime.strptime(date_part, '%Y-%m-%d'))

        # Create a scatter plot with dates on x-axis and regime on y-axis
        fig = go.Figure()

        # Group by regime for better visualization
        # Use 1-based indexing to match regime assignments (1, 2, 3, 4, 5)
        for regime_id in range(1, calendar_data['n_regimes'] + 1):
            regime_dates = [date_objects[i] for i, r in enumerate(regimes) if r == regime_id]
            regime_y = [regime_id] * len(regime_dates)

            if regime_dates:
                fig.add_trace(go.Scatter(
                    x=regime_dates,
                    y=regime_y,
                    mode='markers',
                    marker=dict(
                        size=12,
                        color=colors[regime_id - 1] if regime_id - 1 < len(colors) else '#1f77b4',
                        symbol='square'
                    ),
                    name=regime_names[regime_id - 1] if regime_id - 1 < len(regime_names) else f'Regime {regime_id}',
                    hovertemplate='<b>%{fullData.name}</b><br>' +
                                  'Date: %{x}<br>' +
                                  'Regime: %{y}<br>' +
                                  '<extra></extra>'
                ))

        # Update layout for calendar-like appearance with M5 granularity
        fig.update_layout(
            title="Volatility Regime Calendar (M5 Granularity)",
            xaxis_title="Date & Time",
            yaxis_title="Volatility Regime",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            yaxis=dict(
                tickmode='array',
                tickvals=list(range(1, calendar_data['n_regimes'] + 1)),
                ticktext=[f'Regime {i}' for i in range(1, calendar_data['n_regimes'] + 1)],
                gridcolor='rgba(128,128,128,0.2)'
            ),
            xaxis=dict(
                gridcolor='rgba(128,128,128,0.2)'
            ),
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)'
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating volatility calendar chart: {e}")
        # Return error chart
        fig = go.Figure()
        fig.update_layout(
            title=f"Error Creating Calendar: {str(e)}",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            showlegend=False
        )
        return fig

def create_regime_statistics_chart(calendar_data):
    """Create statistics chart for volatility regimes"""
    try:
        if not calendar_data['dates']:
            return html.Div([
                html.P("No regime statistics available", className="text-muted text-center")
            ])

        regimes = calendar_data['regimes']
        regime_names = calendar_data['regime_names']
        n_regimes = calendar_data['n_regimes']

        # Calculate regime frequency
        regime_counts = {}
        for regime in regimes:
            regime_counts[regime] = regime_counts.get(regime, 0) + 1

        # Get regime explanations from calendar data or use defaults
        regime_explanations = calendar_data.get('regime_explanations', {})

        # Ensure we have explanations for all regimes - use defaults if not provided
        if not regime_explanations:
            regime_explanations = {
                1: "Low Volatility - Calm markets with minimal price movements",
                2: "Medium-Low Volatility - Steady movement with controlled fluctuations",
                3: "Medium Volatility - Normal trading conditions with moderate price action",
                4: "High Volatility - Active markets with significant price movements",
                5: "Very High Volatility - Extreme movements, often during major events"
            }

        # Debug logging to check if explanations are available
        logger.info(f"Regime explanations available: {list(regime_explanations.keys())}")
        logger.info(f"Sample explanation for regime 1: {regime_explanations.get(1, 'None')}")

        # Ensure explanations work with both string and integer keys
        # Convert string keys to integers if needed
        if regime_explanations and isinstance(list(regime_explanations.keys())[0], str):
            regime_explanations = {int(k): v for k, v in regime_explanations.items()}
            logger.info(f"Converted string keys to integers: {list(regime_explanations.keys())}")

        # Create statistics content
        stats_content = [
            html.H6("Regime Explanations", className="mb-3"),
            html.Div([
                html.Div([
                    html.Div([
                        html.Span(f"●", style={'color': calendar_data['colors'][regime_id - 1] if regime_id - 1 < len(calendar_data['colors']) else '#cccccc', 'font-size': '16px', 'margin-right': '8px'}),
                        html.Span(f"Regime {regime_id}: ", className="fw-bold"),
                    ], style={'display': 'flex', 'align-items': 'center'}),
                    html.Div([
                        html.Span(regime_explanations.get(regime_id, f"Regime {regime_id}"), className="text-muted small")
                    ], style={'margin-left': '24px', 'margin-bottom': '8px'})
                ], className="mb-2") for regime_id in range(1, n_regimes + 1)
            ]),
            html.Hr(),
            html.H6("Current Day Distribution", className="mb-3"),
            html.Div([
                html.Div([
                    html.Span([
                        html.Span(f"●", style={'color': calendar_data['colors'][regime_id - 1] if regime_id - 1 < len(calendar_data['colors']) else '#cccccc', 'font-size': '14px', 'margin-right': '6px'}),
                        html.Span(f"Regime {regime_id}: ", className="fw-bold"),
                    ], style={'display': 'flex', 'align-items': 'center'}),
                    html.Span(f"{regime_counts.get(regime_id, 0)} periods ({regime_counts.get(regime_id, 0)/len(regimes)*100:.1f}%)")
                ], className="mb-2", style={'display': 'flex', 'justify-content': 'space-between'}) for regime_id in range(1, n_regimes + 1)
            ]),
            html.Hr(),
            html.H6("Quality Metrics", className="mb-3"),
            html.Div([
                html.Div([
                    html.Span("Total Periods: ", className="fw-bold"),
                    html.Span(f"{len(calendar_data['dates'])}")
                ], className="mb-2"),
                html.Div([
                    html.Span("Number of Regimes: ", className="fw-bold"),
                    html.Span(f"{n_regimes}")
                ], className="mb-2"),
                html.Div([
                    html.Span("Clustering Inertia: ", className="fw-bold"),
                    html.Span(f"{calendar_data['inertia']:.4f}")
                ], className="mb-2")
            ])
        ]

        return html.Div(stats_content, style={'padding': '15px'})

    except Exception as e:
        logger.error(f"Error creating regime statistics: {e}")
        return html.Div([
            html.P(f"Error: {str(e)}", className="text-danger text-center")
        ])

# =============================================================================
# VOLATILITY REGIME CALLBACKS
# =============================================================================

# Shared volatility regime data store callback
@app.callback(
    Output("volatility-regime-store", "data"),
    [Input("main-tabs", "value"),
     Input("data-update-interval", "n_intervals")]
)
def update_volatility_regime_store(active_tab, n_intervals):
    """Central callback to update volatility regime data once and share with all components"""
    if active_tab != "volatility-regimes":
        return {}

    try:
        # Only update volatility regimes if we have sufficient data and the method exists
        if hasattr(clustering_engine.state_manager, 'update_volatility_regimes'):
            try:
                success = clustering_engine.state_manager.update_volatility_regimes(num_days_history=30, n_clusters=5)
                if not success:
                    logger.warning("Volatility regime update returned False")
                    return {}
            except Exception as e:
                logger.warning(f"Could not update volatility regimes: {e}")
                return {}

        # Get all volatility regime data in one place
        regime_data = {}

        # Get calendar data
        if hasattr(clustering_engine.state_manager, 'get_volatility_regime_calendar_data'):
            regime_data['calendar'] = clustering_engine.state_manager.get_volatility_regime_calendar_data()
        else:
            regime_data['calendar'] = {'dates': [], 'regimes': [], 'colors': [], 'regime_names': [], 'n_regimes': 0, 'inertia': 0.0}

        # Get regime transitions
        if hasattr(clustering_engine.state_manager, 'get_regime_transitions'):
            regime_data['transitions'] = clustering_engine.state_manager.get_regime_transitions()
        else:
            regime_data['transitions'] = []

        logger.info(f"Volatility regime store updated: calendar={len(regime_data['calendar'].get('dates', []))} points, "
                   f"transitions={len(regime_data['transitions'])} transitions")

        return regime_data

    except Exception as e:
        logger.error(f"Error updating volatility regime store: {e}")
        return {}

@app.callback(
    Output("volatility-calendar", "children"),
    [Input("volatility-regime-store", "data")]
)
def update_volatility_calendar(regime_data):
    """Update volatility regime calendar using shared data"""
    if not regime_data or 'calendar' not in regime_data:
        return html.Div()

    try:
        calendar_data = regime_data['calendar']

        if not calendar_data['dates']:
            return html.Div([
                html.H6("Volatility Regime Calendar", className="text-center mb-3"),
                html.P("No volatility regime data available yet.",
                       className="text-muted text-center"),
                html.P("Data will appear once sufficient historical price data is collected.",
                       className="text-muted text-center small")
            ], style={'padding': '50px', 'text-align': 'center'})

        # Create calendar visualization
        calendar_fig = create_volatility_calendar_chart(calendar_data)

        return html.Div([
            dcc.Graph(
                figure=calendar_fig,
                config={'displayModeBar': True, 'displaylogo': False},
                style={'height': f'{CHART_HEIGHT}px'}
            ),
            html.Div([
                html.P(f"Showing {len(calendar_data['dates'])} days across {calendar_data['n_regimes']} volatility regimes",
                       className="text-muted text-center small mt-2"),
                html.P(f"Clustering Quality (Inertia): {calendar_data['inertia']:.4f}",
                       className="text-muted text-center small")
            ])
        ])

    except Exception as e:
        logger.error(f"Error updating volatility calendar: {e}")
        return html.Div([
            html.H6("Volatility Regime Calendar", className="text-center mb-3"),
            html.P("Error loading volatility regime data.",
                   className="text-danger text-center"),
            html.P(f"Details: {str(e)}", className="text-muted text-center small")
        ], style={'padding': '50px', 'text-align': 'center'})

@app.callback(
    Output("regime-statistics-panel", "children"),
    [Input("volatility-regime-store", "data")]
)
def update_regime_statistics(regime_data):
    """Update regime statistics panel using shared data"""
    if not regime_data or 'calendar' not in regime_data:
        return html.Div()

    try:
        calendar_data = regime_data['calendar']

        if not calendar_data['dates']:
            return html.Div([
                html.H6("Regime Statistics", className="mb-3"),
                html.P("No volatility regime data available yet.",
                       className="text-muted text-center"),
                html.P("Statistics will appear once volatility regimes are calculated.",
                       className="text-muted text-center small")
            ], style={'padding': '20px', 'text-align': 'center'})

        # Create statistics using the helper function
        return create_regime_statistics_chart(calendar_data)

    except Exception as e:
        logger.error(f"Error updating regime statistics: {e}")
        return html.Div([
            html.H6("Regime Statistics", className="mb-3"),
            html.P("Error loading regime statistics.",
                   className="text-danger text-center"),
            html.P(f"Details: {str(e)}", className="text-muted text-center small")
        ], style={'padding': '20px', 'text-align': 'center'})


@app.callback(
    Output("regime-transition-chart", "figure"),
    [Input("volatility-regime-store", "data")]
)
def update_regime_transition_chart(regime_data):
    """Update regime transition flow chart using shared data"""
    if not regime_data or 'transitions' not in regime_data:
        return go.Figure()

    try:
        # Get regime transitions from shared data
        transitions = regime_data['transitions']
        logger.info(f"Regime transitions data: {len(transitions) if transitions else 0} transitions")

        if not transitions:
            # Fallback to placeholder if no data
            fig = go.Figure()
            fig.add_annotation(
                x=0.5, y=0.5,
                xref="paper", yref="paper",
                text="Regime Transition Flow<br><br>No regime transitions detected yet",
                showarrow=False,
                font=dict(size=14, color="#e1e5e9"),
                align="center"
            )
            fig.update_layout(
                template=CHART_THEME,
                height=CHART_HEIGHT,
                margin=dict(l=20, r=20, t=40, b=20),
                showlegend=False,
                xaxis=dict(visible=False),
                yaxis=dict(visible=False),
                paper_bgcolor='rgba(0,0,0,0)',
                plot_bgcolor='rgba(0,0,0,0)'
            )
            return fig

        # Create regime transition timeline chart
        fig = go.Figure()

        # Extract data for plotting
        dates = [t['date'] for t in transitions]
        from_regimes = [t['from_regime'] for t in transitions]
        to_regimes = [t['to_regime'] for t in transitions]
        symbols = [t['symbol'] for t in transitions]

        # Convert dates to datetime for proper plotting
        try:
            from datetime import datetime
            parsed_dates = []
            for date_str in dates:
                try:
                    parsed_dates.append(datetime.strptime(date_str, '%Y-%m-%d'))
                except:
                    # Try alternative format
                    parsed_dates.append(datetime.fromisoformat(date_str.replace('Z', '+00:00')))
        except:
            parsed_dates = dates  # Fallback to original strings

        # Create scatter plot for transitions
        fig.add_trace(go.Scatter(
            x=parsed_dates,
            y=from_regimes,
            mode='markers',
            name='From Regime',
            marker=dict(
                size=8,
                color='#ff6b6b',
                symbol='circle'
            ),
            hovertemplate='<b>Transition From</b><br>' +
                          'Date: %{x}<br>' +
                          'From Regime: %{y}<br>' +
                          '<extra></extra>'
        ))

        fig.add_trace(go.Scatter(
            x=parsed_dates,
            y=to_regimes,
            mode='markers',
            name='To Regime',
            marker=dict(
                size=8,
                color='#4ecdc4',
                symbol='diamond'
            ),
            hovertemplate='<b>Transition To</b><br>' +
                          'Date: %{x}<br>' +
                          'To Regime: %{y}<br>' +
                          '<extra></extra>'
        ))

        # Add lines connecting transitions
        for i, (date, from_r, to_r) in enumerate(zip(parsed_dates, from_regimes, to_regimes)):
            fig.add_trace(go.Scatter(
                x=[date, date],
                y=[from_r, to_r],
                mode='lines',
                line=dict(color='rgba(255, 255, 255, 0.3)', width=1),
                showlegend=False,
                hoverinfo='skip'
            ))

        fig.update_layout(
            template=CHART_THEME,
            height=CHART_HEIGHT,
            margin=dict(l=40, r=20, t=40, b=40),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            xaxis=dict(
                title="Date",
                type='date'
            ),
            yaxis=dict(
                title="Regime ID",
                tickmode='linear',
                tick0=0,
                dtick=1
            ),
            title=dict(
                text="Volatility Regime Transitions Over Time",
                x=0.5,
                font=dict(size=14)
            )
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating regime transition chart: {e}")
        # Return error placeholder
        fig = go.Figure()
        fig.add_annotation(
            x=0.5, y=0.5,
            xref="paper", yref="paper",
            text=f"Error loading regime transitions<br>{str(e)}",
            showarrow=False,
            font=dict(size=14, color="#ff6b6b"),
            align="center"
        )
        fig.update_layout(
            template=CHART_THEME,
            height=CHART_HEIGHT,
            margin=dict(l=20, r=20, t=40, b=20),
            showlegend=False,
            xaxis=dict(visible=False),
            yaxis=dict(visible=False),
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)'
        )
        return fig

@app.callback(
    Output("tab-state-store", "data"),
    [Input("main-tabs", "value")],
    [State("tab-state-store", "data")]
)
def update_tab_state(active_tab, current_state):
    """Update tab state in store"""
    if current_state is None:
        current_state = {}

    current_state['active_tab'] = active_tab
    return current_state


# =============================================================================
# PORTFOLIO ANALYTICS HELPER FUNCTIONS
# =============================================================================

def create_portfolio_analytics_display(cluster_analyses, diversification_matrix, templates, mpt_lines):
    """Create comprehensive portfolio analytics display"""

    # Create cluster analysis summary
    cluster_summary = []
    for cluster_id, analysis in cluster_analyses.items():
        cluster_summary.append(
            html.Div([
                html.H6(f"Cluster {cluster_id}", className="text-primary"),
                html.P(f"Symbols: {', '.join(analysis.symbols)}", className="mb-1"),
                html.P(f"Avg Correlation: {analysis.avg_correlation:.3f}", className="mb-1"),
                html.P(f"Volatility: {analysis.avg_volatility:.3f}", className="mb-0")
            ], className="mb-2")
        )

    # Create diversification recommendations
    diversification_recs = []
    best_pairs = sorted(diversification_matrix.items(), key=lambda x: x[1].diversification_benefit, reverse=True)[:5]
    for (cluster1, cluster2), metrics in best_pairs:
        diversification_recs.append(
            html.Div([
                html.P(f"Clusters {cluster1} ↔ {cluster2}: {metrics.diversification_benefit:.3f}", className="mb-1")
            ])
        )

    # Create MPT lines chart
    mpt_fig = go.Figure()

    # Add efficient frontier points
    for line in mpt_lines:
        mpt_fig.add_trace(go.Scatter(
            x=[line['risk']],
            y=[line['return']],
            mode='markers',
            name=line['name'],
            marker=dict(size=12, symbol='circle'),
            hovertemplate=f"<b>{line['name']}</b><br>" +
                         f"Risk: {line['risk']:.3f}<br>" +
                         f"Return: {line['return']:.3f}<br>" +
                         f"Sharpe: {line['sharpe']:.3f}<extra></extra>"
        ))

    # Add efficient frontier line if we have multiple points
    if len(mpt_lines) > 1:
        risks = [line['risk'] for line in mpt_lines]
        returns = [line['return'] for line in mpt_lines]
        # Sort by risk for proper line drawing
        sorted_points = sorted(zip(risks, returns))
        sorted_risks, sorted_returns = zip(*sorted_points)

        mpt_fig.add_trace(go.Scatter(
            x=sorted_risks,
            y=sorted_returns,
            mode='lines',
            name='Efficient Frontier',
            line=dict(color='cyan', width=2, dash='dot'),
            showlegend=False
        ))

    mpt_fig.update_layout(
        title="Portfolio Optimization - MPT Lines",
        xaxis_title="Risk (Volatility)",
        yaxis_title="Expected Return",
        template="plotly_dark",
        height=400,
        showlegend=False  # Remove legend as requested
    )

    # Create portfolio templates table
    template_rows = []
    for template in templates:
        template_rows.append(
            html.Tr([
                html.Td(template.name, className="text-primary"),
                html.Td(template.strategy.value.title()),
                html.Td(f"{template.risk_metrics.expected_return:.3f}"),
                html.Td(f"{template.risk_metrics.expected_volatility:.3f}"),
                html.Td(f"{template.risk_metrics.sharpe_ratio:.3f}"),
                html.Td(f"{template.allocation.non_zero_positions}")
            ])
        )

    return html.Div([
        dbc.Row([
            # Cluster Analysis Column
            dbc.Col([
                html.H6("📊 Cluster Analysis", className="mb-3"),
                html.Div(cluster_summary)
            ], width=4),

            # Diversification Recommendations Column
            dbc.Col([
                html.H6("🎯 Diversification Recommendations", className="mb-3"),
                html.Div(diversification_recs)
            ], width=4),

            # MPT Lines Chart Column
            dbc.Col([
                html.H6("📈 MPT Efficient Frontier", className="mb-3"),
                dcc.Graph(figure=mpt_fig, config={'displayModeBar': False})
            ], width=4)
        ], className="mb-4"),

        # Portfolio Templates Table
        dbc.Row([
            dbc.Col([
                html.H6("💼 Portfolio Templates", className="mb-3"),
                dbc.Table([
                    html.Thead([
                        html.Tr([
                            html.Th("Portfolio Name"),
                            html.Th("Strategy"),
                            html.Th("Expected Return"),
                            html.Th("Risk"),
                            html.Th("Sharpe Ratio"),
                            html.Th("Positions")
                        ])
                    ]),
                    html.Tbody(template_rows)
                ], striped=True, bordered=True, hover=True, dark=True, size="sm")
            ], width=12)
        ])
    ])


# =============================================================================
# ADVANCED ANALYTICS CALLBACKS
# =============================================================================

@app.callback(
    Output('clustering-metrics-content', 'children'),
    [Input('clustering-data-store', 'data')]
)
def update_clustering_metrics(clustering_data):
    """Update clustering quality metrics display"""
    logger.info(f"Clustering metrics callback - data available: {clustering_data is not None}")
    logger.info(f"Advanced analytics enabled: {clustering_engine.state_manager.enable_advanced_analytics}")

    if not clustering_data or not clustering_engine.state_manager.enable_advanced_analytics:
        return html.Div([
            html.P("Advanced analytics not available", className="text-muted")
        ])

    try:
        analytics_summary = clustering_engine.state_manager.get_advanced_analytics_summary()
        logger.info(f"Analytics summary keys: {list(analytics_summary.keys()) if analytics_summary else 'None'}")

        if not analytics_summary or 'error' in analytics_summary:
            return html.Div([
                html.P("No clustering metrics available yet", className="text-muted")
            ])

        # Use the actual structure from get_metrics_summary
        logger.info(f"Using analytics summary directly: {analytics_summary}")

        return html.Div([
            dbc.Row([
                dbc.Col([
                    html.H6("Average Silhouette", className="mb-1"),
                    html.H4(f"{analytics_summary.get('avg_silhouette_score', 0):.3f}", className="text-info mb-2"),
                    html.Small("Higher is better (max: 1.0)", className="text-muted")
                ], width=4),
                dbc.Col([
                    html.H6("Cluster Count", className="mb-1"),
                    html.H4(f"{analytics_summary.get('avg_cluster_count', 0):.1f}", className="text-warning mb-2"),
                    html.Small(f"Range: {analytics_summary.get('cluster_count_range', 'N/A')}", className="text-muted")
                ], width=4),
                dbc.Col([
                    html.H6("Stability Score", className="mb-1"),
                    html.H4(f"{analytics_summary.get('avg_stability_score', 0):.1%}", className="text-success mb-2"),
                    html.Small("Cluster consistency", className="text-muted")
                ], width=4)
            ]),
            html.Hr(),
            dbc.Row([
                dbc.Col([
                    html.H6("Total Measurements", className="mb-1"),
                    html.H4(f"{analytics_summary.get('total_measurements', 0)}", className="text-primary mb-2"),
                    html.Small(f"Over {analytics_summary.get('period_hours', 0)} hours", className="text-muted")
                ], width=6),
                dbc.Col([
                    html.H6("Quality Trend", className="mb-1"),
                    html.H4(f"{analytics_summary.get('quality_trend', 'stable').title()}", className="text-secondary mb-2"),
                    html.Small("Recent trend direction", className="text-muted")
                ], width=6)
            ])
        ])

    except Exception as e:
        logger.error(f"Error updating clustering metrics: {e}")
        return html.Div([
            html.P("Error loading clustering metrics", className="text-danger")
        ])

@app.callback(
    Output('regime-classification-content', 'children'),
    [Input('clustering-data-store', 'data')]
)
def update_regime_classification(clustering_data):
    """Update market regime classification display"""
    logger.info(f"Regime classification callback - data available: {clustering_data is not None}")

    if not clustering_data or not clustering_engine.state_manager.enable_advanced_analytics:
        return html.Div([
            html.P("Advanced analytics not available", className="text-muted")
        ])

    try:
        # Get current regime classification directly
        regime = clustering_engine.state_manager.get_current_regime_classification()
        logger.info(f"Current regime classification: {regime}")

        if not regime:
            return html.Div([
                html.P("No regime classification available yet", className="text-muted")
            ])

        # Regime type colors
        regime_colors = {
            'unified': '#28a745',      # Green
            'bifurcated': '#ffc107',   # Yellow
            'fragmented': '#dc3545',   # Red
            'outlier_driven': '#fd7e14', # Orange
            'transitional': '#6f42c1'  # Purple
        }

        regime_type = regime.get('regime_type', 'unknown')
        confidence = regime.get('confidence', 0)
        color = regime_colors.get(regime_type, '#6c757d')

        return html.Div([
            dbc.Row([
                dbc.Col([
                    html.Div([
                        html.H4(regime_type.replace('_', ' ').title(),
                               style={'color': color}, className="mb-1"),
                        html.H6(f"Confidence: {confidence:.1f}%", className="text-muted mb-2")
                    ])
                ], width=8),
                dbc.Col([
                    html.Div([
                        html.Span("●", style={'color': color, 'font-size': '48px'})
                    ], className="text-center")
                ], width=4)
            ]),
            html.Hr(),
            html.Div([
                html.H6("Regime Characteristics:", className="mb-2"),
                html.Ul([
                    html.Li(f"Description: {regime.get('description', 'N/A')}"),
                    html.Li(f"Fragmentation Index: {regime.get('fragmentation_index', 0):.3f}"),
                    html.Li(f"Dominant Cluster Ratio: {regime.get('dominant_cluster_ratio', 0):.1%}")
                ])
            ])
        ])

    except Exception as e:
        logger.error(f"Error updating regime classification: {e}")
        return html.Div([
            html.P("Error loading regime classification", className="text-danger")
        ])

@app.callback(
    Output('alerts-events-content', 'children'),
    [Input('clustering-data-store', 'data')]
)
def update_alerts_events(clustering_data):
    """Update alerts and events display"""
    logger.info(f"Alerts callback - data available: {clustering_data is not None}")
    logger.info(f"Alert system enabled: {clustering_engine.state_manager.enable_alerts}")

    if not clustering_data or not clustering_engine.state_manager.enable_alerts:
        return html.Div([
            html.P("Alert system not available", className="text-muted")
        ])

    try:
        alert_history = clustering_engine.state_manager.get_alert_history(hours_back=24)
        logger.info(f"Alert history count: {len(alert_history) if alert_history else 0}")
        if alert_history:
            logger.info(f"Sample alert data: {alert_history[0]}")

        if not alert_history:
            return html.Div([
                html.P("No alerts in the last 24 hours", className="text-muted")
            ])

        # Group alerts by severity
        alerts_by_severity = {}
        for alert in alert_history[-10:]:  # Show last 10 alerts
            severity = alert.get('severity', 'info')
            if severity not in alerts_by_severity:
                alerts_by_severity[severity] = []
            alerts_by_severity[severity].append(alert)

        severity_colors = {
            'critical': 'danger',
            'high': 'warning',
            'medium': 'info',
            'low': 'secondary'
        }

        alert_items = []
        for severity in ['critical', 'high', 'medium', 'low']:
            if severity in alerts_by_severity:
                for alert in alerts_by_severity[severity]:
                    timestamp = alert.get('timestamp', '')
                    description = alert.get('description', 'Unknown alert')
                    event_type = alert.get('event_type', 'unknown')
                    rule_name = alert.get('rule_name', 'unknown_rule')

                    alert_items.append(
                        dbc.Alert([
                            html.Strong(f"{severity.upper()}: "),
                            html.Span(f"{event_type} - {description}"),
                            html.Br(),
                            html.Small([
                                f"Rule: {rule_name} | ",
                                f"Time: {timestamp}"
                            ], className="text-muted")
                        ], color=severity_colors.get(severity, 'secondary'), className="mb-2")
                    )

        return html.Div(alert_items[:5])  # Show max 5 recent alerts

    except Exception as e:
        logger.error(f"Error updating alerts: {e}")
        return html.Div([
            html.P("Error loading alerts", className="text-danger")
        ])

@app.callback(
    Output('performance-trends-content', 'children'),
    [Input('clustering-data-store', 'data')]
)
def update_performance_trends(clustering_data):
    """Update performance trends display"""
    if not clustering_data:
        return html.Div([
            html.P("No data available", className="text-muted")
        ])

    try:
        # Get recent performance metrics
        performance_metrics = clustering_engine.state_manager.get_performance_metrics()

        if not performance_metrics:
            return html.Div([
                html.P("No performance data available", className="text-muted")
            ])

        return html.Div([
            html.H6("System Performance", className="mb-2"),
            html.Div([
                html.P(f"Data Quality: {performance_metrics.data_quality_average:.1%}", className="mb-1"),
                html.P(f"Total Updates: {performance_metrics.total_updates}", className="mb-1"),
                html.P(f"Processing Time: {performance_metrics.average_processing_time:.2f}s", className="mb-1"),
                html.P(f"Events Detected: {performance_metrics.events_detected}", className="mb-1"),
                html.P(f"Uptime: {performance_metrics.uptime_hours:.1f}h", className="mb-1")
            ])
        ])

    except Exception as e:
        logger.error(f"Error updating performance trends: {e}")
        return html.Div([
            html.P("Error loading performance data", className="text-danger")
        ])

@app.callback(
    Output('portfolio-analytics-content', 'children'),
    [Input('clustering-data-store', 'data')]
)
def update_portfolio_analytics(clustering_data):
    """Update portfolio analytics and MPT integration display"""
    if not clustering_data or not PORTFOLIO_ANALYTICS_AVAILABLE:
        return html.Div([
            html.P("Portfolio analytics not available", className="text-muted")
        ])

    try:
        # Get current clustering state
        current_state = clustering_data
        symbols = current_state.get('symbols', [])
        cluster_assignments = current_state.get('cluster_assignments', [])
        correlation_matrix = np.array(current_state.get('correlation_matrix', []))

        if len(symbols) == 0 or len(correlation_matrix) == 0:
            return html.Div([
                html.P("Insufficient data for portfolio analytics", className="text-muted")
            ])

        # Generate sample volatility profiles (in real implementation, get from volatility regime system)
        volatility_profiles = {symbol: np.random.uniform(0.1, 0.3) for symbol in symbols}

        # Perform cluster analysis
        cluster_analyses = portfolio_analytics.analyze_clusters(
            cluster_assignments, correlation_matrix, volatility_profiles
        )

        # Calculate diversification matrix
        diversification_matrix = portfolio_analytics.calculate_diversification_matrix(
            cluster_analyses, correlation_matrix
        )

        # Generate portfolio templates
        templates = portfolio_template_generator.generate_all_templates(
            correlation_matrix, cluster_analyses, volatility_profiles
        )

        # Create MPT lines using the efficient frontier calculation
        mpt_lines = []
        for template in templates:
            if template.allocation and template.allocation.weights:
                # Calculate efficient frontier points for this template
                weights_array = np.array([template.allocation.weights.get(symbol, 0.0) for symbol in symbols])
                expected_returns = np.array([0.05 + np.random.uniform(-0.02, 0.02) for _ in symbols])  # Sample returns

                # Calculate portfolio metrics
                portfolio_return = np.dot(weights_array, expected_returns)
                portfolio_risk = np.sqrt(np.dot(weights_array, np.dot(correlation_matrix, weights_array))) * 0.15  # Scale risk

                mpt_lines.append({
                    'name': template.name,
                    'strategy': template.strategy.value,
                    'risk': portfolio_risk,
                    'return': portfolio_return,
                    'sharpe': template.risk_metrics.sharpe_ratio,
                    'weights': template.allocation.weights
                })

        return create_portfolio_analytics_display(
            cluster_analyses, diversification_matrix, templates, mpt_lines
        )

    except Exception as e:
        logger.error(f"Error updating portfolio analytics: {e}")
        return html.Div([
            html.P(f"Error in portfolio analytics: {str(e)}", className="text-danger")
        ])


@app.callback(
    Output('export-config-content', 'children'),
    [Input('clustering-data-store', 'data')]
)
def update_export_config(clustering_data):
    """Update export and configuration options"""
    return html.Div([
        dbc.Row([
            dbc.Col([
                html.H6("Data Export", className="mb-2"),
                dbc.ButtonGroup([
                    dbc.Button("Export CSV", id="export-csv-btn", color="primary", size="sm"),
                    dbc.Button("Export JSON", id="export-json-btn", color="secondary", size="sm"),
                    dbc.Button("Export Excel", id="export-excel-btn", color="success", size="sm")
                ], className="mb-2"),
                html.Div(id="export-status")
            ], width=6),
            dbc.Col([
                html.H6("Alert Configuration", className="mb-2"),
                dbc.ButtonGroup([
                    dbc.Button("Configure Alerts", id="config-alerts-btn", color="warning", size="sm"),
                    dbc.Button("View History", id="view-history-btn", color="info", size="sm")
                ])
            ], width=6)
        ])
    ])

# Note: Export button functionality can be implemented later
# For now, the buttons are visible but not functional to avoid callback ID conflicts

# =============================================================================
# MAIN APPLICATION ENTRY POINT
# =============================================================================

if __name__ == "__main__":
    logger.info("Starting Dynamic FX Clustering Dashboard...")
    logger.info(f"Monitoring {len(CURRENCY_PAIRS)} currency pairs")
    logger.info(f"Data update interval: {DATA_UPDATE_INTERVAL/1000}s")
    logger.info(f"Chart update interval: {CHART_UPDATE_INTERVAL/1000}s")
    
    # Start the dashboard
    app.run(
        debug=True,
        host='127.0.0.1',
        port=8090,
        dev_tools_hot_reload=True
    )
