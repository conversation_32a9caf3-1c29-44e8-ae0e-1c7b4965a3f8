"""
Advanced Portfolio Analytics Engine for Dynamic FX Clustering

This module provides comprehensive portfolio analysis capabilities using correlation
clustering results for Modern Portfolio Theory (MPT) applications.

Features:
- Cluster-based diversification analysis
- Portfolio risk optimization
- Dynamic rebalancing strategies
- Regime-aware portfolio construction
- MPT-ready portfolio templates
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, NamedTuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from scipy.optimize import minimize
from scipy.stats import pearsonr
import json

logger = logging.getLogger(__name__)


@dataclass
class ClusterAnalysis:
    """Analysis results for a cluster"""
    cluster_id: int
    symbols: List[str]
    avg_correlation: float
    volatility_profile: Dict[str, float]
    cluster_size: int
    intra_cluster_correlation: float
    representative_symbol: str


@dataclass
class DiversificationMetrics:
    """Diversification analysis between clusters"""
    cluster_pair: Tuple[int, int]
    inter_cluster_correlation: float
    diversification_ratio: float
    risk_reduction_potential: float
    recommended_allocation: Dict[str, float]


@dataclass
class PortfolioTemplate:
    """Portfolio construction template"""
    name: str
    strategy: str
    symbols: List[str]
    weights: Dict[str, float]
    expected_risk: float
    diversification_score: float
    cluster_distribution: Dict[int, float]
    rebalancing_frequency: str
    description: str


@dataclass
class RebalancingSignal:
    """Dynamic rebalancing recommendation"""
    timestamp: datetime
    trigger_type: str  # 'cluster_change', 'volatility_regime', 'correlation_shift'
    current_allocation: Dict[str, float]
    recommended_allocation: Dict[str, float]
    urgency: str  # 'low', 'medium', 'high'
    reason: str
    expected_improvement: float


class PortfolioAnalytics:
    """
    Advanced Portfolio Analytics Engine
    
    Provides comprehensive analysis of clustering results for portfolio optimization,
    diversification analysis, and dynamic rebalancing strategies.
    """
    
    def __init__(self, symbols: List[str]):
        """
        Initialize portfolio analytics engine
        
        Args:
            symbols: List of currency pair symbols
        """
        self.symbols = symbols
        self.cluster_analyses: Dict[int, ClusterAnalysis] = {}
        self.diversification_matrix: Dict[Tuple[int, int], DiversificationMetrics] = {}
        self.portfolio_templates: List[PortfolioTemplate] = []
        self.rebalancing_history: List[RebalancingSignal] = []
        
        # Portfolio optimization parameters
        self.risk_free_rate = 0.02  # 2% annual risk-free rate
        self.max_position_size = 1.0  # Maximum 100% in any single position (allows leverage)
        self.min_position_size = -1.0  # Minimum -100% in any position (allows short selling)

    def _normalize_weights_absolute(self, weights: Dict[str, float]) -> Dict[str, float]:
        """Normalize weights so absolute sum = 1, allowing negative weights (-1 to 1)"""
        try:
            # Calculate absolute sum
            abs_sum = sum(abs(weight) for weight in weights.values())

            if abs_sum <= 1e-15:
                # If all weights are zero, return equal weights
                equal_weight = 1.0 / len(self.symbols)
                return {symbol: equal_weight for symbol in self.symbols}

            # Normalize by absolute sum
            normalized_weights = {symbol: weight / abs_sum for symbol, weight in weights.items()}

            # Ensure weights are within bounds [-1, 1]
            for symbol in normalized_weights:
                normalized_weights[symbol] = max(-1.0, min(1.0, normalized_weights[symbol]))

            # Verify absolute sum is 1 (within tolerance)
            final_abs_sum = sum(abs(weight) for weight in normalized_weights.values())
            if abs(final_abs_sum - 1.0) > 1e-6:
                # Re-normalize if needed
                normalized_weights = {symbol: weight / final_abs_sum for symbol, weight in normalized_weights.items()}

            return normalized_weights

        except Exception as e:
            logger.error(f"Error normalizing weights: {str(e)}")
            # Fallback to equal weights
            equal_weight = 1.0 / len(self.symbols)
            return {symbol: equal_weight for symbol in self.symbols}
        
    def analyze_clusters(self, 
                        correlation_matrix: np.ndarray,
                        cluster_assignments: List[int],
                        volatility_profiles: Dict[str, float]) -> Dict[int, ClusterAnalysis]:
        """
        Analyze individual clusters for portfolio construction
        
        Args:
            correlation_matrix: Correlation matrix between symbols
            cluster_assignments: Cluster assignment for each symbol
            volatility_profiles: Volatility data for each symbol
            
        Returns:
            Dictionary of cluster analyses
        """
        try:
            cluster_analyses = {}
            unique_clusters = list(set(cluster_assignments))
            
            for cluster_id in unique_clusters:
                # Get symbols in this cluster
                cluster_indices = [i for i, c in enumerate(cluster_assignments) if c == cluster_id]
                cluster_symbols = [self.symbols[i] for i in cluster_indices]
                
                if len(cluster_symbols) == 0:
                    continue
                
                # Calculate intra-cluster correlation
                if len(cluster_indices) > 1:
                    cluster_corr_matrix = correlation_matrix[np.ix_(cluster_indices, cluster_indices)]
                    # Get upper triangle (excluding diagonal)
                    upper_triangle = np.triu(cluster_corr_matrix, k=1)
                    intra_cluster_corr = np.mean(upper_triangle[upper_triangle != 0])
                    avg_correlation = intra_cluster_corr
                else:
                    intra_cluster_corr = 1.0
                    avg_correlation = 1.0
                
                # Get volatility profile for cluster
                cluster_volatility = {symbol: volatility_profiles.get(symbol, 0.01) 
                                    for symbol in cluster_symbols}
                
                # Find representative symbol (lowest volatility in cluster)
                representative = min(cluster_symbols, 
                                   key=lambda s: volatility_profiles.get(s, 0.01))
                
                cluster_analysis = ClusterAnalysis(
                    cluster_id=cluster_id,
                    symbols=cluster_symbols,
                    avg_correlation=avg_correlation,
                    volatility_profile=cluster_volatility,
                    cluster_size=len(cluster_symbols),
                    intra_cluster_correlation=intra_cluster_corr,
                    representative_symbol=representative
                )
                
                cluster_analyses[cluster_id] = cluster_analysis
            
            self.cluster_analyses = cluster_analyses
            return cluster_analyses
            
        except Exception as e:
            logger.error(f"Error analyzing clusters: {str(e)}")
            return {}
    
    def calculate_diversification_matrix(self, 
                                       correlation_matrix: np.ndarray,
                                       cluster_assignments: List[int]) -> Dict[Tuple[int, int], DiversificationMetrics]:
        """
        Calculate diversification benefits between all cluster pairs
        
        Args:
            correlation_matrix: Correlation matrix between symbols
            cluster_assignments: Cluster assignment for each symbol
            
        Returns:
            Dictionary of diversification metrics between cluster pairs
        """
        try:
            diversification_matrix = {}
            unique_clusters = list(set(cluster_assignments))
            
            for i, cluster_1 in enumerate(unique_clusters):
                for j, cluster_2 in enumerate(unique_clusters):
                    if i >= j:  # Only calculate upper triangle
                        continue
                    
                    # Get indices for each cluster
                    indices_1 = [idx for idx, c in enumerate(cluster_assignments) if c == cluster_1]
                    indices_2 = [idx for idx, c in enumerate(cluster_assignments) if c == cluster_2]
                    
                    if len(indices_1) == 0 or len(indices_2) == 0:
                        continue
                    
                    # Calculate inter-cluster correlation
                    inter_cluster_correlations = []
                    for idx1 in indices_1:
                        for idx2 in indices_2:
                            inter_cluster_correlations.append(correlation_matrix[idx1, idx2])
                    
                    inter_cluster_corr = np.mean(inter_cluster_correlations)
                    
                    # Calculate diversification ratio (1 - |correlation|)
                    diversification_ratio = 1.0 - abs(inter_cluster_corr)
                    
                    # Calculate risk reduction potential
                    risk_reduction = diversification_ratio * 0.5  # Simplified calculation
                    
                    # Recommend equal allocation as starting point
                    recommended_allocation = {
                        f"cluster_{cluster_1}": 0.5,
                        f"cluster_{cluster_2}": 0.5
                    }
                    
                    diversification_metrics = DiversificationMetrics(
                        cluster_pair=(cluster_1, cluster_2),
                        inter_cluster_correlation=inter_cluster_corr,
                        diversification_ratio=diversification_ratio,
                        risk_reduction_potential=risk_reduction,
                        recommended_allocation=recommended_allocation
                    )
                    
                    diversification_matrix[(cluster_1, cluster_2)] = diversification_metrics
            
            self.diversification_matrix = diversification_matrix
            return diversification_matrix
            
        except Exception as e:
            logger.error(f"Error calculating diversification matrix: {str(e)}")
            return {}
    
    def get_best_diversification_pairs(self, top_n: int = 5) -> List[DiversificationMetrics]:
        """
        Get the best cluster pairs for diversification
        
        Args:
            top_n: Number of top pairs to return
            
        Returns:
            List of best diversification metrics
        """
        try:
            if not self.diversification_matrix:
                return []
            
            # Sort by diversification ratio (descending)
            sorted_pairs = sorted(
                self.diversification_matrix.values(),
                key=lambda x: x.diversification_ratio,
                reverse=True
            )
            
            return sorted_pairs[:top_n]
            
        except Exception as e:
            logger.error(f"Error getting best diversification pairs: {str(e)}")
            return []
    
    def calculate_portfolio_risk(self, 
                               weights: Dict[str, float],
                               correlation_matrix: np.ndarray,
                               volatility_profiles: Dict[str, float]) -> float:
        """
        Calculate portfolio risk using MPT formula
        
        Args:
            weights: Portfolio weights for each symbol
            correlation_matrix: Correlation matrix
            volatility_profiles: Volatility for each symbol
            
        Returns:
            Portfolio risk (standard deviation)
        """
        try:
            # Convert weights to numpy array in symbol order
            weight_array = np.array([weights.get(symbol, 0.0) for symbol in self.symbols])
            volatility_array = np.array([volatility_profiles.get(symbol, 0.01) for symbol in self.symbols])
            
            # Create covariance matrix from correlation matrix and volatilities
            vol_matrix = np.outer(volatility_array, volatility_array)
            covariance_matrix = correlation_matrix * vol_matrix
            
            # Calculate portfolio variance: w^T * Σ * w
            portfolio_variance = np.dot(weight_array.T, np.dot(covariance_matrix, weight_array))
            
            # Return portfolio risk (standard deviation)
            return np.sqrt(portfolio_variance)
            
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {str(e)}")
            return 0.0

    def optimize_minimum_variance_portfolio(self,
                                          correlation_matrix: np.ndarray,
                                          volatility_profiles: Dict[str, float],
                                          cluster_constraints: Optional[Dict[int, float]] = None) -> Dict[str, float]:
        """
        Optimize portfolio for minimum variance

        Args:
            correlation_matrix: Correlation matrix
            volatility_profiles: Volatility profiles
            cluster_constraints: Optional constraints on cluster allocation

        Returns:
            Optimized portfolio weights
        """
        try:
            n_assets = len(self.symbols)

            # Objective function: minimize portfolio variance
            def objective(weights):
                portfolio_risk = self.calculate_portfolio_risk(
                    dict(zip(self.symbols, weights)),
                    correlation_matrix,
                    volatility_profiles
                )
                return portfolio_risk ** 2  # Minimize variance

            # Constraints - use absolute sum = 1
            def absolute_sum_constraint(weights):
                return np.sum(np.abs(weights)) - 1.0

            constraints = [
                {'type': 'eq', 'fun': absolute_sum_constraint}  # Absolute sum of weights = 1
            ]

            # Bounds for each weight
            bounds = [(self.min_position_size, self.max_position_size) for _ in range(n_assets)]

            # Initial guess (equal weights)
            initial_weights = np.array([1.0 / n_assets] * n_assets)

            # Optimize
            result = minimize(
                objective,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000}
            )

            if result.success:
                optimized_weights = dict(zip(self.symbols, result.x))
                # Apply absolute sum normalization
                optimized_weights = self._normalize_weights_absolute(optimized_weights)
                return optimized_weights
            else:
                logger.warning("Portfolio optimization failed, returning equal weights")
                fallback_weights = dict(zip(self.symbols, initial_weights))
                fallback_weights = self._normalize_weights_absolute(fallback_weights)
                return fallback_weights

        except Exception as e:
            logger.error(f"Error optimizing portfolio: {str(e)}")
            # Return equal weights as fallback
            equal_weight = 1.0 / len(self.symbols)
            return {symbol: equal_weight for symbol in self.symbols}

    def generate_portfolio_templates(self,
                                   correlation_matrix: np.ndarray,
                                   cluster_assignments: List[int],
                                   volatility_profiles: Dict[str, float]) -> List[PortfolioTemplate]:
        """
        Generate 4-5 portfolio templates for different strategies

        Args:
            correlation_matrix: Correlation matrix
            cluster_assignments: Cluster assignments
            volatility_profiles: Volatility profiles

        Returns:
            List of portfolio templates
        """
        try:
            templates = []

            # 1. Conservative Portfolio (Low Risk)
            conservative_weights = self.optimize_minimum_variance_portfolio(
                correlation_matrix, volatility_profiles
            )
            conservative_risk = self.calculate_portfolio_risk(
                conservative_weights, correlation_matrix, volatility_profiles
            )

            templates.append(PortfolioTemplate(
                name="Conservative",
                strategy="minimum_variance",
                symbols=list(conservative_weights.keys()),
                weights=conservative_weights,
                expected_risk=conservative_risk,
                diversification_score=self._calculate_diversification_score(
                    conservative_weights, correlation_matrix
                ),
                cluster_distribution=self._calculate_cluster_distribution(
                    conservative_weights, cluster_assignments
                ),
                rebalancing_frequency="monthly",
                description="Minimum variance portfolio optimized for capital preservation"
            ))

            # 2. Balanced Portfolio (Cluster Diversified)
            balanced_weights = self._create_cluster_diversified_portfolio(
                cluster_assignments, volatility_profiles
            )
            balanced_risk = self.calculate_portfolio_risk(
                balanced_weights, correlation_matrix, volatility_profiles
            )

            templates.append(PortfolioTemplate(
                name="Balanced",
                strategy="cluster_diversified",
                symbols=list(balanced_weights.keys()),
                weights=balanced_weights,
                expected_risk=balanced_risk,
                diversification_score=self._calculate_diversification_score(
                    balanced_weights, correlation_matrix
                ),
                cluster_distribution=self._calculate_cluster_distribution(
                    balanced_weights, cluster_assignments
                ),
                rebalancing_frequency="bi-weekly",
                description="Balanced allocation across clusters for optimal diversification"
            ))

            # 3. Aggressive Portfolio (High Volatility Focus)
            aggressive_weights = self._create_volatility_focused_portfolio(
                volatility_profiles, target_volatility="high"
            )
            aggressive_risk = self.calculate_portfolio_risk(
                aggressive_weights, correlation_matrix, volatility_profiles
            )

            templates.append(PortfolioTemplate(
                name="Aggressive",
                strategy="high_volatility",
                symbols=list(aggressive_weights.keys()),
                weights=aggressive_weights,
                expected_risk=aggressive_risk,
                diversification_score=self._calculate_diversification_score(
                    aggressive_weights, correlation_matrix
                ),
                cluster_distribution=self._calculate_cluster_distribution(
                    aggressive_weights, cluster_assignments
                ),
                rebalancing_frequency="weekly",
                description="High volatility portfolio for maximum return potential"
            ))

            # 4. Regime-Adaptive Portfolio
            regime_weights = self._create_regime_adaptive_portfolio(
                correlation_matrix, cluster_assignments, volatility_profiles
            )
            regime_risk = self.calculate_portfolio_risk(
                regime_weights, correlation_matrix, volatility_profiles
            )

            templates.append(PortfolioTemplate(
                name="Regime-Adaptive",
                strategy="regime_adaptive",
                symbols=list(regime_weights.keys()),
                weights=regime_weights,
                expected_risk=regime_risk,
                diversification_score=self._calculate_diversification_score(
                    regime_weights, correlation_matrix
                ),
                cluster_distribution=self._calculate_cluster_distribution(
                    regime_weights, cluster_assignments
                ),
                rebalancing_frequency="daily",
                description="Dynamic allocation that adapts to market regime changes"
            ))

            # 5. Equal Risk Contribution Portfolio
            erc_weights = self._create_equal_risk_contribution_portfolio(
                correlation_matrix, volatility_profiles
            )
            erc_risk = self.calculate_portfolio_risk(
                erc_weights, correlation_matrix, volatility_profiles
            )

            templates.append(PortfolioTemplate(
                name="Equal Risk Contribution",
                strategy="equal_risk_contribution",
                symbols=list(erc_weights.keys()),
                weights=erc_weights,
                expected_risk=erc_risk,
                diversification_score=self._calculate_diversification_score(
                    erc_weights, correlation_matrix
                ),
                cluster_distribution=self._calculate_cluster_distribution(
                    erc_weights, cluster_assignments
                ),
                rebalancing_frequency="bi-weekly",
                description="Each position contributes equally to portfolio risk"
            ))

            self.portfolio_templates = templates
            return templates

        except Exception as e:
            logger.error(f"Error generating portfolio templates: {str(e)}")
            return []

    def _create_cluster_diversified_portfolio(self,
                                            cluster_assignments: List[int],
                                            volatility_profiles: Dict[str, float]) -> Dict[str, float]:
        """
        Create a portfolio that maximizes diversification across clusters

        Args:
            cluster_assignments: Cluster assignment for each symbol
            volatility_profiles: Volatility for each symbol

        Returns:
            Portfolio weights dictionary
        """
        try:
            logger.debug(f"DEBUG: _create_cluster_diversified_portfolio called with cluster_assignments type: {type(cluster_assignments)}")
            logger.debug(f"DEBUG: volatility_profiles type: {type(volatility_profiles)}")

            # Get unique clusters
            unique_clusters = list(set(cluster_assignments))
            n_clusters = len(unique_clusters)

            if n_clusters == 0:
                # Fallback to equal weights
                equal_weight = 1.0 / len(self.symbols)
                return {symbol: equal_weight for symbol in self.symbols}

            # Allocate equal weight to each cluster
            cluster_weight = 1.0 / n_clusters

            # Within each cluster, use inverse volatility weighting
            weights = {}

            for cluster_id in unique_clusters:
                # Get symbols in this cluster
                cluster_symbols = [self.symbols[i] for i, c in enumerate(cluster_assignments) if c == cluster_id]

                if not cluster_symbols:
                    continue

                # Calculate inverse volatility weights within cluster
                cluster_inv_vols = {}
                total_inv_vol = 0.0

                for symbol in cluster_symbols:
                    vol = volatility_profiles.get(symbol, 0.01)
                    inv_vol = 1.0 / vol if vol > 0 else 1.0
                    cluster_inv_vols[symbol] = inv_vol
                    total_inv_vol += inv_vol

                # Assign weights within cluster
                for symbol in cluster_symbols:
                    if total_inv_vol > 0:
                        symbol_weight_in_cluster = cluster_inv_vols[symbol] / total_inv_vol
                        weights[symbol] = symbol_weight_in_cluster * cluster_weight
                    else:
                        weights[symbol] = cluster_weight / len(cluster_symbols)

            # Ensure all symbols have weights
            for symbol in self.symbols:
                if symbol not in weights:
                    weights[symbol] = 0.0

            # Normalize to ensure sum equals 1 first, then apply absolute sum normalization
            total_weight = sum(weights.values())
            if total_weight > 0:
                weights = {symbol: weight / total_weight for symbol, weight in weights.items()}
            else:
                equal_weight = 1.0 / len(self.symbols)
                weights = {symbol: equal_weight for symbol in self.symbols}

            # Apply absolute sum normalization
            weights = self._normalize_weights_absolute(weights)
            return weights

        except Exception as e:
            logger.error(f"Error creating cluster diversified portfolio: {str(e)}")
            equal_weight = 1.0 / len(self.symbols)
            return {symbol: equal_weight for symbol in self.symbols}

    def _create_volatility_focused_portfolio(self,
                                           volatility_profiles: Dict[str, float],
                                           focus_type: str = "low") -> Dict[str, float]:
        """
        Create a portfolio focused on low or high volatility assets

        Args:
            volatility_profiles: Volatility for each symbol
            focus_type: "low" for low volatility focus, "high" for high volatility focus

        Returns:
            Portfolio weights dictionary
        """
        try:
            logger.debug(f"DEBUG: _create_volatility_focused_portfolio called with volatility_profiles type: {type(volatility_profiles)}")
            logger.debug(f"DEBUG: focus_type: {focus_type}")

            if focus_type == "low":
                # Inverse volatility weighting (favor low volatility)
                weights = {}
                total_inv_vol = 0.0

                for symbol in self.symbols:
                    vol = volatility_profiles.get(symbol, 0.01)
                    inv_vol = 1.0 / vol if vol > 0 else 1.0
                    weights[symbol] = inv_vol
                    total_inv_vol += inv_vol

                # Normalize using regular sum first, then apply absolute sum normalization
                if total_inv_vol > 0:
                    weights = {symbol: weight / total_inv_vol for symbol, weight in weights.items()}
                else:
                    equal_weight = 1.0 / len(self.symbols)
                    weights = {symbol: equal_weight for symbol in self.symbols}

                # Apply absolute sum normalization
                weights = self._normalize_weights_absolute(weights)

            elif focus_type == "high":
                # Volatility weighting (favor high volatility)
                weights = {}
                total_vol = 0.0

                for symbol in self.symbols:
                    vol = volatility_profiles.get(symbol, 0.01)
                    weights[symbol] = vol
                    total_vol += vol

                # Normalize using regular sum first, then apply absolute sum normalization
                if total_vol > 0:
                    weights = {symbol: weight / total_vol for symbol, weight in weights.items()}
                else:
                    equal_weight = 1.0 / len(self.symbols)
                    weights = {symbol: equal_weight for symbol in self.symbols}

                # Apply absolute sum normalization
                weights = self._normalize_weights_absolute(weights)

            else:
                # Unknown focus type - return equal weights
                equal_weight = 1.0 / len(self.symbols)
                weights = {symbol: equal_weight for symbol in self.symbols}

            return weights

        except Exception as e:
            logger.error(f"Error creating volatility focused portfolio: {str(e)}")
            equal_weight = 1.0 / len(self.symbols)
            return {symbol: equal_weight for symbol in self.symbols}

    def _calculate_diversification_score(self,
                                       weights: Dict[str, float],
                                       correlation_matrix: np.ndarray) -> float:
        """Calculate diversification score for a portfolio"""
        try:
            weight_array = np.array([weights.get(symbol, 0.0) for symbol in self.symbols])

            # Calculate weighted average correlation
            weighted_corr = 0.0
            total_weight_pairs = 0.0

            for i in range(len(self.symbols)):
                for j in range(i + 1, len(self.symbols)):
                    weight_product = weight_array[i] * weight_array[j]
                    weighted_corr += weight_product * abs(correlation_matrix[i, j])
                    total_weight_pairs += weight_product

            if total_weight_pairs > 0:
                avg_correlation = weighted_corr / total_weight_pairs
                return 1.0 - avg_correlation  # Higher score = better diversification
            else:
                return 0.0

        except Exception as e:
            logger.error(f"Error calculating diversification score: {str(e)}")
            return 0.0

    def _calculate_cluster_distribution(self,
                                      weights: Dict[str, float],
                                      cluster_assignments: List[int]) -> Dict[int, float]:
        """Calculate weight distribution across clusters"""
        try:
            cluster_weights = {}

            for i, symbol in enumerate(self.symbols):
                if i < len(cluster_assignments):
                    cluster_id = cluster_assignments[i]
                    weight = weights.get(symbol, 0.0)

                    if cluster_id not in cluster_weights:
                        cluster_weights[cluster_id] = 0.0
                    cluster_weights[cluster_id] += weight

            return cluster_weights

        except Exception as e:
            logger.error(f"Error calculating cluster distribution: {str(e)}")
            return {}





    def _create_regime_adaptive_portfolio(self,
                                        correlation_matrix: np.ndarray,
                                        cluster_assignments: List[int],
                                        volatility_profiles: Dict[str, float]) -> Dict[str, float]:
        """Create portfolio that adapts to current market regime"""
        try:
            # Analyze current regime characteristics
            avg_correlation = np.mean(np.abs(correlation_matrix[np.triu_indices_from(correlation_matrix, k=1)]))
            avg_volatility = np.mean(list(volatility_profiles.values()))

            # Adaptive strategy based on regime
            if avg_correlation > 0.7:  # High correlation regime
                # Focus on diversification across clusters
                return self._create_cluster_diversified_portfolio(cluster_assignments, volatility_profiles)
            elif avg_volatility > 0.02:  # High volatility regime
                # Focus on low volatility assets
                return self._create_volatility_focused_portfolio(volatility_profiles, "low")
            else:  # Normal regime
                # Use minimum variance approach
                return self.optimize_minimum_variance_portfolio(correlation_matrix, volatility_profiles)

        except Exception as e:
            logger.error(f"Error creating regime adaptive portfolio: {str(e)}")
            equal_weight = 1.0 / len(self.symbols)
            return {symbol: equal_weight for symbol in self.symbols}

    def _create_equal_risk_contribution_portfolio(self,
                                                correlation_matrix: np.ndarray,
                                                volatility_profiles: Dict[str, float]) -> Dict[str, float]:
        """Create portfolio where each asset contributes equally to total risk"""
        try:
            # Simplified ERC: inverse volatility weighting
            volatilities = np.array([volatility_profiles.get(symbol, 0.01) for symbol in self.symbols])

            # Inverse volatility weights
            inv_vol_weights = 1.0 / volatilities
            inv_vol_weights = inv_vol_weights / np.sum(inv_vol_weights)

            weights_dict = dict(zip(self.symbols, inv_vol_weights))

            # Apply absolute sum normalization
            weights_dict = self._normalize_weights_absolute(weights_dict)
            return weights_dict

        except Exception as e:
            logger.error(f"Error creating ERC portfolio: {str(e)}")
            equal_weight = 1.0 / len(self.symbols)
            return {symbol: equal_weight for symbol in self.symbols}

    def detect_rebalancing_signals(self,
                                 current_allocation: Dict[str, float],
                                 new_correlation_matrix: np.ndarray,
                                 new_cluster_assignments: List[int],
                                 new_volatility_profiles: Dict[str, float],
                                 previous_correlation_matrix: Optional[np.ndarray] = None,
                                 previous_cluster_assignments: Optional[List[int]] = None) -> List[RebalancingSignal]:
        """
        Detect when portfolio rebalancing is needed based on regime changes

        Args:
            current_allocation: Current portfolio weights
            new_correlation_matrix: Latest correlation matrix
            new_cluster_assignments: Latest cluster assignments
            new_volatility_profiles: Latest volatility profiles
            previous_correlation_matrix: Previous correlation matrix for comparison
            previous_cluster_assignments: Previous cluster assignments for comparison

        Returns:
            List of rebalancing signals
        """
        try:
            signals = []
            timestamp = datetime.now()

            # 1. Check for cluster structure changes
            if previous_cluster_assignments is not None:
                cluster_stability = self._calculate_cluster_stability(
                    previous_cluster_assignments, new_cluster_assignments
                )

                if cluster_stability < 0.7:  # Significant cluster change
                    # Recommend cluster-diversified rebalancing
                    new_allocation = self._create_cluster_diversified_portfolio(
                        new_cluster_assignments, new_volatility_profiles
                    )

                    signals.append(RebalancingSignal(
                        timestamp=timestamp,
                        trigger_type="cluster_change",
                        current_allocation=current_allocation,
                        recommended_allocation=new_allocation,
                        urgency="high",
                        reason=f"Cluster structure changed significantly (stability: {cluster_stability:.2f})",
                        expected_improvement=0.15
                    ))

            # 2. Check for correlation regime shifts
            if previous_correlation_matrix is not None:
                correlation_shift = self._calculate_correlation_shift(
                    previous_correlation_matrix, new_correlation_matrix
                )

                if correlation_shift > 0.3:  # Significant correlation change
                    # Recommend minimum variance rebalancing
                    new_allocation = self.optimize_minimum_variance_portfolio(
                        new_correlation_matrix, new_volatility_profiles
                    )

                    signals.append(RebalancingSignal(
                        timestamp=timestamp,
                        trigger_type="correlation_shift",
                        current_allocation=current_allocation,
                        recommended_allocation=new_allocation,
                        urgency="medium",
                        reason=f"Correlation structure shifted significantly (change: {correlation_shift:.2f})",
                        expected_improvement=0.10
                    ))

            # 3. Check for volatility regime changes
            avg_volatility = np.mean(list(new_volatility_profiles.values()))
            if hasattr(self, '_previous_avg_volatility'):
                volatility_change = abs(avg_volatility - self._previous_avg_volatility) / self._previous_avg_volatility

                if volatility_change > 0.5:  # 50% change in average volatility
                    # Recommend volatility-adjusted rebalancing
                    target_vol = "low" if avg_volatility > self._previous_avg_volatility else "high"
                    new_allocation = self._create_volatility_focused_portfolio(
                        new_volatility_profiles, target_vol
                    )

                    signals.append(RebalancingSignal(
                        timestamp=timestamp,
                        trigger_type="volatility_regime",
                        current_allocation=current_allocation,
                        recommended_allocation=new_allocation,
                        urgency="medium",
                        reason=f"Volatility regime changed by {volatility_change:.1%}",
                        expected_improvement=0.08
                    ))

            self._previous_avg_volatility = avg_volatility

            # Store signals in history
            self.rebalancing_history.extend(signals)

            return signals

        except Exception as e:
            logger.error(f"Error detecting rebalancing signals: {str(e)}")
            return []

    def _calculate_cluster_stability(self,
                                   previous_assignments: List[int],
                                   current_assignments: List[int]) -> float:
        """Calculate stability between two cluster assignments"""
        try:
            if len(previous_assignments) != len(current_assignments):
                return 0.0

            # Calculate adjusted rand index (simplified)
            matches = sum(1 for p, c in zip(previous_assignments, current_assignments) if p == c)
            stability = matches / len(previous_assignments)

            return stability

        except Exception as e:
            logger.error(f"Error calculating cluster stability: {str(e)}")
            return 0.0

    def _calculate_correlation_shift(self,
                                   previous_matrix: np.ndarray,
                                   current_matrix: np.ndarray) -> float:
        """Calculate the magnitude of correlation matrix change"""
        try:
            if previous_matrix.shape != current_matrix.shape:
                return 1.0  # Maximum change

            # Calculate Frobenius norm of the difference
            diff_matrix = current_matrix - previous_matrix
            frobenius_norm = np.linalg.norm(diff_matrix, 'fro')

            # Normalize by matrix size
            normalized_change = frobenius_norm / np.sqrt(previous_matrix.size)

            return min(normalized_change, 1.0)

        except Exception as e:
            logger.error(f"Error calculating correlation shift: {str(e)}")
            return 0.0

    def export_portfolio_templates_for_mpt(self, output_path: str = "portfolio_templates.json") -> bool:
        """
        Export portfolio templates in MPT-compatible format

        Args:
            output_path: Path to save the templates

        Returns:
            True if successful, False otherwise
        """
        try:
            export_data = {
                "timestamp": datetime.now().isoformat(),
                "templates": [],
                "cluster_analysis": {},
                "diversification_matrix": {}
            }

            # Export templates
            for template in self.portfolio_templates:
                template_data = {
                    "name": template.name,
                    "strategy": template.strategy,
                    "symbols": template.symbols,
                    "weights": template.weights,
                    "expected_risk": template.expected_risk,
                    "diversification_score": template.diversification_score,
                    "cluster_distribution": template.cluster_distribution,
                    "rebalancing_frequency": template.rebalancing_frequency,
                    "description": template.description
                }
                export_data["templates"].append(template_data)

            # Export cluster analysis
            for cluster_id, analysis in self.cluster_analyses.items():
                export_data["cluster_analysis"][str(cluster_id)] = {
                    "symbols": analysis.symbols,
                    "avg_correlation": analysis.avg_correlation,
                    "volatility_profile": analysis.volatility_profile,
                    "cluster_size": analysis.cluster_size,
                    "intra_cluster_correlation": analysis.intra_cluster_correlation,
                    "representative_symbol": analysis.representative_symbol
                }

            # Export diversification matrix
            for (c1, c2), metrics in self.diversification_matrix.items():
                key = f"{c1}_{c2}"
                export_data["diversification_matrix"][key] = {
                    "cluster_pair": [c1, c2],
                    "inter_cluster_correlation": metrics.inter_cluster_correlation,
                    "diversification_ratio": metrics.diversification_ratio,
                    "risk_reduction_potential": metrics.risk_reduction_potential,
                    "recommended_allocation": metrics.recommended_allocation
                }

            # Save to file
            with open(output_path, 'w') as f:
                json.dump(export_data, f, indent=2)

            logger.info(f"Portfolio templates exported to {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error exporting portfolio templates: {str(e)}")
            return False

    def get_portfolio_summary(self) -> Dict:
        """Get comprehensive portfolio analytics summary"""
        try:
            summary = {
                "cluster_count": len(self.cluster_analyses),
                "template_count": len(self.portfolio_templates),
                "best_diversification_pairs": self.get_best_diversification_pairs(3),
                "rebalancing_signals_count": len(self.rebalancing_history),
                "cluster_analyses": self.cluster_analyses,
                "portfolio_templates": self.portfolio_templates
            }

            return summary

        except Exception as e:
            logger.error(f"Error generating portfolio summary: {str(e)}")
            return {}
