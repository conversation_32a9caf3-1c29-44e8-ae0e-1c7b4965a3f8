"""
Real-time Alert System for Dynamic FX Clustering Application
==========================================================

Provides real-time alerting capabilities for regime changes,
clustering events, and market anomalies.
"""

import smtplib
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from pathlib import Path
import threading
import queue
import time

from config import MARKET_TIMEZONE
from .advanced_analytics import AdvancedEvent, RegimeType, RegimeClassification

logger = logging.getLogger(__name__)


@dataclass
class AlertRule:
    """Configuration for an alert rule"""
    name: str
    event_types: List[str]  # Event types to monitor
    severity_threshold: str  # Minimum severity level
    cooldown_minutes: int  # Minimum time between similar alerts
    enabled: bool = True
    description: str = ""


@dataclass
class AlertChannel:
    """Configuration for an alert delivery channel"""
    name: str
    channel_type: str  # 'email', 'file', 'webhook', 'callback'
    config: Dict[str, Any]  # Channel-specific configuration
    enabled: bool = True


@dataclass
class Alert:
    """Alert message"""
    timestamp: datetime
    rule_name: str
    event: AdvancedEvent
    message: str
    channel: str
    delivered: bool = False
    delivery_attempts: int = 0


class AlertSystem:
    """
    Real-time alert system for clustering events and regime changes
    
    Features:
    - Multiple delivery channels (email, file, webhook, callback)
    - Configurable alert rules with cooldown periods
    - Alert history and delivery tracking
    - Asynchronous alert processing
    """
    
    def __init__(self, config_file: str = "alert_config.json"):
        """
        Initialize alert system
        
        Args:
            config_file: Path to alert configuration file
        """
        self.config_file = config_file
        self.rules: Dict[str, AlertRule] = {}
        self.channels: Dict[str, AlertChannel] = {}
        self.alert_history: List[Alert] = []
        self.last_alert_times: Dict[str, datetime] = {}
        
        # Alert processing queue and thread
        self.alert_queue = queue.Queue()
        self.processing_thread = None
        self.stop_processing = threading.Event()
        
        # Load configuration
        self._load_config()
        
        # Start alert processing thread
        self._start_processing_thread()
    
    def _load_config(self):
        """Load alert configuration from file"""
        try:
            config_path = Path(self.config_file)
            if config_path.exists():
                with open(config_path, 'r') as f:
                    config = json.load(f)
                
                # Load rules
                for rule_data in config.get('rules', []):
                    rule = AlertRule(**rule_data)
                    self.rules[rule.name] = rule
                
                # Load channels
                for channel_data in config.get('channels', []):
                    channel = AlertChannel(**channel_data)
                    self.channels[channel.name] = channel
                    
                logger.info(f"Loaded {len(self.rules)} alert rules and {len(self.channels)} channels")
            else:
                # Create default configuration
                self._create_default_config()
                
        except Exception as e:
            logger.error(f"Error loading alert configuration: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """Create default alert configuration"""
        # Default alert rules
        self.rules = {
            "regime_changes": AlertRule(
                name="regime_changes",
                event_types=["regime_change"],
                severity_threshold="medium",
                cooldown_minutes=5,
                description="Alert on market regime changes"
            ),
            "high_severity_events": AlertRule(
                name="high_severity_events",
                event_types=["market_fragmentation", "cluster_count_change", "quality_degradation"],
                severity_threshold="high",
                cooldown_minutes=10,
                description="Alert on high-severity clustering events"
            ),
            "critical_events": AlertRule(
                name="critical_events",
                event_types=["regime_change", "market_fragmentation"],
                severity_threshold="critical",
                cooldown_minutes=1,
                description="Immediate alerts for critical events"
            )
        }
        
        # Default alert channels
        self.channels = {
            "file_log": AlertChannel(
                name="file_log",
                channel_type="file",
                config={"file_path": "alerts.log"},
                enabled=True
            ),
            "console": AlertChannel(
                name="console",
                channel_type="callback",
                config={"callback": "console_print"},
                enabled=True
            )
        }
        
        # Save default configuration
        self._save_config()
    
    def _save_config(self):
        """Save current configuration to file"""
        try:
            config = {
                "rules": [asdict(rule) for rule in self.rules.values()],
                "channels": [asdict(channel) for channel in self.channels.values()]
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error saving alert configuration: {e}")
    
    def add_rule(self, rule: AlertRule):
        """Add or update an alert rule"""
        self.rules[rule.name] = rule
        self._save_config()
        logger.info(f"Added alert rule: {rule.name}")
    
    def add_channel(self, channel: AlertChannel):
        """Add or update an alert channel"""
        self.channels[channel.name] = channel
        self._save_config()
        logger.info(f"Added alert channel: {channel.name}")
    
    def process_event(self, event: AdvancedEvent):
        """
        Process an event and generate alerts if rules match
        
        Args:
            event: Event to process
        """
        try:
            for rule_name, rule in self.rules.items():
                if not rule.enabled:
                    continue
                
                # Check if event matches rule criteria
                if self._event_matches_rule(event, rule):
                    # Check cooldown period
                    if self._is_in_cooldown(rule_name, rule.cooldown_minutes):
                        continue
                    
                    # Generate alert
                    alert_message = self._generate_alert_message(event, rule)
                    
                    # Queue alert for delivery
                    for channel_name, channel in self.channels.items():
                        if channel.enabled:
                            alert = Alert(
                                timestamp=datetime.now(MARKET_TIMEZONE),
                                rule_name=rule_name,
                                event=event,
                                message=alert_message,
                                channel=channel_name
                            )
                            self.alert_queue.put(alert)
                    
                    # Update last alert time
                    self.last_alert_times[rule_name] = datetime.now(MARKET_TIMEZONE)
                    
        except Exception as e:
            logger.error(f"Error processing event for alerts: {e}")
    
    def _event_matches_rule(self, event: AdvancedEvent, rule: AlertRule) -> bool:
        """Check if event matches alert rule criteria"""
        # Check event type
        if event.event_type not in rule.event_types:
            return False
        
        # Check severity threshold
        severity_levels = {"low": 1, "medium": 2, "high": 3, "critical": 4}
        event_severity = severity_levels.get(event.severity, 0)
        rule_threshold = severity_levels.get(rule.severity_threshold, 0)
        
        return event_severity >= rule_threshold
    
    def _is_in_cooldown(self, rule_name: str, cooldown_minutes: int) -> bool:
        """Check if rule is in cooldown period"""
        if rule_name not in self.last_alert_times:
            return False
        
        last_alert = self.last_alert_times[rule_name]
        cooldown_period = timedelta(minutes=cooldown_minutes)
        
        return datetime.now(MARKET_TIMEZONE) - last_alert < cooldown_period
    
    def _generate_alert_message(self, event: AdvancedEvent, rule: AlertRule) -> str:
        """Generate formatted alert message"""
        timestamp_str = event.timestamp.strftime("%Y-%m-%d %H:%M:%S %Z")
        
        message = f"""
CLUSTERING ALERT - {rule.name.upper()}
=====================================

Time: {timestamp_str}
Event Type: {event.event_type}
Severity: {event.severity.upper()}

Description: {event.description}

Affected Symbols: {', '.join(event.affected_symbols)}

Metrics:
"""
        
        for key, value in event.metrics.items():
            if isinstance(value, float):
                message += f"  {key}: {value:.4f}\n"
            else:
                message += f"  {key}: {value}\n"
        
        if event.regime_change:
            message += f"\nRegime Information:\n"
            message += f"  Type: {event.regime_change.regime_type.value}\n"
            message += f"  Confidence: {event.regime_change.confidence:.3f}\n"
            message += f"  Fragmentation: {event.regime_change.fragmentation_index:.3f}\n"
        
        return message
    
    def _start_processing_thread(self):
        """Start the alert processing thread"""
        if self.processing_thread is None or not self.processing_thread.is_alive():
            self.stop_processing.clear()
            self.processing_thread = threading.Thread(target=self._process_alerts, daemon=True)
            self.processing_thread.start()
            logger.info("Alert processing thread started")
    
    def _process_alerts(self):
        """Process alerts from the queue"""
        while not self.stop_processing.is_set():
            try:
                # Get alert from queue with timeout
                alert = self.alert_queue.get(timeout=1.0)
                
                # Deliver alert
                self._deliver_alert(alert)
                
                # Store in history
                self.alert_history.append(alert)
                
                # Limit history size
                if len(self.alert_history) > 1000:
                    self.alert_history = self.alert_history[-500:]
                
                self.alert_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error processing alert: {e}")
    
    def _deliver_alert(self, alert: Alert):
        """Deliver alert through specified channel"""
        try:
            channel = self.channels.get(alert.channel)
            if not channel or not channel.enabled:
                return
            
            if channel.channel_type == "file":
                self._deliver_file_alert(alert, channel)
            elif channel.channel_type == "email":
                self._deliver_email_alert(alert, channel)
            elif channel.channel_type == "webhook":
                self._deliver_webhook_alert(alert, channel)
            elif channel.channel_type == "callback":
                self._deliver_callback_alert(alert, channel)
            
            alert.delivered = True
            logger.info(f"Alert delivered via {alert.channel}: {alert.rule_name}")
            
        except Exception as e:
            alert.delivery_attempts += 1
            logger.error(f"Failed to deliver alert via {alert.channel}: {e}")
    
    def _deliver_file_alert(self, alert: Alert, channel: AlertChannel):
        """Deliver alert to file"""
        file_path = channel.config.get("file_path", "alerts.log")
        
        with open(file_path, 'a', encoding='utf-8') as f:
            f.write(f"\n{alert.message}\n")
            f.write("=" * 50 + "\n")
    
    def _deliver_email_alert(self, alert: Alert, channel: AlertChannel):
        """Deliver alert via email"""
        # Email delivery implementation would go here
        # Requires SMTP configuration in channel.config
        pass
    
    def _deliver_webhook_alert(self, alert: Alert, channel: AlertChannel):
        """Deliver alert via webhook"""
        # Webhook delivery implementation would go here
        # Requires URL and authentication in channel.config
        pass
    
    def _deliver_callback_alert(self, alert: Alert, channel: AlertChannel):
        """Deliver alert via callback function"""
        callback_name = channel.config.get("callback")
        if callback_name == "console_print":
            print(f"\n🚨 CLUSTERING ALERT: {alert.rule_name}")
            print(f"Event: {alert.event.event_type} ({alert.event.severity})")
            print(f"Description: {alert.event.description}")
            print(f"Time: {alert.timestamp.strftime('%H:%M:%S')}")
    
    def get_alert_history(self, hours_back: int = 24) -> List[Dict[str, Any]]:
        """Get alert history for specified time period"""
        cutoff_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=hours_back)
        recent_alerts = [a for a in self.alert_history if a.timestamp >= cutoff_time]
        
        return [
            {
                "timestamp": alert.timestamp.isoformat(),
                "rule_name": alert.rule_name,
                "event_type": alert.event.event_type,
                "severity": alert.event.severity,
                "description": alert.event.description,
                "channel": alert.channel,
                "delivered": alert.delivered
            }
            for alert in recent_alerts
        ]
    
    def stop(self):
        """Stop the alert system"""
        self.stop_processing.set()
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5.0)
        logger.info("Alert system stopped")
