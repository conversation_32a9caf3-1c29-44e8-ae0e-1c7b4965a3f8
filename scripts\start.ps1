# Dynamic FX Clustering Application - Startup Script
# PowerShell script to start the application with proper environment setup

param(
    [switch]$Debug,
    [switch]$NoMT5Check,
    [string]$Port = "8050",
    [string]$Host = "localhost"
)

Write-Host "=== Dynamic FX Clustering Application Startup ===" -ForegroundColor Cyan

# Function to check if MetaTrader 5 is running
function Test-MT5Running {
    $mt5Process = Get-Process -Name "terminal64" -ErrorAction SilentlyContinue
    if ($mt5Process) {
        Write-Host "✓ MetaTrader 5 is running (PID: $($mt5Process.Id))" -ForegroundColor Green
        return $true
    } else {
        Write-Host "⚠ MetaTrader 5 is not running" -ForegroundColor Yellow
        return $false
    }
}

# Function to check port availability
function Test-PortAvailable {
    param($Port)
    try {
        $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $Port)
        $listener.Start()
        $listener.Stop()
        return $true
    } catch {
        return $false
    }
}

# Function to set environment variables for performance
function Set-PerformanceEnvironment {
    Write-Host "Setting performance environment variables..." -ForegroundColor Cyan
    
    # OpenBLAS threading configuration
    $env:OPENBLAS_NUM_THREADS = "1"
    $env:MKL_NUM_THREADS = "1"
    $env:NUMEXPR_NUM_THREADS = "1"
    $env:OMP_NUM_THREADS = "1"
    
    # Python optimization
    $env:PYTHONOPTIMIZE = "1"
    
    Write-Host "✓ Performance environment configured" -ForegroundColor Green
}

# Step 1: Pre-flight checks
Write-Host "`n--- Pre-flight Checks ---" -ForegroundColor Yellow

# Check if we're in the correct directory
if (-not (Test-Path "run_clustering_app.py")) {
    Write-Host "✗ run_clustering_app.py not found in current directory" -ForegroundColor Red
    Write-Host "Please run this script from the application root directory" -ForegroundColor Red
    exit 1
}

# Check Python
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Python not found or not accessible" -ForegroundColor Red
    exit 1
}

# Check MetaTrader 5
if (-not $NoMT5Check) {
    $mt5Running = Test-MT5Running
    if (-not $mt5Running) {
        Write-Host "Please start MetaTrader 5 and log in to your account" -ForegroundColor Yellow
        Write-Host "Or use -NoMT5Check flag to skip this check" -ForegroundColor Yellow
        
        $response = Read-Host "Continue anyway? (y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            exit 1
        }
    }
}

# Check port availability
if (-not (Test-PortAvailable $Port)) {
    Write-Host "⚠ Port $Port is already in use" -ForegroundColor Yellow
    $newPort = [int]$Port + 1
    Write-Host "Trying port $newPort..." -ForegroundColor Cyan
    
    if (Test-PortAvailable $newPort) {
        $Port = $newPort.ToString()
        Write-Host "✓ Using port $Port" -ForegroundColor Green
    } else {
        Write-Host "✗ Could not find available port" -ForegroundColor Red
        exit 1
    }
}

# Step 2: Environment setup
Write-Host "`n--- Environment Setup ---" -ForegroundColor Yellow
Set-PerformanceEnvironment

# Step 3: Create data directories if needed
$dataDirs = @("data", "data/clustering", "data/state", "exports")
foreach ($dir in $dataDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ Created directory: $dir" -ForegroundColor Green
    }
}

# Step 4: Start the application
Write-Host "`n--- Starting Application ---" -ForegroundColor Yellow

try {
    Write-Host "Starting Dynamic FX Clustering Application..." -ForegroundColor Cyan
    Write-Host "Dashboard will be available at: http://${Host}:${Port}" -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Yellow
    Write-Host ""
    
    if ($Debug) {
        Write-Host "Debug mode enabled - verbose logging active" -ForegroundColor Magenta
        $env:DASH_DEBUG = "True"
        $env:CLUSTERING_LOG_LEVEL = "DEBUG"
    }
    
    # Start the application
    python run_clustering_app.py --host $Host --port $Port
    
} catch {
    Write-Host "`n✗ Application startup failed: $_" -ForegroundColor Red
    
    Write-Host "`nTroubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Check that all dependencies are installed: pip install -r requirements.txt" -ForegroundColor White
    Write-Host "2. Verify MetaTrader 5 is running and logged in" -ForegroundColor White
    Write-Host "3. Check the error logs for specific issues" -ForegroundColor White
    Write-Host "4. Try running with -Debug flag for more information" -ForegroundColor White
    
    exit 1
}

Write-Host "`nApplication stopped." -ForegroundColor Gray
