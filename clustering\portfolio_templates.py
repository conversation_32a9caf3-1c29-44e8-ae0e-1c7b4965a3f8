"""
Portfolio Construction Templates for MPT Integration

This module generates comprehensive portfolio templates based on different
investment strategies, ready for export to the MPT (Modern Portfolio Theory) project.

Templates include:
1. Conservative Risk-Parity Portfolio
2. Balanced Cluster-Diversified Portfolio  
3. Aggressive Maximum-Sharpe Portfolio
4. Regime-Adaptive Dynamic Portfolio
5. Equal Risk Contribution (ERC) Portfolio

Each template includes:
- Asset allocation weights
- Risk metrics
- Expected performance characteristics
- Rebalancing guidelines
- Export format for MPT project
"""

import numpy as np
import pandas as pd
import json
from typing import Dict, List, Optional, NamedTuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

from portfolio_analytics import PortfolioAnalytics
from risk_optimization import RiskOptimizer, OptimizationResult
from dynamic_rebalancing import DynamicRebalancer, RebalancingThresholds

import logging
logger = logging.getLogger(__name__)


class PortfolioStrategy(Enum):
    """Portfolio strategy types"""
    CONSERVATIVE = "conservative"
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"
    REGIME_ADAPTIVE = "regime_adaptive"
    EQUAL_RISK_CONTRIBUTION = "equal_risk_contribution"


@dataclass
class PortfolioCharacteristics:
    """Portfolio performance and risk characteristics"""
    expected_return: float
    expected_volatility: float
    sharpe_ratio: float
    max_drawdown_estimate: float
    diversification_ratio: float
    risk_concentration: float
    turnover_estimate: float


@dataclass
class RebalancingGuidelines:
    """Guidelines for portfolio rebalancing"""
    frequency: str  # "monthly", "quarterly", "regime_based", "threshold_based"
    volatility_threshold: float
    correlation_threshold: float
    drift_threshold: float
    minimum_interval_days: int
    emergency_triggers: List[str]


@dataclass
class PortfolioTemplate:
    """Complete portfolio template for MPT export"""
    name: str
    strategy: PortfolioStrategy
    description: str
    weights: Dict[str, float]
    characteristics: PortfolioCharacteristics
    rebalancing_guidelines: RebalancingGuidelines
    cluster_constraints: Optional[Dict[int, float]]
    risk_budgets: Dict[str, float]
    creation_timestamp: datetime
    data_requirements: List[str]
    implementation_notes: str


class PortfolioTemplateGenerator:
    """
    Generates comprehensive portfolio templates for different investment strategies
    """
    
    def __init__(self, symbols: List[str]):
        """
        Initialize template generator
        
        Args:
            symbols: List of asset symbols
        """
        self.symbols = symbols
        self.portfolio_analytics = PortfolioAnalytics(symbols)
        self.risk_optimizer = RiskOptimizer(symbols)
        self.rebalancer = DynamicRebalancer(symbols)
    
    def generate_all_templates(self, 
                             correlation_matrix: np.ndarray,
                             volatility_profiles: Dict[str, float],
                             cluster_assignments: List[int],
                             expected_returns: Optional[np.ndarray] = None) -> List[PortfolioTemplate]:
        """
        Generate all portfolio templates
        
        Args:
            correlation_matrix: Asset correlation matrix
            volatility_profiles: Volatility for each asset
            cluster_assignments: Cluster assignments for assets
            expected_returns: Expected returns (optional, estimated if not provided)
            
        Returns:
            List of portfolio templates
        """
        try:
            # Estimate expected returns if not provided
            if expected_returns is None:
                expected_returns = self._estimate_expected_returns(volatility_profiles)
            
            templates = []
            
            # 1. Conservative Risk-Parity Portfolio
            conservative_template = self._create_conservative_template(
                correlation_matrix, volatility_profiles, cluster_assignments
            )
            templates.append(conservative_template)
            
            # 2. Balanced Cluster-Diversified Portfolio
            balanced_template = self._create_balanced_template(
                correlation_matrix, volatility_profiles, cluster_assignments, expected_returns
            )
            templates.append(balanced_template)
            
            # 3. Aggressive Maximum-Sharpe Portfolio
            aggressive_template = self._create_aggressive_template(
                correlation_matrix, volatility_profiles, expected_returns
            )
            templates.append(aggressive_template)
            
            # 4. Regime-Adaptive Dynamic Portfolio
            regime_adaptive_template = self._create_regime_adaptive_template(
                correlation_matrix, volatility_profiles, cluster_assignments, expected_returns
            )
            templates.append(regime_adaptive_template)
            
            # 5. Equal Risk Contribution Portfolio
            erc_template = self._create_erc_template(
                correlation_matrix, volatility_profiles
            )
            templates.append(erc_template)
            
            logger.info(f"Generated {len(templates)} portfolio templates")
            return templates
            
        except Exception as e:
            logger.error(f"Error generating portfolio templates: {str(e)}")
            return []
    
    def _estimate_expected_returns(self, volatility_profiles: Dict[str, float]) -> np.ndarray:
        """Estimate expected returns based on volatility (simplified approach)"""
        # Realistic risk premium model for FX markets
        base_return = 0.03  # 3% base return (more realistic for FX)
        risk_premium_factor = 0.5  # Lower risk premium for FX

        expected_returns = []
        for symbol in self.symbols:
            vol = volatility_profiles.get(symbol, 0.01)
            # Calculate expected return with caps
            expected_return = base_return + (vol * risk_premium_factor)
            # Cap at reasonable levels for FX (max 15% annually)
            expected_return = min(expected_return, 0.15)
            # Ensure minimum positive expected return (1% annually)
            expected_return = max(expected_return, 0.01)
            expected_returns.append(expected_return)

        return np.array(expected_returns)

    def _normalize_weights_with_negatives(self, weights: Dict[str, float]) -> Dict[str, float]:
        """Normalize weights to absolute sum = 1, allowing negative weights (-1 to 1)"""
        try:
            # Calculate absolute sum
            abs_sum = sum(abs(weight) for weight in weights.values())

            if abs_sum <= 1e-15:
                # If all weights are zero, return equal weights
                equal_weight = 1.0 / len(self.symbols)
                return {symbol: equal_weight for symbol in self.symbols}

            # Normalize by absolute sum
            normalized_weights = {symbol: weight / abs_sum for symbol, weight in weights.items()}

            # Ensure weights are within bounds [-1, 1]
            for symbol in normalized_weights:
                normalized_weights[symbol] = max(-1.0, min(1.0, normalized_weights[symbol]))

            # Verify absolute sum is 1 (within tolerance)
            final_abs_sum = sum(abs(weight) for weight in normalized_weights.values())
            if abs(final_abs_sum - 1.0) > 1e-6:
                # Re-normalize if needed
                normalized_weights = {symbol: weight / final_abs_sum for symbol, weight in normalized_weights.items()}

            return normalized_weights

        except Exception as e:
            logger.error(f"Error normalizing weights: {str(e)}")
            # Fallback to equal weights
            equal_weight = 1.0 / len(self.symbols)
            return {symbol: equal_weight for symbol in self.symbols}
    
    def _create_conservative_template(self, 
                                    correlation_matrix: np.ndarray,
                                    volatility_profiles: Dict[str, float],
                                    cluster_assignments: List[int]) -> PortfolioTemplate:
        """Create conservative risk-parity portfolio template"""
        try:
            # Use risk parity optimization
            covariance_matrix = self.risk_optimizer.calculate_covariance_matrix(
                correlation_matrix, volatility_profiles
            )
            
            optimization_result = self.risk_optimizer.optimize_risk_parity(covariance_matrix)
            
            if not optimization_result.success:
                # Fallback to inverse volatility weights
                weights = self.portfolio_analytics._create_volatility_focused_portfolio(
                    volatility_profiles, "low"
                )
            else:
                weights = optimization_result.weights

            # Normalize weights to absolute sum = 1 with range [-1, 1]
            weights = self._normalize_weights_with_negatives(weights)
            
            # Calculate characteristics
            portfolio_risk = self.portfolio_analytics.calculate_portfolio_risk(
                weights, correlation_matrix, volatility_profiles
            )
            
            # Calculate risk budgets
            risk_budgets = self.risk_optimizer.calculate_risk_budgets(weights, covariance_matrix)
            risk_budget_dict = {rb.symbol: rb.risk_percentage for rb in risk_budgets}
            
            # Calculate diversification metrics
            diversification_metrics = self.portfolio_analytics.calculate_diversification_matrix(
                correlation_matrix, cluster_assignments
            )
            
            # Calculate expected portfolio return
            expected_returns = self._estimate_expected_returns(volatility_profiles)
            portfolio_expected_return = sum(weights.get(symbol, 0.0) * expected_returns[i]
                                          for i, symbol in enumerate(self.symbols))

            # Calculate Sharpe ratio
            risk_free_rate = 0.02  # 2% risk-free rate
            sharpe_ratio = (portfolio_expected_return - risk_free_rate) / portfolio_risk if portfolio_risk > 0 else 0.0

            characteristics = PortfolioCharacteristics(
                expected_return=portfolio_expected_return,
                expected_volatility=portfolio_risk,
                sharpe_ratio=sharpe_ratio,
                max_drawdown_estimate=portfolio_risk * 2.5,
                diversification_ratio=1.2,  # Conservative diversification
                risk_concentration=max(risk_budget_dict.values()) if risk_budget_dict else 0.0,
                turnover_estimate=0.1  # Low turnover
            )
            
            rebalancing_guidelines = RebalancingGuidelines(
                frequency="quarterly",
                volatility_threshold=0.5,
                correlation_threshold=0.3,
                drift_threshold=0.05,
                minimum_interval_days=60,
                emergency_triggers=["volatility_spike", "correlation_breakdown"]
            )
            
            return PortfolioTemplate(
                name="Conservative Risk-Parity Portfolio",
                strategy=PortfolioStrategy.CONSERVATIVE,
                description="Low-risk portfolio using risk parity principles with equal risk contribution from all assets",
                weights=weights,
                characteristics=characteristics,
                rebalancing_guidelines=rebalancing_guidelines,
                cluster_constraints=None,
                risk_budgets=risk_budget_dict,
                creation_timestamp=datetime.now(),
                data_requirements=["correlation_matrix", "volatility_profiles"],
                implementation_notes="Focus on capital preservation with steady returns. Suitable for risk-averse investors."
            )
            
        except Exception as e:
            logger.error(f"Error creating conservative template: {str(e)}")
            # Return equal weight fallback
            equal_weight = 1.0 / len(self.symbols)
            weights = {symbol: equal_weight for symbol in self.symbols}
            
            return PortfolioTemplate(
                name="Conservative Equal-Weight Portfolio (Fallback)",
                strategy=PortfolioStrategy.CONSERVATIVE,
                description="Equal-weight portfolio as fallback",
                weights=weights,
                characteristics=PortfolioCharacteristics(0.02, 0.1, 0.2, 0.2, 1.0, 1.0/len(self.symbols), 0.05),
                rebalancing_guidelines=RebalancingGuidelines("quarterly", 0.5, 0.3, 0.05, 60, []),
                cluster_constraints=None,
                risk_budgets={symbol: 1.0/len(self.symbols) for symbol in self.symbols},
                creation_timestamp=datetime.now(),
                data_requirements=[],
                implementation_notes="Fallback equal-weight allocation"
            )
    
    def _create_balanced_template(self, 
                                correlation_matrix: np.ndarray,
                                volatility_profiles: Dict[str, float],
                                cluster_assignments: List[int],
                                expected_returns: np.ndarray) -> PortfolioTemplate:
        """Create balanced cluster-diversified portfolio template"""
        try:
            # Use cluster-diversified allocation
            weights = self.portfolio_analytics._create_cluster_diversified_portfolio(
                cluster_assignments, volatility_profiles
            )

            # Normalize weights to absolute sum = 1 with range [-1, 1]
            weights = self._normalize_weights_with_negatives(weights)
            
            # Calculate characteristics
            portfolio_risk = self.portfolio_analytics.calculate_portfolio_risk(
                weights, correlation_matrix, volatility_profiles
            )
            
            portfolio_return = sum(weights[symbol] * expected_returns[i] 
                                 for i, symbol in enumerate(self.symbols))
            
            sharpe_ratio = (portfolio_return - 0.02) / portfolio_risk if portfolio_risk > 0 else 0
            
            # Calculate cluster constraints for rebalancing
            unique_clusters = list(set(cluster_assignments))
            cluster_weights = {}
            for cluster_id in unique_clusters:
                cluster_symbols = [self.symbols[i] for i, c in enumerate(cluster_assignments) if c == cluster_id]
                cluster_weight = sum(weights.get(symbol, 0.0) for symbol in cluster_symbols)
                cluster_weights[cluster_id] = cluster_weight
            
            characteristics = PortfolioCharacteristics(
                expected_return=portfolio_return,
                expected_volatility=portfolio_risk,
                sharpe_ratio=sharpe_ratio,
                max_drawdown_estimate=portfolio_risk * 2.0,
                diversification_ratio=1.5,
                risk_concentration=0.3,  # Moderate concentration
                turnover_estimate=0.15
            )
            
            rebalancing_guidelines = RebalancingGuidelines(
                frequency="monthly",
                volatility_threshold=0.4,
                correlation_threshold=0.25,
                drift_threshold=0.08,
                minimum_interval_days=30,
                emergency_triggers=["cluster_change", "volatility_regime_shift"]
            )
            
            return PortfolioTemplate(
                name="Balanced Cluster-Diversified Portfolio",
                strategy=PortfolioStrategy.BALANCED,
                description="Balanced portfolio using cluster analysis for optimal diversification across currency groups",
                weights=weights,
                characteristics=characteristics,
                rebalancing_guidelines=rebalancing_guidelines,
                cluster_constraints=cluster_weights,
                risk_budgets={symbol: weights[symbol] for symbol in self.symbols},
                creation_timestamp=datetime.now(),
                data_requirements=["correlation_matrix", "volatility_profiles", "cluster_assignments"],
                implementation_notes="Balances risk and return using cluster-based diversification. Suitable for moderate risk tolerance."
            )
            
        except Exception as e:
            logger.error(f"Error creating balanced template: {str(e)}")
            return self._create_fallback_template("Balanced Portfolio (Fallback)", PortfolioStrategy.BALANCED)

    def _create_aggressive_template(self,
                                  correlation_matrix: np.ndarray,
                                  volatility_profiles: Dict[str, float],
                                  expected_returns: np.ndarray) -> PortfolioTemplate:
        """Create aggressive maximum-Sharpe portfolio template"""
        try:
            # Use maximum Sharpe ratio optimization
            covariance_matrix = self.risk_optimizer.calculate_covariance_matrix(
                correlation_matrix, volatility_profiles
            )

            optimization_result = self.risk_optimizer.optimize_maximum_sharpe(
                covariance_matrix, expected_returns
            )

            if not optimization_result.success:
                # Fallback to high volatility focused portfolio
                weights = self.portfolio_analytics._create_volatility_focused_portfolio(
                    volatility_profiles, "high"
                )
                # Normalize weights to absolute sum = 1 with range [-1, 1]
                weights = self._normalize_weights_with_negatives(weights)

                portfolio_return = sum(weights[symbol] * expected_returns[i]
                                     for i, symbol in enumerate(self.symbols))
                portfolio_risk = self.portfolio_analytics.calculate_portfolio_risk(
                    weights, correlation_matrix, volatility_profiles
                )
                sharpe_ratio = (portfolio_return - 0.02) / portfolio_risk if portfolio_risk > 0 else 0
            else:
                weights = optimization_result.weights
                portfolio_return = optimization_result.expected_return
                portfolio_risk = optimization_result.expected_risk
                sharpe_ratio = optimization_result.sharpe_ratio

            # Normalize weights to absolute sum = 1 with range [-1, 1]
            weights = self._normalize_weights_with_negatives(weights)

            characteristics = PortfolioCharacteristics(
                expected_return=portfolio_return,
                expected_volatility=portfolio_risk,
                sharpe_ratio=sharpe_ratio,
                max_drawdown_estimate=portfolio_risk * 3.0,  # Higher drawdown potential
                diversification_ratio=1.8,
                risk_concentration=0.5,  # Higher concentration allowed
                turnover_estimate=0.25  # Higher turnover
            )

            rebalancing_guidelines = RebalancingGuidelines(
                frequency="monthly",
                volatility_threshold=0.3,
                correlation_threshold=0.2,
                drift_threshold=0.12,
                minimum_interval_days=15,
                emergency_triggers=["sharp_drawdown", "correlation_breakdown", "volatility_spike"]
            )

            return PortfolioTemplate(
                name="Aggressive Maximum-Sharpe Portfolio",
                strategy=PortfolioStrategy.AGGRESSIVE,
                description="High-return seeking portfolio optimized for maximum risk-adjusted returns",
                weights=weights,
                characteristics=characteristics,
                rebalancing_guidelines=rebalancing_guidelines,
                cluster_constraints=None,
                risk_budgets={symbol: weights[symbol] for symbol in self.symbols},
                creation_timestamp=datetime.now(),
                data_requirements=["correlation_matrix", "volatility_profiles", "expected_returns"],
                implementation_notes="Targets maximum Sharpe ratio. Suitable for high risk tolerance investors seeking optimal risk-adjusted returns."
            )

        except Exception as e:
            logger.error(f"Error creating aggressive template: {str(e)}")
            return self._create_fallback_template("Aggressive Portfolio (Fallback)", PortfolioStrategy.AGGRESSIVE)

    def _create_regime_adaptive_template(self,
                                       correlation_matrix: np.ndarray,
                                       volatility_profiles: Dict[str, float],
                                       cluster_assignments: List[int],
                                       expected_returns: np.ndarray) -> PortfolioTemplate:
        """Create regime-adaptive dynamic portfolio template"""
        try:
            # Debug: Check expected_returns type
            logger.debug(f"Expected returns type: {type(expected_returns)}")
            logger.debug(f"Expected returns shape: {expected_returns.shape if hasattr(expected_returns, 'shape') else 'No shape'}")

            # Create regime-adaptive allocation using volatility-based weighting
            # This is different from cluster-diversified approach
            avg_volatility = np.mean(list(volatility_profiles.values()))

            # Create adaptive weights based on volatility regime
            weights = {}
            total_weight = 0.0

            for symbol in self.symbols:
                vol = volatility_profiles.get(symbol, 0.01)

                if avg_volatility > 0.015:  # High volatility regime
                    # Favor low volatility assets with inverse weighting
                    weight = 1.0 / (vol + 0.001)  # Add small constant to avoid division by zero
                    regime_description = "Currently in high volatility regime - defensive positioning"
                else:  # Low volatility regime
                    # More balanced approach with slight volatility bias
                    weight = 1.0 / np.sqrt(vol + 0.001)  # Square root for moderate bias
                    regime_description = "Currently in normal volatility regime - balanced positioning"

                weights[symbol] = weight
                total_weight += weight

            # Normalize to sum = 1 first
            if total_weight > 0:
                weights = {symbol: weight / total_weight for symbol, weight in weights.items()}

            # Normalize weights to absolute sum = 1 with range [-1, 1]
            weights = self._normalize_weights_with_negatives(weights)

            portfolio_risk = self.portfolio_analytics.calculate_portfolio_risk(
                weights, correlation_matrix, volatility_profiles
            )

            # Debug: Check types before calculation
            logger.debug(f"Weights type: {type(weights)}")
            logger.debug(f"Expected returns type before calculation: {type(expected_returns)}")

            portfolio_return = sum(weights[symbol] * expected_returns[i]
                                 for i, symbol in enumerate(self.symbols))

            sharpe_ratio = (portfolio_return - 0.02) / portfolio_risk if portfolio_risk > 0 else 0

            characteristics = PortfolioCharacteristics(
                expected_return=portfolio_return,
                expected_volatility=portfolio_risk,
                sharpe_ratio=sharpe_ratio,
                max_drawdown_estimate=portfolio_risk * 2.2,
                diversification_ratio=1.6,
                risk_concentration=0.35,
                turnover_estimate=0.3  # Higher due to regime changes
            )

            rebalancing_guidelines = RebalancingGuidelines(
                frequency="regime_based",
                volatility_threshold=0.2,  # More sensitive
                correlation_threshold=0.15,  # More sensitive
                drift_threshold=0.06,
                minimum_interval_days=7,  # More frequent
                emergency_triggers=["volatility_regime_shift", "correlation_regime_shift", "cluster_instability"]
            )

            return PortfolioTemplate(
                name="Regime-Adaptive Dynamic Portfolio",
                strategy=PortfolioStrategy.REGIME_ADAPTIVE,
                description=f"Dynamic portfolio that adapts to market regimes. {regime_description}",
                weights=weights,
                characteristics=characteristics,
                rebalancing_guidelines=rebalancing_guidelines,
                cluster_constraints=None,
                risk_budgets={symbol: weights[symbol] for symbol in self.symbols},
                creation_timestamp=datetime.now(),
                data_requirements=["correlation_matrix", "volatility_profiles", "cluster_assignments", "regime_classification"],
                implementation_notes="Adapts allocation based on volatility and correlation regimes. Requires active monitoring and frequent rebalancing."
            )

        except Exception as e:
            logger.error(f"Error creating regime adaptive template: {str(e)}")
            return self._create_fallback_template("Regime-Adaptive Portfolio (Fallback)", PortfolioStrategy.REGIME_ADAPTIVE)

    def _create_erc_template(self,
                           correlation_matrix: np.ndarray,
                           volatility_profiles: Dict[str, float]) -> PortfolioTemplate:
        """Create Equal Risk Contribution (ERC) portfolio template"""
        try:
            # Use equal risk contribution approach (different from risk parity)
            # ERC aims for equal marginal risk contribution from each asset
            covariance_matrix = self.risk_optimizer.calculate_covariance_matrix(
                correlation_matrix, volatility_profiles
            )

            # Try minimum variance optimization first for ERC
            optimization_result = self.risk_optimizer.optimize_minimum_variance(covariance_matrix)

            if not optimization_result.success:
                # Fallback to equal risk weighting (inverse volatility squared)
                weights = {}
                total_inv_vol_sq = 0.0

                for symbol in self.symbols:
                    vol = volatility_profiles.get(symbol, 0.01)
                    inv_vol_sq = 1.0 / (vol * vol) if vol > 0 else 1.0
                    weights[symbol] = inv_vol_sq
                    total_inv_vol_sq += inv_vol_sq

                # Normalize
                weights = {symbol: weight / total_inv_vol_sq for symbol, weight in weights.items()}
            else:
                weights = optimization_result.weights

            # Normalize weights to absolute sum = 1 with range [-1, 1]
            weights = self._normalize_weights_with_negatives(weights)

            # Calculate risk budgets to verify ERC
            risk_budgets = self.risk_optimizer.calculate_risk_budgets(weights, covariance_matrix)
            risk_budget_dict = {rb.symbol: rb.risk_percentage for rb in risk_budgets}

            portfolio_risk = self.portfolio_analytics.calculate_portfolio_risk(
                weights, correlation_matrix, volatility_profiles
            )

            # ERC portfolios typically have moderate expected returns
            expected_return = 0.04
            sharpe_ratio = (expected_return - 0.02) / portfolio_risk if portfolio_risk > 0 else 0

            characteristics = PortfolioCharacteristics(
                expected_return=expected_return,
                expected_volatility=portfolio_risk,
                sharpe_ratio=sharpe_ratio,
                max_drawdown_estimate=portfolio_risk * 2.0,
                diversification_ratio=1.4,
                risk_concentration=max(risk_budget_dict.values()) if risk_budget_dict else 0.0,
                turnover_estimate=0.12
            )

            rebalancing_guidelines = RebalancingGuidelines(
                frequency="quarterly",
                volatility_threshold=0.4,
                correlation_threshold=0.3,
                drift_threshold=0.07,
                minimum_interval_days=45,
                emergency_triggers=["risk_budget_breach", "volatility_spike"]
            )

            return PortfolioTemplate(
                name="Equal Risk Contribution (ERC) Portfolio",
                strategy=PortfolioStrategy.EQUAL_RISK_CONTRIBUTION,
                description="Portfolio where each asset contributes equally to total portfolio risk",
                weights=weights,
                characteristics=characteristics,
                rebalancing_guidelines=rebalancing_guidelines,
                cluster_constraints=None,
                risk_budgets=risk_budget_dict,
                creation_timestamp=datetime.now(),
                data_requirements=["correlation_matrix", "volatility_profiles"],
                implementation_notes="True risk parity approach ensuring equal risk contribution from all assets. Good for diversification-focused strategies."
            )

        except Exception as e:
            logger.error(f"Error creating ERC template: {str(e)}")
            return self._create_fallback_template("ERC Portfolio (Fallback)", PortfolioStrategy.EQUAL_RISK_CONTRIBUTION)

    def _create_fallback_template(self, name: str, strategy: PortfolioStrategy) -> PortfolioTemplate:
        """Create a fallback equal-weight template"""
        equal_weight = 1.0 / len(self.symbols)
        weights = {symbol: equal_weight for symbol in self.symbols}

        return PortfolioTemplate(
            name=name,
            strategy=strategy,
            description="Equal-weight fallback portfolio",
            weights=weights,
            characteristics=PortfolioCharacteristics(0.03, 0.08, 0.25, 0.15, 1.0, equal_weight, 0.05),
            rebalancing_guidelines=RebalancingGuidelines("quarterly", 0.5, 0.3, 0.1, 60, []),
            cluster_constraints=None,
            risk_budgets={symbol: equal_weight for symbol in self.symbols},
            creation_timestamp=datetime.now(),
            data_requirements=[],
            implementation_notes="Fallback equal-weight allocation due to optimization failure"
        )

    def export_templates_for_mpt(self,
                               templates: List[PortfolioTemplate],
                               output_file: str = "portfolio_templates_for_mpt.json") -> bool:
        """
        Export portfolio templates in format suitable for MPT project

        Args:
            templates: List of portfolio templates to export
            output_file: Output file path

        Returns:
            Success status
        """
        try:
            export_data = {
                "metadata": {
                    "export_timestamp": datetime.now().isoformat(),
                    "source_project": "Dynamic FX Clustering",
                    "target_project": "MPT Portfolio Optimization",
                    "template_count": len(templates),
                    "symbols": self.symbols
                },
                "templates": []
            }

            for template in templates:
                template_data = {
                    "id": f"{template.strategy.value}_{int(template.creation_timestamp.timestamp())}",
                    "name": template.name,
                    "strategy": template.strategy.value,
                    "description": template.description,
                    "allocation": {
                        "weights": template.weights,
                        "weight_sum": sum(template.weights.values()),
                        "non_zero_positions": sum(1 for w in template.weights.values() if w > 0.001)
                    },
                    "risk_metrics": {
                        "expected_return": template.characteristics.expected_return,
                        "expected_volatility": template.characteristics.expected_volatility,
                        "sharpe_ratio": template.characteristics.sharpe_ratio,
                        "max_drawdown_estimate": template.characteristics.max_drawdown_estimate,
                        "diversification_ratio": template.characteristics.diversification_ratio,
                        "risk_concentration": template.characteristics.risk_concentration
                    },
                    "risk_budgets": template.risk_budgets,
                    "rebalancing": {
                        "frequency": template.rebalancing_guidelines.frequency,
                        "thresholds": {
                            "volatility": template.rebalancing_guidelines.volatility_threshold,
                            "correlation": template.rebalancing_guidelines.correlation_threshold,
                            "drift": template.rebalancing_guidelines.drift_threshold
                        },
                        "minimum_interval_days": template.rebalancing_guidelines.minimum_interval_days,
                        "emergency_triggers": template.rebalancing_guidelines.emergency_triggers
                    },
                    "constraints": {
                        "cluster_constraints": template.cluster_constraints,
                        "position_limits": {
                            "min_weight": min(template.weights.values()),
                            "max_weight": max(template.weights.values())
                        }
                    },
                    "implementation": {
                        "data_requirements": template.data_requirements,
                        "notes": template.implementation_notes,
                        "estimated_turnover": template.characteristics.turnover_estimate
                    },
                    "creation_info": {
                        "timestamp": template.creation_timestamp.isoformat(),
                        "optimization_success": True  # Assume success if template was created
                    }
                }

                export_data["templates"].append(template_data)

            # Write to JSON file
            with open(output_file, 'w') as f:
                json.dump(export_data, f, indent=2, default=str)

            logger.info(f"Exported {len(templates)} portfolio templates to {output_file}")
            return True

        except Exception as e:
            logger.error(f"Error exporting templates: {str(e)}")
            return False

    def generate_template_summary(self, templates: List[PortfolioTemplate]) -> pd.DataFrame:
        """
        Generate a summary DataFrame of all templates

        Args:
            templates: List of portfolio templates

        Returns:
            Summary DataFrame
        """
        try:
            summary_data = []

            for template in templates:
                summary_data.append({
                    'Name': template.name,
                    'Strategy': template.strategy.value,
                    'Expected Return': f"{template.characteristics.expected_return:.2%}",
                    'Expected Volatility': f"{template.characteristics.expected_volatility:.2%}",
                    'Sharpe Ratio': f"{template.characteristics.sharpe_ratio:.2f}",
                    'Max Drawdown Est.': f"{template.characteristics.max_drawdown_estimate:.2%}",
                    'Diversification Ratio': f"{template.characteristics.diversification_ratio:.2f}",
                    'Risk Concentration': f"{template.characteristics.risk_concentration:.2%}",
                    'Rebalancing Frequency': template.rebalancing_guidelines.frequency,
                    'Estimated Turnover': f"{template.characteristics.turnover_estimate:.1%}",
                    'Non-Zero Positions': sum(1 for w in template.weights.values() if w > 0.001),
                    'Max Position Size': f"{max(template.weights.values()):.1%}",
                    'Min Position Size': f"{min(w for w in template.weights.values() if w > 0.001):.1%}" if any(w > 0.001 for w in template.weights.values()) else "0.0%"
                })

            return pd.DataFrame(summary_data)

        except Exception as e:
            logger.error(f"Error generating template summary: {str(e)}")
            return pd.DataFrame()


def create_portfolio_templates_demo():
    """Create a demonstration of portfolio template generation"""

    # Sample currency pairs
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']

    # Initialize template generator
    generator = PortfolioTemplateGenerator(symbols)

    # Generate sample market data
    np.random.seed(42)

    # Correlation matrix
    correlation_matrix = np.random.rand(len(symbols), len(symbols))
    correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2
    np.fill_diagonal(correlation_matrix, 1.0)

    # Volatility profiles
    volatility_profiles = {
        symbol: 0.008 + np.random.exponential(0.004) for symbol in symbols
    }

    # Cluster assignments (3 clusters)
    cluster_assignments = [i % 3 for i in range(len(symbols))]

    # Expected returns
    expected_returns = np.array([0.02 + np.random.normal(0, 0.01) for _ in symbols])

    print("=== Portfolio Template Generation Demo ===")
    print(f"Symbols: {symbols}")
    print(f"Volatility profiles: {volatility_profiles}")
    print(f"Cluster assignments: {cluster_assignments}")
    print()

    # Generate all templates
    templates = generator.generate_all_templates(
        correlation_matrix=correlation_matrix,
        volatility_profiles=volatility_profiles,
        cluster_assignments=cluster_assignments,
        expected_returns=expected_returns
    )

    print(f"Generated {len(templates)} portfolio templates:")
    print()

    # Display template summary
    summary_df = generator.generate_template_summary(templates)
    print("Template Summary:")
    print(summary_df.to_string(index=False))
    print()

    # Show detailed information for each template
    for i, template in enumerate(templates):
        print(f"Template {i+1}: {template.name}")
        print(f"  Strategy: {template.strategy.value}")
        print(f"  Description: {template.description}")
        print(f"  Expected Return: {template.characteristics.expected_return:.2%}")
        print(f"  Expected Volatility: {template.characteristics.expected_volatility:.2%}")
        print(f"  Sharpe Ratio: {template.characteristics.sharpe_ratio:.2f}")
        print(f"  Rebalancing: {template.rebalancing_guidelines.frequency}")

        # Show top 3 positions
        sorted_weights = sorted(template.weights.items(), key=lambda x: x[1], reverse=True)[:3]
        print(f"  Top positions:")
        for symbol, weight in sorted_weights:
            print(f"    {symbol}: {weight:.1%}")
        print()

    # Export templates
    success = generator.export_templates_for_mpt(templates, "demo_portfolio_templates.json")
    if success:
        print("Templates exported successfully to 'demo_portfolio_templates.json'")
    else:
        print("Failed to export templates")


if __name__ == "__main__":
    create_portfolio_templates_demo()
