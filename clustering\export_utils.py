"""
Export utilities for Dynamic FX Clustering Application
====================================================

Provides data export functionality for clustering results, performance metrics,
and historical analysis data in various formats (CSV, JSON, Excel).
"""

import json
import csv
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path

from config import MARKET_TIMEZONE

logger = logging.getLogger(__name__)


class DataExporter:
    """
    Handles data export functionality for clustering application
    
    Supports:
    - Clustering state export (CSV, JSON)
    - Performance metrics export
    - Event history export
    - Volatility regime data export
    - Historical analysis export
    """
    
    def __init__(self, export_dir: str = "exports"):
        """
        Initialize data exporter
        
        Args:
            export_dir: Directory to save exported files
        """
        self.export_dir = Path(export_dir)
        self.export_dir.mkdir(exist_ok=True)
        
    def export_clustering_state(self, state_manager, format: str = "csv") -> str:
        """
        Export current clustering state
        
        Args:
            state_manager: StateManager instance
            format: Export format ('csv', 'json', 'excel')
            
        Returns:
            Path to exported file
        """
        try:
            current_state = state_manager.get_current_state()
            if not current_state:
                raise ValueError("No current state available for export")
            
            timestamp = datetime.now(MARKET_TIMEZONE).strftime("%Y%m%d_%H%M%S")
            
            # Prepare data
            export_data = {
                'timestamp': current_state.timestamp.isoformat(),
                'cluster_count': current_state.cluster_count,
                'regime_stability': current_state.regime_stability,
                'data_quality_score': current_state.data_quality_score,
                'symbols': current_state.symbols,
                'cluster_assignments': current_state.cluster_assignments
            }
            
            if format.lower() == 'csv':
                return self._export_to_csv(export_data, f"clustering_state_{timestamp}.csv")
            elif format.lower() == 'json':
                return self._export_to_json(export_data, f"clustering_state_{timestamp}.json")
            elif format.lower() == 'excel':
                return self._export_to_excel(export_data, f"clustering_state_{timestamp}.xlsx")
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting clustering state: {e}")
            raise
    
    def export_performance_metrics(self, state_manager, format: str = "csv") -> str:
        """
        Export performance metrics
        
        Args:
            state_manager: StateManager instance
            format: Export format ('csv', 'json', 'excel')
            
        Returns:
            Path to exported file
        """
        try:
            metrics = state_manager.get_performance_metrics()
            timestamp = datetime.now(MARKET_TIMEZONE).strftime("%Y%m%d_%H%M%S")
            
            # Prepare data
            export_data = {
                'total_updates': metrics.total_updates,
                'events_detected': metrics.events_detected,
                'average_processing_time': metrics.average_processing_time,
                'data_quality_average': metrics.data_quality_average,
                'uptime_hours': metrics.uptime_hours,
                'last_update': metrics.last_update.isoformat()
            }
            
            if format.lower() == 'csv':
                return self._export_to_csv(export_data, f"performance_metrics_{timestamp}.csv")
            elif format.lower() == 'json':
                return self._export_to_json(export_data, f"performance_metrics_{timestamp}.json")
            elif format.lower() == 'excel':
                return self._export_to_excel(export_data, f"performance_metrics_{timestamp}.xlsx")
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting performance metrics: {e}")
            raise
    
    def export_event_history(self, state_manager, hours_back: int = 24, format: str = "csv") -> str:
        """
        Export event history
        
        Args:
            state_manager: StateManager instance
            hours_back: Number of hours of history to export
            format: Export format ('csv', 'json', 'excel')
            
        Returns:
            Path to exported file
        """
        try:
            events = state_manager.get_recent_events(hours_back)
            timestamp = datetime.now(MARKET_TIMEZONE).strftime("%Y%m%d_%H%M%S")
            
            # Prepare data
            export_data = []
            for event in events:
                export_data.append({
                    'timestamp': event.timestamp.isoformat(),
                    'event_type': event.event_type,
                    'description': event.description,
                    'significance_score': event.significance_score,
                    'affected_pairs': ','.join(event.affected_pairs) if event.affected_pairs else '',
                    'rand_index': event.rand_index
                })
            
            if format.lower() == 'csv':
                return self._export_events_to_csv(export_data, f"event_history_{hours_back}h_{timestamp}.csv")
            elif format.lower() == 'json':
                return self._export_to_json(export_data, f"event_history_{hours_back}h_{timestamp}.json")
            elif format.lower() == 'excel':
                return self._export_events_to_excel(export_data, f"event_history_{hours_back}h_{timestamp}.xlsx")
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting event history: {e}")
            raise
    
    def export_volatility_regimes(self, state_manager, format: str = "csv") -> str:
        """
        Export volatility regime data
        
        Args:
            state_manager: StateManager instance
            format: Export format ('csv', 'json', 'excel')
            
        Returns:
            Path to exported file
        """
        try:
            if not hasattr(state_manager, 'volatility_regimes') or not state_manager.volatility_regimes:
                raise ValueError("No volatility regime data available for export")
            
            timestamp = datetime.now(MARKET_TIMEZONE).strftime("%Y%m%d_%H%M%S")
            regime_data = state_manager.volatility_regimes
            
            # Prepare calendar data for export
            calendar_data = state_manager.get_volatility_regime_calendar_data()
            export_data = []
            
            for i, date in enumerate(calendar_data.get('dates', [])):
                if i < len(calendar_data.get('regimes', [])):
                    export_data.append({
                        'date': date,
                        'regime': calendar_data['regimes'][i],
                        'regime_name': calendar_data.get('regime_names', [f"Regime {calendar_data['regimes'][i]}"])[i] if i < len(calendar_data.get('regime_names', [])) else f"Regime {calendar_data['regimes'][i]}"
                    })
            
            if format.lower() == 'csv':
                return self._export_regimes_to_csv(export_data, f"volatility_regimes_{timestamp}.csv")
            elif format.lower() == 'json':
                return self._export_to_json(export_data, f"volatility_regimes_{timestamp}.json")
            elif format.lower() == 'excel':
                return self._export_regimes_to_excel(export_data, f"volatility_regimes_{timestamp}.xlsx")
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error exporting volatility regimes: {e}")
            raise
    
    def _export_to_csv(self, data: Dict[str, Any], filename: str) -> str:
        """Export single record data to CSV"""
        filepath = self.export_dir / filename
        
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Field', 'Value'])
            for key, value in data.items():
                writer.writerow([key, value])
        
        logger.info(f"Data exported to CSV: {filepath}")
        return str(filepath)
    
    def _export_events_to_csv(self, events: List[Dict[str, Any]], filename: str) -> str:
        """Export events list to CSV"""
        filepath = self.export_dir / filename
        
        if events:
            fieldnames = events[0].keys()
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(events)
        else:
            # Create empty file with headers
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['timestamp', 'event_type', 'description', 'significance_score', 'affected_pairs', 'rand_index'])
        
        logger.info(f"Events exported to CSV: {filepath}")
        return str(filepath)
    
    def _export_regimes_to_csv(self, regimes: List[Dict[str, Any]], filename: str) -> str:
        """Export volatility regimes to CSV"""
        filepath = self.export_dir / filename
        
        if regimes:
            fieldnames = regimes[0].keys()
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(regimes)
        else:
            # Create empty file with headers
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['date', 'regime', 'regime_name'])
        
        logger.info(f"Volatility regimes exported to CSV: {filepath}")
        return str(filepath)
    
    def _export_to_json(self, data: Any, filename: str) -> str:
        """Export data to JSON"""
        filepath = self.export_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, indent=2, ensure_ascii=False)
        
        logger.info(f"Data exported to JSON: {filepath}")
        return str(filepath)
    
    def _export_to_excel(self, data: Dict[str, Any], filename: str) -> str:
        """Export single record data to Excel"""
        filepath = self.export_dir / filename
        
        df = pd.DataFrame(list(data.items()), columns=['Field', 'Value'])
        df.to_excel(filepath, index=False, engine='openpyxl')
        
        logger.info(f"Data exported to Excel: {filepath}")
        return str(filepath)
    
    def _export_events_to_excel(self, events: List[Dict[str, Any]], filename: str) -> str:
        """Export events to Excel"""
        filepath = self.export_dir / filename
        
        df = pd.DataFrame(events)
        df.to_excel(filepath, index=False, engine='openpyxl')
        
        logger.info(f"Events exported to Excel: {filepath}")
        return str(filepath)
    
    def _export_regimes_to_excel(self, regimes: List[Dict[str, Any]], filename: str) -> str:
        """Export volatility regimes to Excel"""
        filepath = self.export_dir / filename
        
        df = pd.DataFrame(regimes)
        df.to_excel(filepath, index=False, engine='openpyxl')
        
        logger.info(f"Volatility regimes exported to Excel: {filepath}")
        return str(filepath)


def get_available_exports(state_manager) -> Dict[str, bool]:
    """
    Check which export types are available based on current data
    
    Args:
        state_manager: StateManager instance
        
    Returns:
        Dictionary indicating availability of each export type
    """
    availability = {
        'clustering_state': False,
        'performance_metrics': False,
        'event_history': False,
        'volatility_regimes': False
    }
    
    try:
        # Check clustering state
        if state_manager.get_current_state():
            availability['clustering_state'] = True
        
        # Check performance metrics
        metrics = state_manager.get_performance_metrics()
        if metrics and metrics.total_updates > 0:
            availability['performance_metrics'] = True
        
        # Check event history
        events = state_manager.get_recent_events(24)
        if events:
            availability['event_history'] = True
        
        # Check volatility regimes
        if hasattr(state_manager, 'volatility_regimes') and state_manager.volatility_regimes:
            availability['volatility_regimes'] = True
            
    except Exception as e:
        logger.error(f"Error checking export availability: {e}")
    
    return availability
