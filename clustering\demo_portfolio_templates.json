{"metadata": {"export_timestamp": "2025-07-07T21:58:22.074021", "source_project": "Dynamic FX Clustering", "target_project": "MPT Portfolio Optimization", "template_count": 5, "symbols": ["EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", "USDCAD", "NZDUSD"]}, "templates": [{"id": "conservative_1751914702", "name": "Conservative Risk-Parity Portfolio", "strategy": "conservative", "description": "Low-risk portfolio using risk parity principles with equal risk contribution from all assets", "allocation": {"weights": {"EURUSD": 0.24344210905127273, "GBPUSD": 0.08636432696006095, "USDJPY": 0.15754043868698123, "USDCHF": 0.1144509483147968, "AUDUSD": 0.10991212830319762, "USDCAD": 0.1742154426175168, "NZDUSD": 0.11407460606617392}, "weight_sum": 1.0000000000000002, "non_zero_positions": 7}, "risk_metrics": {"expected_return": 0.03, "expected_volatility": 0.01058815037153586, "sharpe_ratio": 0.5, "max_drawdown_estimate": 0.026470375928839652, "diversification_ratio": 1.2, "risk_concentration": 0.14286655448644914}, "risk_budgets": {"EURUSD": 0.14286655448644914, "GBPUSD": 0.1428539720450402, "USDJPY": 0.1428498026137363, "USDCHF": 0.14285835700047267, "AUDUSD": 0.1428525151941981, "USDCAD": 0.14285469545665774, "NZDUSD": 0.14286410320344586}, "rebalancing": {"frequency": "quarterly", "thresholds": {"volatility": 0.5, "correlation": 0.3, "drift": 0.05}, "minimum_interval_days": 60, "emergency_triggers": ["volatility_spike", "correlation_breakdown"]}, "constraints": {"cluster_constraints": null, "position_limits": {"min_weight": 0.08636432696006095, "max_weight": 0.24344210905127273}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles"], "notes": "Focus on capital preservation with steady returns. Suitable for risk-averse investors.", "estimated_turnover": 0.1}, "creation_info": {"timestamp": "2025-07-07T21:58:22.053539", "optimization_success": true}}, {"id": "balanced_1751914702", "name": "Balanced Cluster-Diversified Portfolio", "strategy": "balanced", "description": "Balanced portfolio using cluster analysis for optimal diversification across currency groups", "allocation": {"weights": {"EURUSD": 0.1111111111111111, "USDCHF": 0.1111111111111111, "NZDUSD": 0.1111111111111111, "GBPUSD": 0.16666666666666666, "AUDUSD": 0.16666666666666666, "USDJPY": 0.16666666666666666, "USDCAD": 0.16666666666666666}, "weight_sum": 1.0, "non_zero_positions": 7}, "risk_metrics": {"expected_return": 0.017481569391932367, "expected_volatility": 0.011920139301523422, "sharpe_ratio": -0.2112752665353308, "max_drawdown_estimate": 0.023840278603046845, "diversification_ratio": 1.5, "risk_concentration": 0.3}, "risk_budgets": {"EURUSD": 0.1111111111111111, "GBPUSD": 0.16666666666666666, "USDJPY": 0.16666666666666666, "USDCHF": 0.1111111111111111, "AUDUSD": 0.16666666666666666, "USDCAD": 0.16666666666666666, "NZDUSD": 0.1111111111111111}, "rebalancing": {"frequency": "monthly", "thresholds": {"volatility": 0.4, "correlation": 0.25, "drift": 0.08}, "minimum_interval_days": 30, "emergency_triggers": ["cluster_change", "volatility_regime_shift"]}, "constraints": {"cluster_constraints": {"0": 0.3333333333333333, "1": 0.3333333333333333, "2": 0.3333333333333333}, "position_limits": {"min_weight": 0.1111111111111111, "max_weight": 0.16666666666666666}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles", "cluster_assignments"], "notes": "Balances risk and return using cluster-based diversification. Suitable for moderate risk tolerance.", "estimated_turnover": 0.15}, "creation_info": {"timestamp": "2025-07-07T21:58:22.053613", "optimization_success": true}}, {"id": "aggressive_1751914702", "name": "Aggressive Maximum-<PERSON>", "strategy": "aggressive", "description": "High-return seeking portfolio optimized for maximum risk-adjusted returns", "allocation": {"weights": {"EURUSD": 0.2999999999770139, "GBPUSD": 0.034273902642765225, "USDJPY": 0.010000000001234476, "USDCHF": 0.010000000001521117, "AUDUSD": 0.045726097381034565, "USDCAD": 0.2999999999979063, "NZDUSD": 0.29999999999852445}, "weight_sum": 0.9999999999999999, "non_zero_positions": 7}, "risk_metrics": {"expected_return": 0.023321607959827458, "expected_volatility": 0.01030578079889296, "sharpe_ratio": 0.3223053182136634, "max_drawdown_estimate": 0.030917342396678878, "diversification_ratio": 1.8, "risk_concentration": 0.5}, "risk_budgets": {"EURUSD": 0.2999999999770139, "GBPUSD": 0.034273902642765225, "USDJPY": 0.010000000001234476, "USDCHF": 0.010000000001521117, "AUDUSD": 0.045726097381034565, "USDCAD": 0.2999999999979063, "NZDUSD": 0.29999999999852445}, "rebalancing": {"frequency": "monthly", "thresholds": {"volatility": 0.3, "correlation": 0.2, "drift": 0.12}, "minimum_interval_days": 15, "emergency_triggers": ["sharp_drawdown", "correlation_breakdown", "volatility_spike"]}, "constraints": {"cluster_constraints": null, "position_limits": {"min_weight": 0.010000000001234476, "max_weight": 0.29999999999852445}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles", "expected_returns"], "notes": "Targets maximum Sharpe ratio. Suitable for high risk tolerance investors seeking optimal risk-adjusted returns.", "estimated_turnover": 0.25}, "creation_info": {"timestamp": "2025-07-07T21:58:22.058165", "optimization_success": true}}, {"id": "regime_adaptive_1751914702", "name": "Regime-Adaptive Dynamic Portfolio", "strategy": "regime_adaptive", "description": "Dynamic portfolio that adapts to market regimes. Currently in high volatility regime - defensive positioning", "allocation": {"weights": {"EURUSD": 0.25, "GBPUSD": 0.0, "USDJPY": 0.25, "USDCHF": 0.0, "AUDUSD": 0.25, "USDCAD": 0.25, "NZDUSD": 0.0}, "weight_sum": 1.0, "non_zero_positions": 4}, "risk_metrics": {"expected_return": 0.017505782956258653, "expected_volatility": 0.010021594278850781, "sharpe_ratio": -0.24888425676990886, "max_drawdown_estimate": 0.02204750741347172, "diversification_ratio": 1.6, "risk_concentration": 0.35}, "risk_budgets": {"EURUSD": 0.25, "GBPUSD": 0.0, "USDJPY": 0.25, "USDCHF": 0.0, "AUDUSD": 0.25, "USDCAD": 0.25, "NZDUSD": 0.0}, "rebalancing": {"frequency": "regime_based", "thresholds": {"volatility": 0.2, "correlation": 0.15, "drift": 0.06}, "minimum_interval_days": 7, "emergency_triggers": ["volatility_regime_shift", "correlation_regime_shift", "cluster_instability"]}, "constraints": {"cluster_constraints": null, "position_limits": {"min_weight": 0.0, "max_weight": 0.25}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles", "cluster_assignments", "regime_classification"], "notes": "Adapts allocation based on volatility and correlation regimes. Requires active monitoring and frequent rebalancing.", "estimated_turnover": 0.3}, "creation_info": {"timestamp": "2025-07-07T21:58:22.058265", "optimization_success": true}}, {"id": "equal_risk_contribution_1751914702", "name": "Equal Risk Contribution (ERC) Portfolio", "strategy": "equal_risk_contribution", "description": "Portfolio where each asset contributes equally to total portfolio risk", "allocation": {"weights": {"EURUSD": 0.24344210905127273, "GBPUSD": 0.08636432696006095, "USDJPY": 0.15754043868698123, "USDCHF": 0.1144509483147968, "AUDUSD": 0.10991212830319762, "USDCAD": 0.1742154426175168, "NZDUSD": 0.11407460606617392}, "weight_sum": 1.0000000000000002, "non_zero_positions": 7}, "risk_metrics": {"expected_return": 0.04, "expected_volatility": 0.01058815037153586, "sharpe_ratio": 1.888904038779618, "max_drawdown_estimate": 0.02117630074307172, "diversification_ratio": 1.4, "risk_concentration": 0.14286655448644914}, "risk_budgets": {"EURUSD": 0.14286655448644914, "GBPUSD": 0.1428539720450402, "USDJPY": 0.1428498026137363, "USDCHF": 0.14285835700047267, "AUDUSD": 0.1428525151941981, "USDCAD": 0.14285469545665774, "NZDUSD": 0.14286410320344586}, "rebalancing": {"frequency": "quarterly", "thresholds": {"volatility": 0.4, "correlation": 0.3, "drift": 0.07}, "minimum_interval_days": 45, "emergency_triggers": ["risk_budget_breach", "volatility_spike"]}, "constraints": {"cluster_constraints": null, "position_limits": {"min_weight": 0.08636432696006095, "max_weight": 0.24344210905127273}}, "implementation": {"data_requirements": ["correlation_matrix", "volatility_profiles"], "notes": "True risk parity approach ensuring equal risk contribution from all assets. Good for diversification-focused strategies.", "estimated_turnover": 0.12}, "creation_info": {"timestamp": "2025-07-07T21:58:22.063738", "optimization_success": true}}]}