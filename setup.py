"""
Setup script for Dynamic FX Clustering Application
"""

from setuptools import setup, find_packages
import os

# Read README for long description
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Dynamic FX Clustering Application for real-time forex market analysis"

# Read requirements
def read_requirements(filename):
    req_path = os.path.join(os.path.dirname(__file__), filename)
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f 
                   if line.strip() and not line.startswith('#') and not line.startswith('-r')]
    return []

setup(
    name="dynamic-fx-clustering",
    version="1.0.0",
    author="FX Analytics Team",
    author_email="<EMAIL>",
    description="Real-time forex clustering and volatility regime analysis",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/fxanalytics/dynamic-fx-clustering",
    
    packages=find_packages(exclude=['tests*', 'docs*']),
    
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial :: Investment",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Programming Language :: Rust",
        "Operating System :: Microsoft :: Windows",
    ],
    
    python_requires=">=3.9",
    install_requires=read_requirements('requirements.txt'),
    
    extras_require={
        'dev': read_requirements('requirements-dev.txt'),
        'test': [
            'pytest>=7.4.3',
            'pytest-cov>=4.1.0',
            'pytest-benchmark>=4.0.0',
        ],
    },
    
    entry_points={
        'console_scripts': [
            'fx-clustering=run_clustering_app:main',
        ],
    },
    
    include_package_data=True,
    package_data={
        'clustering': ['*.json', '*.yaml'],
        '': ['*.md', '*.txt', '*.toml'],
    },
    
    project_urls={
        "Bug Reports": "https://github.com/fxanalytics/dynamic-fx-clustering/issues",
        "Source": "https://github.com/fxanalytics/dynamic-fx-clustering",
        "Documentation": "https://github.com/fxanalytics/dynamic-fx-clustering/docs",
    },
    
    keywords="forex clustering volatility trading finance real-time analysis",
    
    zip_safe=False,  # Required for Rust extensions
)
