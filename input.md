<img src="./5g3p0e1c.png"
style="width:0.28299in;height:0.28212in" /><img src="./peoi3pim.png"
style="width:0.9878in;height:0.16927in" />

**Dynamic** **Clustering** **for** **Real-Time** **FX** **Regime**
**Detection**

Experienced quantitative developers can enhance a CSSD/dispersion-based
FX trading system by integrating **dynamic** **clustering** to detect
and adapt to market regimes in real time. Clustering allows
identification of groups of currencies or pairs that behave similarly
(e.g. highly correlated or sharing volatility characteristics), and
tracking how these groups form or break apart over time. This section
provides a comprehensive guide to implementing minute-by-minute
clustering for FX correlations and
volatilities,includingalgorithmchoices,datapreparation,detectionofclusterchanges,andintegrationwith
CSSD signals.

**Clustering** **Algorithms** **for** **Real-Time** **Streaming**
**Data**

In a real-time FX context, clustering algorithms must handle
continuously updating data eficiently. Key options include:

> • **Online** **K-Means** **(Incremental** **K-Means):** A
> centroid-based algorithm that can be updated iteratively. Using a
> mini-batch or streaming variant of K-Means allows updating cluster
> centers with each new data batch rather than recomputing from scratch.
> For example, scikit-learn’s MiniBatchKMeans supports an online
> partial_fit method to update clusters incrementally
>
> [1](https://stackoverflow.com/questions/53091623/differences-between-minibatchkmeans-fit-and-minibatchkmeans-partial-fit#:~:text=So%20as%20well%20as%20the,as%20online%20clustering)
> . This is well-suited for fast updates, but you must predefine the
> number of clusters *K* and clusters are assumed roughly spherical in
> feature space.
>
> • **DBSCAN** **(Density-Based** **Clustering):** A density-based
> algorithm that can find clusters of arbitrary shape and identify
> outliers (noise points) without a fixed K. While standard DBSCAN is a
> static algorithm, it can be applied on a sliding window of recent data
> to reflect current structure. For true streaming use, variants like
> **DenStream** extend DBSCAN to dynamic data by maintaining
> “micro-clusters” and periodically applying DBSCAN to them
> [2](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=that%20relies%20on%20micro,construct%20and%20save%20coreset%20information)
> . DBSCAN is useful for detecting when one currency pair becomes an
> outlier or when the number of clusters changes (as it can yield a
> variable number of clusters based on an epsilon radius parameter).
>
> • **Gaussian** **Mixture** **Models** **(GMM):** A probabilistic
> clustering via mixture of Gaussians. GMM can be applied to recent data
> to estimate clusters (with each cluster having a mean vector and
> covariance). GMMs provide soft assignments (each currency’s
> probability of belonging to each cluster), which can be insightful for
> borderline cases. For streaming updates, one can either refit the GMM
> on a rolling window or use an online EM algorithm (sequentially
> updating mixture parameters as new data arrives). GMMs handle cluster
> covariance structure explicitly (useful if volatility scales differ),
> but are more computationally intensive than K-Means, and still
> typically require specifying the number of components (unless using
> more complex Bayesian variants).
>
> • **Spectral** **Clustering** **/** **Community** **Detection:** These
> methods treat the correlation matrix as a graph of relationships.
> Spectral clustering computes eigenvectors of a similarity matrix (e.g.
>
> 1
>
> correlations) to find clusters of tightly connected currencies.
> Community detection algorithms (like modularity optimization or label
> propagation) on a currency correlation network can also identify
> groups. For example, one study found that clustering currencies with a
> *distance* *correlation* metric and a community-detection algorithm
> revealed meaningful “FX communities” that reorganized during the
> 2007–2008 crisis
> [3](https://www.cs.upc.edu/~argimiro/mytalks/MIDAS16slides.pdf#:~:text=Experiments%20with%20real%20data%20show,2015)
> . Spectral methods can capture complex relationship structures and
> don’t assume cluster shapes, but need to be rerun for each update (no
> simple online version) and might be overkill if the number of
> currencies is modest. With ~20–30 currency pairs, re-computing
> spectral clustering each minute is usually feasible.
>
> • **Hierarchical** **Clustering** **(Agglomerative** **or**
> **Divisive):** A hierarchical approach builds a binary tree
> (dendrogram) of clusters based on pairwise distances (e.g. correlation
> distances). In an FX setting, hierarchical clustering on the
> correlation distance (often using distance = √(2*(1–ρ))* *for*
> *correlation* *ρ)* *can* *produce* *intuitive* *clusters* *of*
> *currencies.* *A* *dynamic* *hierarchical* *clustering*\* approach
> would rebuild
> thistreeateachtimestep(orupdateitifpossible)andthendetermineclustersbycuttingthetreeat
> a certain distance threshold. This allows the number of clusters to
> adapt over time. While standard hierarchical clustering is a batch
> algorithm, the small universe of currency pairs means you can
> recompute it frequently. There are also research algorithms like ODAC
> (Online Divisive Agglomerative Clustering) that update a hierarchy
> incrementally
> [4](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=Silva%20et%20al,algorithms%20used%20in%20our%20experiments)
> , though implementing these from scratch is complex. Hierarchical
> clustering is very interpretable (you can visualize which currencies
> cluster together and how distances grow), and by adjusting the cutoff
> threshold you can detect when formerly unified clusters split apart
> (if distances exceed the threshold).
>
> • **Other** **Streaming** **Clustering** **Methods:** In machine
> learning literature, many specialized stream clustering algorithms
> exist (e.g. BIRCH, CluStream, ClusTree, SWClustering, StreamKM++ to
> name a few
> [4](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=Silva%20et%20al,algorithms%20used%20in%20our%20experiments)
> ). These often maintain summary statistics or micro-clusters to handle
> potentially large data streams with concept drift. For an FX
> application with a limited set of instruments, such heavy frameworks
> may be unnecessary, but it’s worth noting that MiniBatchKMeans and
> Birch (both available in scikit-learn) are among those known to work
> incrementally
> [1](https://stackoverflow.com/questions/53091623/differences-between-minibatchkmeans-fit-and-minibatchkmeans-partial-fit#:~:text=So%20as%20well%20as%20the,as%20online%20clustering)
> . **BIRCH**, for instance, builds a tree of clustering features and
> can update it with new data points in an online fashion
> [5](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=BIRCH%20%28Zhang%20et%20al,on%20their%20density%20before%20clustering)
> . **StreamKM++** is an eficient k-means for streams using core-sets
> [2](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=that%20relies%20on%20micro,construct%20and%20save%20coreset%20information)
> . If your FX system were to scale to hundreds of assets or required
> millisecond updates, exploring these advanced methods could be
> justified. In most FX use-cases, however, a simpler approach (like
> repeated clustering on a rolling window or using MiniBatchKMeans)
> sufices given the small scale of instruments and 60-second update
> interval.

**Comparative** **Trade-offs:**Simpler algorithms (like K-Means) are
fast and easy to implement in real time, but require choosing K and
might oversimplify correlation structures. Density-based or hierarchical
methods adapt the cluster count automatically and can flag outliers
(useful for regime shifts), but may be somewhat slower and produce
clusters that are less stable minute-to-minute unless parameters are
tuned well. Spectral and GMM approaches capture richer structure (e.g.
overlapping clusters or clusters with different shapes), at the cost of
more computation and complexity – interpretability can also suffer,
since eigenvector
combinationsorGaussiancomponentsarenotasstraightforwardas,say,“thesethreecurrenciesaretightly
correlated”. In practice, many quant teams start with an easily
interpretable approach like hierarchical clustering on correlation or an
online K-Means, then only move to more complex algorithms if needed for
accuracy or if non-linear patterns in correlations are evident. Always
consider the latency budget of your trading system: algorithms like
MiniBatchKMeans can update in milliseconds, whereas a full spectral
decomposition or GMM EM iteration might take seconds (though with ~30
points, even these are quite fast

> 2

onmodernhardware).Foraminute-by-minuteupdate,anyoftheabovealgorithmscanbeoptimizedtorun
well within a 60s cycle, but simpler ones leave more headroom for other
tasks running in your FX dashboard.

**Data** **Representation** **and** **Feature** **Engineering** **for**
**FX** **Clustering**

Thechoiceofinputfeaturestotheclusteringalgorithmiscrucial.Weneedtorepresentthestateofmultiple
FX pairs in a way that captures correlation and volatility patterns over
recent time, and update that representation continuously. Below are
recommended approaches for preparing FX data for clustering:

> • **Sliding** **Window** **of** **Returns:** Compute returns (or log
> returns) for each currency pair on a minute-by-minute basis. Maintain
> a rolling window of the last *N* minutes (e.g. last 60 minutes) of
> returns for each pair. This window should be long enough to capture
> short-term correlation structure but short enough to adapt quickly
> (N=30, 60, or 120 are common choices, configurable via
> experimentation). At each update, drop the oldest minute and add the
> newest. This gives you a matrix of shape \[N x M\] (N time points, M
> currency pairs) for the recent window. From this window you can
> derive:
>
> • A **correlation** **matrix** of dimension MxM, showing pairwise
> correlations among the M currency pairs over the last N minutes.
>
> • **Volatility** **estimates** for each pair, e.g. the standard
> deviation of returns over that window (or other volatility metrics).
>
> • Other cross-sectional measures like the cross-sectional standard
> deviation (CSSD) itself, or principal components.

*Representation* *for* *Clustering:* One straightforward approach is to
use the correlation matrix directly: define a distance between
currencies i and j as d(i,j) = 1 – ρ(i,j) (or a related metric) and feed
the distance matrix into a clustering algorithm that accepts distance
matrices (like hierarchical clustering or spectral clustering). Another
approach is to convert the correlation matrix into feature vectors: for
example, represent each currency pair by the vector of its correlations
to all other pairs. This would give each currency a feature vector of
length M (which is the i-th row of the correlation matrix). Clustering
these vectors (with a standard Euclidean-distance-based algorithm) will
tend to group currencies that have similar correlation profiles with
others. Be mindful that correlation vectors are highly inter-dependent
(each pair’s vector shares elements with others), so some dimensionality
reduction is often used: you could apply PCA to the correlation matrix
to extract the top few eigenvectors (which capture the main co-movement
patterns) and use those as features for clustering. In essence, this is
clustering in the space of principal components (e.g. clustering on the
first 2–3 PCs of returns). If the first principal component dominates
(i.e.,
onelargeclusterofallassetsmovingtogether),that’soneregime;ifthefirstcomponent’svariancefallsand
second/third components are significant, it indicates a more fragmented
market with multiple clusters.

> • **Normalized** **Volatility** **Vectors:** In addition to
> correlation, you may want to cluster by volatility regimes. Each
> currency pair can be characterized by its recent volatility level and
> perhaps how that
> volatilitycomparestoothers.Forexample,youcancreateafeaturevectorforeachcurrencypairlike
> \[vol_5min, vol_60min, vol_1day\] – short-term and longer-term
> realized volatilities (standard deviations of returns) or ranges.
> Normalize these features (so that, say, each feature is scaled by its
> mean or z-score across currencies) to ensure one scale doesn’t
> dominate. Clustering these volatility vectors will group currencies
> with similar volatility dynamics (e.g. all high-volatility pairs in
> one cluster, low-volatility pairs in another). This is useful to
> detect volatility clustering across currencies – for instance, during
> a market turmoil, many risky currencies might simultaneously move to a
> high-
>
> 3
>
> vol regime and cluster together. A simple version is to cluster on a
> single volatility metric (like 60-min realized vol), which essentially
> ranks currencies by risk. Indeed, a tutorial by Rezaei (2019)
> demonstrated clustering major FX pairs by their historical
> volatilities using K-Means, and identified groupings of pairs with
> “similar characteristics” in terms of riskiness
> [6](https://medium.com/@bbkrze/volatility-clustering-in-forex-market-93db8e1e28d3#:~:text=To%20summarize%2C%20in%20this%20article%2C,determine%20the%20optimum%20cluster%20numbers)
> [7](https://medium.com/@bbkrze/volatility-clustering-in-forex-market-93db8e1e28d3#:~:text=Dividing%20currency%20pairs%20into%20groups,with%20enough%20diversification%20between%20them)
> . In that static example, silhouette analysis was used to pick an
> optimal number of clusters, and the result helped ensure a diversified
> set of currency pairs by grouping those with similar volatility
> [8](https://medium.com/@bbkrze/volatility-clustering-in-forex-market-93db8e1e28d3#:~:text=To%20summarize%2C%20in%20this%20article%2C,determine%20the%20optimum%20cluster%20numbers)
> . In our real-time context, you would recompute such volatility
> features every minute and observe if any currency migrates from one
> volatility cluster to another (a sign of a volatility regime change
> for that currency or group).
>
> • **Combined** **Features** **(Correlation** **+** **Volatility):**
> You may also experiment with feature sets that include both
> correlation-based features and volatility features. For instance, one
> could create an *M*-dimensional correlation-based vector for each
> currency (as described), and append a volatility metric to it.
> However, be cautious in combining them directly, since distance-based
> clustering will then weigh correlation differences versus volatility
> differences in some ratio. If doing so, it’s often useful to normalize
> each feature type (e.g. each currency’s correlation vector could be
> scaled to unit norm, and volatility could be scaled to a comparable
> range or given a weighting factor). An alternative is to perform
> clustering in two stages – e.g. first identify clusters based on
> correlation structure, then within each correlation cluster,
> sub-cluster by volatility level (or vice versa). This may yield
> insight such as “Cluster A: highly correlated European currencies,
> which further split into low-vol vs high-vol subgroups.”
>
> • **Exponential** **Weighting** **and** **Recency:** Whether for
> correlation or volatility, consider using *exponentially* *weighted*
> *moving* *averages* instead of simple rolling windows. An EWMA puts
> more weight on recent observations and gradually down-weights older
> data, which can react faster to sudden changes while still retaining
> some memory. For example, you can maintain an EWMA covariance matrix
> for all currency pairs updated each minute: \$\$COV_t = \lambda \\
> COV\_{t-1} + (1-\lambda)\\ r_t r_t^\top,\$\$ where \$r_t\$ is the
> vector of returns at time *t* (for all pairs), and \$\lambda\$
> (e.g.0.97fora~30-minutehalf-life,tunedasneeded)isthedecayfactor.Fromthiscovariance,derive
> the correlation matrix at time *t*. Many trading systems use such
> RiskMetrics-style updates for volatilities and correlations. In
> Python, pandas offers an ewm (exponential weighted) functionality that
> can be applied to compute moving averages or covariances. Using EWMs
> can make your clustering input more stable from minute to minute (less
> jumpy than a hard rolling window cutoff) while still adapting to
> regime changes quickly when a shock occurs.
>
> • **Data** **Normalization:** Ensure that features are scaled
> appropriately for clustering. If you cluster on raw returns or
> combined features, differences in scale can skew results. Usually,
> working with correlation (which is already bounded \[-1,1\]) and
> normalized volatilities (e.g. z-scores or percentile ranks) is
> effective. If using raw returns as features (for shape-based
> clustering of time series), you would standardize them (each series
> scaled to unit variance) so that differences reflect correlation
> structure rather than absolute volatility.

**Feature** **Preparation** **Example:** Suppose we have 10 currency
pairs. Each minute, we compute the 60-minute correlation matrix among
their returns. We convert that into a 10x10 distance matrix \$D\$ where
\$D\_{ij} = 1 - \rho\_{ij}\$. We also compute each pair’s 60-minute
realized volatility. We could proceed in a few ways: 1. Use \$D\$
directly with a hierarchical clustering to group the 10 pairs by
correlation afinity. 2. Perform spectral clustering on the correlation
matrix (treating it as a graph adjacency). 3. Create a 10x10 correlation
feature matrix (each row i is correlations of currency *i* to others)
and append the volatility as an

> 4

extra column, yielding a 10x11 feature matrix. Then apply
MiniBatchKMeans or GMM on that, requesting perhaps 2–4 clusters. 4.
Alternatively, cluster correlation and volatility separately. For
instance, run DBSCAN on the correlation distance matrix to find
correlation clusters, and *simultaneously* categorize currencies into
“high-vol” vs “low-vol” buckets; then examine if any high-vol currencies
are clustering with low-vol ones, which might indicate unusual behavior.

The appropriate representation may depend on which aspect (correlation
or volatility) you want to emphasize as a regime indicator. It’s common
to maintain multiple clustering views – e.g. one purely
correlation-based cluster assignment and one purely volatility-based
assignment – and use them in different parts of your strategy
(correlation clusters for co-movement regimes, volatility clusters for
risk regimes).

**Detecting** **Cluster** **Persistence** **and** **Breakage**

Once clustering is in place, the next challenge is **monitoring**
**how** **clusters** **evolve** over time, and detecting significant
changes that could signal a regime shift. Here are methods to quantify
cluster persistence or detect breakage:

> • **Track** **Cluster** **Membership** **Over** **Time:** The simplest
> indicator is to watch if currencies change their cluster labels from
> one interval to the next. In a stable regime, you’d expect the core
> membership of clusters to remain mostly the same (e.g. the set of
> currencies in a “risk-on” cluster stays put). If several currencies
> suddenly swap clusters or one cluster splits into two, that’s a clear
> sign of change. You can formalize this by measuring **similarity**
> **between** **consecutive** **cluster** **partitions**. One measure is
> the *Adjusted* *Rand* *Index* *(ARI)* or *Normalized* *Mutual*
> *Information* *(NMI)* between the set of clusters at time *t* and time
> *t+1*. An ARI of 1 means identical clustering, whereas a low ARI (near
> 0) means the grouping changed substantially. These metrics give a
> numeric gauge of stability. For example, you might compute ARI each
> minute; a sharp drop in ARI indicates a cluster reorganization beyond
> normal noise. (Note: ARI is available via
> sklearn.metrics.adjusted_rand_score for comparing two label arrays.)
>
> • **Silhouette** **Score** **and** **Cluster** **Compactness:** The
> **silhouette** **coeficient** is a classic internal validation metric
> that measures how well-separated the clusters are. It ranges from -1
> to 1, where a high average silhouette means clusters are tight and
> well-separated, and a low (or negative) value means clusters are
> muddled or overlapping. Track the **average** **silhouette** **score**
> of the clustering as a time series. A drift downward in silhouette
> over time might indicate that the clusters are becoming less distinct
> – perhaps previously separate clusters are starting to merge together
> or a clear cluster structure is dissolving. Conversely, a jump in
> silhouette could mean the market is segmenting more distinctly into
> clusters. Because cluster validation in a streaming context is an
> evolving field, be
> carefulwithreal-timesilhouetteinterpretation(smalloscillationsmightjustbenoise).Nonetheless,a
> persistent trend or a large one-time drop can be a valuable warning.
> Silhouette (as well as Davies– Bouldin, Calinski–Harabasz, etc.) can
> be computed quickly for each new clustering result
> [9](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=match%20at%20L274%20Many%20methods,compare%2011%20popular%20CVIs)
> , and these indices are widely used to evaluate clustering quality.
>
> • **Entropy** **of** **Cluster** **Assignments:**Another perspective
> is to compute an entropy measure for cluster membership distribution.
> For instance, if you have \$K\$ clusters at time *t*, you can form a
> probability distribution \$p = (n_1/N, n_2/N, ..., n_K/N)\$ where
> \$n_k\$ is the number of currency pairs in cluster *k* out of N total.
> The entropy \$H(p) = -\sum_k p_k \log p_k\$ is maximized when clusters
> are equal-sized
>
> 5
>
> and minimized when one cluster dominates. Changes in this entropy over
> time could reveal structural shifts: e.g. if the entropy drops
> significantly, it might mean one cluster has grown dominant (others
> shrank or merged), whereas a jump in entropy could mean the market is
> fragmenting into more even-sized clusters. This measure is somewhat
> coarse, as it doesn’t account for *which* currencies moved, just the
> size balance, but it’s easy to track. You might combine it with other
> measures (like ARI) to differentiate between, say, “one cluster
> absorbing others” vs “currencies shufling between clusters”.
>
> • **Cluster** **Count** **Jumps:** Keep an eye on the number of
> clusters produced by algorithms that determine it automatically
> (DBSCAN, hierarchical with distance threshold, GMM with adaptive
> components, etc). A sudden increase in cluster count (e.g. from 1
> cluster to 3 clusters within a few minutes) is a strong signal of
> regime change – the market moved from a cohesive state to a dispersed
> state. For example, in calm periods you might see essentially 1 big
> cluster (all currencies moderately correlated), whereas in stress
> periods you might see 2–3 distinct clusters (e.g. one cluster of
> safe-haven currencies, one of risk currencies, and maybe an isolated
> mover). Conversely, a drop in cluster count (e.g. going from 3
> clusters to 1) might signal a phase where “all correlations go to 1” –
> often in a crisis or major event, previously uncorrelated assets start
> moving together. These transitions are exactly what a dispersion-based
> strategy wants to detect. By using a method like DBSCAN or
> hierarchical clustering, you can let the data decide how many clusters
> fit at each time and simply monitor that. Even with K-Means (fixed K),
> you could run K-Means for a range of K values and calculate a goodness
> metric (elbow or silhouette) to estimate an “optimal” K at each time –
> if the optimal K shifts, that’s informative (though doing this
> continuously is computationally heavier). In literature, clustering of
> financial time series has been used to detect market regime shifts;
> for example, Arratia & Renedo (2016) observed FX **community**
> **reorganization** during crises when applying clustering with more
> sophisticated distance metrics
> [3](https://www.cs.upc.edu/~argimiro/mytalks/MIDAS16slides.pdf#:~:text=Experiments%20with%20real%20data%20show,2015)
> – effectively the cluster structure (and likely the optimal cluster
> count) changed in response to the 2007–2008 credit crisis
> [3](https://www.cs.upc.edu/~argimiro/mytalks/MIDAS16slides.pdf#:~:text=Experiments%20with%20real%20data%20show,2015)
> .
>
> • **Intra-Cluster** **vs.** **Inter-Cluster** **Distance:** If you
> prefer not to rely on a particular clustering algorithm’s output, you
> can define metrics based on the raw correlation matrix. For instance,
> compute the average correlation among currencies that are currently in
> the same cluster (intra-cluster correlation) vs. the average
> correlation of all currencies (or of different clusters). If a cluster
> is truly breaking apart, its internal average correlation will drop
> sharply. You could identify a set of currencies that historically
> moved together (e.g. by a longer-term clustering) and then monitor the
> pairwise correlations within that set in real time. A significant drop
> might trigger a signal that “Cluster X is disintegrating.” Similarly,
> correlation between previously separate clusters might increase if
> clusters are merging. These continuous metrics can complement the
> discrete clustering output.
>
> • **Persistence** **vs.** **Flexibility:** It’s important to filter
> out small, temporary oscillations in cluster assignments (which can
> occur due to noise). You can introduce hysteresis or smoothing in your
> regime detection logic. For example, require that a cluster change
> persists for several minutes or that a metric exceeds a threshold by a
> comfortable margin before declaring a regime shift. One approach is to
> use an exponentially weighted moving average on the **cluster**
> **quality** **metrics**
> themselves.Forexample,trackanEWMAofthesilhouettescoreorclustercounttoavoidwhipsaws–
> you’d detect a change when the EWMA of silhouette drops below some
> benchmark, rather than on a single-minute drop that might revert.
> Another approach is to accumulate evidence: e.g., if ARI has
>
> 6
>
> been consistently low (\<0.5) for the last 5 minutes, it’s safer to
> say the cluster structure truly changed, rather than reacting to one
> outlier minute.

In summary, you have multiple lenses to view cluster stability. A
practical system might log several of these indicators (cluster count,
silhouette, ARI between today’s and yesterday’s clusters, etc.) and then
use a
heuristicorsimplerulestodecidewhen“regimechange”isflagged.Forinstance:
*“Ifclustercountchangesor* *silhouette* *falls* *by* *more* *than* *X,*
*and* *this* *persists* *for* *Y* *minutes,* *then* *mark* *the*
*correlation* *regime* *as* *shifted.”* Fine-tune X and Y based on
backtests around known events (e.g. central bank decisions, flash
crashes) to calibrate sensitivity.

**Practical** **Implementation** **Tips** **for** **Minute-by-Minute**
**Updates**

Implementing dynamic clustering in a live FX dashboard requires careful
handling of streaming data and computational eficiency. Here are some
actionable tips for building this in Python (common in quant research
environments):

> • **Rolling** **Window** **Updates:** Use data structures that make it
> eficient to maintain a rolling window of the last N minutes. For
> example, if using pandas.DataFrame for price or return data, you can
> use df.tail(N) to get the latest window or maintain an in-memory deque
> for each time series. Pandas also offers built-in rolling
> computations: df.rolling(window=N).corr() can compute a rolling
> correlation (though it returns a matrix for each timestamp; you may
> prefer manual computation for just the latest window to save
> overhead). If data volume is small (dozens of series), computing
> correlation from scratch each minute is fine. If you need to optimize,
> note that a correlation matrix can be updated incrementally: when a
> new observation comes in and the oldest drops out, you can update sums
> and sums-of-squares to recompute variances and covariances without
> recalculating everything. However, given typical FX scenarios (N maybe
> 60, M ~ 20 pairs), a
>
> brute-force recompute (O(M^2 \* N)) each minute is likely under a few
> milliseconds.
>
> • **Use** **Vectorized** **Libraries:** Leverage NumPy/Pandas for
> heavy lifting. For instance, to compute an EWMA covariance, you might
> use pandas.DataFrame.ewm(span=..., adjust=False).cov() on your returns
> DataFrame, which at each call gives the up-to-date covariance matrix.
> This can be done in a streaming fashion by updating the DataFrame with
> the latest row and dropping the oldest if using a fixed window.
> Scikit-learn’s clustering algorithms (KMeans, DBSCAN,
> AgglomerativeClustering, etc.) are implemented in optimized C and can
> handle these small matrices very quickly. For hierarchical clustering,
> SciPy’s linkage function can perform clustering from a distance
> matrix, and you could use fcluster to extract cluster labels given a
> distance threshold.
>
> • **Partial** **Fits** **and** **Warm** **Starts:** If you use
> algorithms like MiniBatchKMeans or Birch , take advantage of their
> incremental learning. For example, you might initialize
> MiniBatchKMeans on the first window of data, then each minute call
> partial_fit(new_feature_batch) to refine the centroids
> [1](https://stackoverflow.com/questions/53091623/differences-between-minibatchkmeans-fit-and-minibatchkmeans-partial-fit#:~:text=So%20as%20well%20as%20the,as%20online%20clustering)
> . In our scenario, since the “points” (currency pairs) are the same
> but their feature values change, one trick is to treat each minute’s
> set of points as a new batch. This isn’t a textbook use of partial_fit
> (which assumes new independent samples), but in practice feeding the
> current state as a batch can nudge the centroids to follow the data’s
> movement. Monitor how much drift this causes versus a full
> re-clustering. Alternatively, if using KMeans via the standard fit
> each time, you can speed it up by using the previous clustering result
> as a warm start. Scikit-learn’s KMeans
>
> 7
>
> accepts initial centroids (e.g. pass init=previous_centroids,
> n_init=1, max_iter=20 for a quick convergence). This way, when market
> conditions are steady, the KMeans will converge almost
> immediately(sincelastsolutionisstillgood),andwhenconditionschanged,itwilladjustaccordingly.
> This approach preserves cluster identity to some extent across time
> (centroid 1 at time t is “close” to centroid 1 at time t-1, etc.),
> which can be useful for continuity.
>
> • **Parameter** **Tuning:** Set sensible parameters for your
> clustering algorithm upfront, and possibly
> adaptthemwiththeregime.Forexample,ifusingDBSCAN,theepsilon(neighborhoodradius)might
> be chosen based on expected correlation levels. You might set epsilon
> to something like 0.2–0.3 in correlation distance (meaning currencies
> must have \>70–80% correlation to be in the same cluster). If overall
> correlation levels rise or fall (e.g. in extreme risk-off, many pairs
> might have \>0.8
> correlation),afixedepsiloncouldresultinonegiantcluster;youmightthenreduceepsilonorswitch
> to a different method to still differentiate subclusters. In practice,
> you can use your CSSD dispersion as a guide: when dispersion is
> extremely low (herding behavior), you may not need fine-grained
> clusters (one cluster may sufice). When dispersion picks up, you could
> tighten the clustering threshold to capture emerging splits. Some
> practitioners even calibrate clustering thresholds to match certain
> dispersion levels (e.g. choose the number of clusters such that
> average intra-cluster dispersion falls below a threshold).
>
> • **Performance** **and** **Frequency:** Update clustering as
> frequently as your strategy requires. The user asks for
> minute-by-minute, which is typically suficient for detecting regime
> shifts (those tend to play out over tens of minutes or hours). If your
> system handles high-frequency data, you might consider sub-minute
> updates, but be cautious of noise at very high frequency; regimes
> don’t usually change every few seconds. A one-minute granularity is a
> good trade-off for FX, which balances timely detection with noise
> smoothing. Ensure that the clustering computation (including feature
> calc) comfortably fits within the 1-minute cycle. If you find some
> clustering method too slow, consider simplifying features (e.g. use
> fewer pairs or lower resolution) or upgrading hardware/parallelizing.
> For example, if using spectral clustering, you could potentially
> parallelize the eigen-decomposition (though again, with 20-30 assets
> it’s trivial). Python’s GIL can be a bottleneck; if heavy computation
> is needed, ofload to NumPy (which releases GIL in linear algebra ops)
> or use joblib to parallelize independent tasks (like computing
> metrics). Most likely, though, the bottleneck will not be the
> clustering itself but maybe data retrieval or other parts of the
> system.
>
> • **Python** **Tooling** **Suggestions:**
>
> • Use **pandas** for rolling window management and statistical
> calculations (rolling mean, std, corr).
>
> • Use **NumPy** for vectorized math and possibly linear algebra (e.g.
> numpy.cov with fweights or aweights for EWMA covariance).
>
> • For clustering, **scikit-learn** is your go-to: MiniBatchKMeans ,
> KMeans , DBSCAN , AffinityPropagation (another clustering method that
> doesn’t require K, though can be slow), AgglomerativeClustering for
> hierarchical (you can set a distance threshold via distance_threshold
> parameter to have it find appropriate cluster count). For Gaussian
>
> mixtures, sklearn.mixture.GaussianMixture can fit a GMM; you might use
> warm_start=True to initialize with previous parameters for speed on
> each iteration.
>
> • If you want to experiment with specialized stream clustering
> algorithms, consider the **River** library (formerly creme ) which
> implements some online machine learning algorithms – as of last check,
> its clustering options were limited, but it’s worth seeing if newer
> versions support it. Alternatively,
>
> 8
>
> research prototypes might exist, but scikit-learn’s partial_fit on
> MiniBatchKMeans and Birch are proven options
> [1](https://stackoverflow.com/questions/53091623/differences-between-minibatchkmeans-fit-and-minibatchkmeans-partial-fit#:~:text=So%20as%20well%20as%20the,as%20online%20clustering)
> .
>
> • **Visualization**: To integrate into a dashboard, visualize the
> clustering results and regime metrics. For example, you can plot a
> heatmap of the correlation matrix with rows/columns ordered by cluster
> (showing cluster blocks clearly). Plot time series of the metrics like
> silhouette score or number of clusters over the day. Also consider a
> network graph visualization: nodes as currencies, draw edges with
> thickness proportional to correlation, and color-code nodes by cluster
> – updating this graph in real time can give an intuitive view of how
> the market network is evolving (e.g. previously all nodes tightly
> connected, now split into two subnetworks). Python libraries like
> networkx with Plotly or Bokeh can be used for interactive network
> charts. For simpler dashboards, just listing cluster membership is
> useful (e.g. “Cluster 1: EUR, GBP, AUD; Cluster 2: USD, JPY, CHF” etc)
> and updating it when it changes. This can be accompanied by an
> indicator icon if a big change occurred recently.
>
> • **Robustness** **in** **Code:** When deploying such a system,
> include sanity checks. If data is missing or a calculation fails (e.g.
> singular covariance matrix if window is too short), handle gracefully
> – maybe by carrying forward the last known cluster state. Also be
> mindful of lookahead bias: ensure that at time *t* you only use
> information up to *t*, especially if integrating with the CSSD signal
> logic. A properly set rolling window or EWMA inherently does this.

A pseudo-code for the streaming clustering loop might look like:

> \# Initialization
>
> window = 60 \# 60-minute window
>
> features_history = \[\] \# to store cluster metrics if needed
> prev_centers = None
>
> for each minute (t): prices.update(new_tick_data)
>
> returns = compute_returns(prices) \# latest returns for each pair
> recent_returns = returns.tail(window)
>
> corr_matrix = recent_returns.corr() \# DataFrame of correlations
> vol_series = recent_returns.std() \# Series of volatilities per pair
>
> \# Prepare features for clustering (example: correlation vector + vol)
> corr_vectors = np.array(corr_matrix) \# as 2D array
> np.fill_diagonal(corr_vectors, 1.0) \# self-corr = 1 for consistency
>
> (though will be ignored)
>
> features = corr_vectors \# here each row i is correlations of pair i
> with others
>
> \# Append volatility as another column
>
> features = np.hstack(\[features, vol_series.values.reshape(-1,1)\]) \#
> Optionally normalize features here...
>
> \# Cluster (example using KMeans with warm start) if prev_centers is
> not None:
>
> kmeans = KMeans(n_clusters=K, init=prev_centers, n_init=1,
> max_iter=20)
>
> 9
>
> else:
>
> kmeans = KMeans(n_clusters=K, n_init=5) \# first time, do a proper
> init labels = kmeans.fit_predict(features)
>
> prev_centers = kmeans.cluster_centers\_
>
> \# Evaluate clustering
>
> silhouette = silhouette_score(features, labels) n_clusters =
> len(np.unique(labels))
>
> ARI = adjusted_rand_score(prev_labels, labels) if 'prev_labels' in
> locals() else 1.0
>
> prev_labels = labels
>
> \# Store or output cluster info
>
> dashboard.update_clusters(labels, silhouette, n_clusters, ARI,
> features, pairs_list)

This pseudo-code illustrates maintaining a rolling window, computing
features, clustering with possible warm start, and calculating metrics.
In practice, you’d integrate this into your data feed and event loop.

**Using** **Clustering** **to** **Enhance** **CSSD/Dispersion**
**Signals**

Finally, the dynamic clustering should be used in concert with your
existing CSSD-based signals to improve regime detection and trading
decisions:

> • **Clustering** **as** **a** **Signal** **Filter:** Cross-sectional
> standard deviation (CSSD) of returns is a measure of dispersion – how
> far individual currency returns deviate from the mean. High CSSD
> indicates a wide dispersion (potentially a “regime change” if normally
> they move together), while low CSSD indicates herd behavior (all
> moving closely together). However, CSSD alone doesn’t tell you *why*
> dispersion is high – is it because one currency is an outlier, or the
> market split into two camps? Here clustering provides context. You can
> set up rules such as: *Only* *act* *on* *a* *high* *CSSD* *signal*
> *if* *it’s* *accompanied* *by* *a* *cluster* *count* *jump* *or* *a*
> *low* *ARI* *(meaning* *the* *market* *truly* *bifurcated,* *not*
> *just* *one* *rogue* *currency).* For example, imagine CSSD spikes
> because one currency pair had a huge move while others stayed quiet. A
> clustering analysis might show still one dominant cluster (everyone
> except that one pair). If your strategy is, say, a mean-reversion
> dispersion trade, you might treat that scenario differently (one
> outlier might revert differently) than a scenario where CSSD spikes
> and clustering shows two balanced clusters (suggesting a broader
> risk-off vs risk-on split). In the latter case, it might indicate a
> more fundamental regime shift, and you may choose to reduce exposure
> or switch strategies entirely (e.g. if the market has clearly entered
> a “risk-off” regime, you might close high-risk positions).
>
> • **Clustering** **as** **a** **Signal** **Generator:** The clustering
> itself can produce trading signals. One obvious case is **cluster**
> **breakups**: if a group of currencies that were tightly correlated (a
> cluster) starts to fragment, that could presage profit opportunities
> or risks. For instance, suppose AUD, NZD, CAD have been in a
> high-correlation cluster (all commodity currencies moving together).
> If suddenly NZD diverges (cluster splits: AUD/CAD still together, NZD
> on its own or with a different cluster), that’s a significant event.
> It could mean an NZD-specific driver (like an RBNZ surprise) – a
> trader might
>
> 10
>
> investigate NZD trades specifically. Or it could be the start of a
> larger regime change where commodity currencies are no longer in sync.
> Conversely, **cluster** **formation** can be a signal: e.g. previously
> uncorrelated currencies suddenly cluster together, indicating a new
> common factor driving them (perhaps a broad macro event). Your system
> could generate alerts like “Emerging cluster: EUR, GBP, CHF
> correlation surging – European currencies moving in lockstep,” which
> might correlate with news (e.g. pan-European sentiment change).
>
> • **Regime** **Classification:** You can use clustering to **label**
> **regimes** in a more granular way than just dispersion magnitude. For
> example, define a few canonical regimes based on clustering patterns:
>
> • Regime A: One big cluster (high correlation across all FX majors – a
> unified market, often risk-on periods or times of low volatility).
>
> • Regime B: Two clusters (e.g. USD and maybe JPY in one, vs all others
> in another – could indicate a strong risk-off with USD/JPY acting as
> safe havens).
>
> • Regime C: Multiple small clusters or no clear clusters
> (idiosyncratic moves dominate, dispersion is high).
>
> • Regime D: One cluster plus outliers (e.g. mostly cohesive but one
> currency is dramatically decoupled).

These are just examples – the actual patterns will emerge from data. The
point is you can map the clustering output to a qualitative description
of market state, which your dashboard can display. A CSSD-based heatmap
might tell you “dispersion is in the 90th percentile,” while clustering
can tell you *where* that
dispersionismanifest(twoblocs,oneoutlier,etc.).Thisenhancesdecision-making:adispersiontrademight
be more reliable in one type of regime but not in another. If your
strategy historically performs poorly when there are multiple
independent clusters (perhaps because no single factor is driving
moves), you could dial down risk when you detect that scenario.

> • **Volatility** **Regime** **Adapting:** Clustering by volatility can
> guide position sizing or risk management in the CSSD strategy. For
> instance, if clustering indicates a subset of currencies is in a
> high-volatility cluster, you might reduce position sizes for those or
> require stronger confirmation for signals involving them.
> Additionally, volatility clustering might foreshadow correlation
> changes: often, rising volatility coincides with changing
> correlations. If you see a volatility cluster forming (e.g. suddenly 5
> currencies show elevated vol together), anticipate that correlation
> relationships may be in flux and give extra weight to the cluster
> analysis for correlation.
>
> • **Concrete** **Example:** Imagine your CSSD indicator flashes a
> high-dispersion alert at 14:00. Without clustering, you’d note that
> dispersion is high compared to some threshold. Now add clustering
> insight: say at 13:50 you had one cluster (all currencies correlated),
> and by 14:00 the clustering algorithm finds 3 clusters. Perhaps
> cluster 1: {EUR, GBP, CHF}, cluster 2: {AUD, NZD, CAD}, cluster 3:
> {USD, JPY}. This paints a clear picture – the European currencies
> moved one way, the commodity currencies another, and the traditional
> safe havens (USD, JPY) detached in their own way. This could be a
> classic risk-off pattern (USD and JPY strengthening, commodities
> weakening, EUR/CHF doing something in between). Your trading response
> might be to pivot strategies: maybe focus on long USD trades, or hedge
> exposures across these clusters to reduce directional risk. On the
> other hand, if CSSD was high but clustering still showed 1 cluster
> plus one outlier currency, you’d interpret it as an idiosyncratic
> event – maybe fade the outlier if mean-reversion is expected, or
> ignore the dispersion as a regime signal since the broader market
> regime is intact.
>
> 11
>
> • **Herding** **and** **Validation:** During periods of extreme
> herding (low dispersion), clustering provides a sanity check. For
> example, CSSD might be extremely low, suggesting all currencies moving
> together. Clustering should reflect one big cluster. If you instead
> see some minor clusters despite low CSSD, it could mean your
> dispersion metric isn’t capturing something – perhaps two groups with
> small relative differences. This feedback might lead you to refine the
> CSSD calculation (maybe use CSAD – cross-sectional absolute deviation
> – or include more pairs). In other words, clustering and CSSD can
> validate each other. Researchers often use dispersion (like
> cross-sectional std or MAD) to infer herding, but explicitly
> clustering gives a richer picture of the “herd’s composition”
> [6](https://medium.com/@bbkrze/volatility-clustering-in-forex-market-93db8e1e28d3#:~:text=To%20summarize%2C%20in%20this%20article%2C,determine%20the%20optimum%20cluster%20numbers)
> .
>
> • **Integration** **into** **Dashboard** **and** **Strategy**
> **Logic:** On your FX dashboard, you might have a panel showing the
> current cluster assignments and a log of significant changes (with
> timestamps). For trading automation, define rules that incorporate
> cluster info. Some examples:
>
> • “If CSSD \> X and number_of_clusters \>= 2, then trigger ‘dispersion
> regime’ mode (e.g. allocate to relative value trades).”
>
> • “If previously multiple clusters and now 1 cluster (correlations
> converging), consider exiting dispersion trades as convergence plays
> out.”
>
> • “If any cluster’s internal correlation drops below 0.y (meaning that
> cluster is breaking internally), flag it for human review or reduce
> positions in those currencies until things stabilize.”
>
> • “Use cluster membership as part of risk calculations: e.g. if my
> portfolio has many positions within the same cluster, my risk is less
> diversified than it looks; the dashboard can highlight that using the
> clustering results.”

In summary, clustering adds **contextual** **awareness** to a CSSD-based
system. Dispersion indicators tell you magnitude; clustering tells you
structure. Together, they form a more robust regime detection mechanism.
Notably, academic studies have argued that looking at how assets cluster
can give a “realistic picture” of market reorganization during crises
[3](https://www.cs.upc.edu/~argimiro/mytalks/MIDAS16slides.pdf#:~:text=Experiments%20with%20real%20data%20show,2015)
, reinforcing the idea that beyond a single metric, the pattern of
correlations matters. By implementing dynamic clustering, the system can
differentiate between a benign increase in dispersion and a structural
break in market behavior, leading to more nuanced and effective trading
responses.

**Trade-offs** **and** **Conclusion**

Building a real-time clustering module involves balancing
**computational** **cost,** **algorithm** **complexity,** **and**
**interpretability**:

> • **Computational** **Cost:** As discussed, the universe of major FX
> pairs is relatively small (28 major crosses at most), so most
> clustering methods are feasible in real time. Simpler algorithms like
> online K-Means or repeated hierarchical clustering on a distance
> matrix will run almost instantly at this scale. More complex methods
> (spectral clustering, GMMs with many iterations) might take longer but
> should still be under a second. The main cost consideration is if you
> decide to incorporate a very large number of instruments or a very
> high frequency of updates. In those cases, streaming algorithms
> designed for big data (BIRCH, CluStream, etc.
> [4](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=Silva%20et%20al,algorithms%20used%20in%20our%20experiments)
> ) or dimensionality reduction techniques to compress features might be
> required. For a minute-by-minute G10 FX regime detector, a
> straightforward approach is typically fine and easier to maintain.
>
> 12
>
> •
> **AlgorithmicComplexityvs.Robustness:**Atrade-offexistsbetweenusingasophisticatedalgorithm
> that might capture nuances (but is harder to tune and explain) and a
> simpler one that captures the main effects. For example, spectral
> clustering might better identify a subtle community structure in the
> correlation network (especially if relationships are not simply
> “spherical” clusters in feature space). But spectral outputs (e.g.
> eigenvector values) are harder to interpret and debug compared to
> saying “these currencies all have \>0.8 correlation, hence cluster.”
> K-Means is easy to understand (centroid and members) but might miss
> non-linear structures (e.g. one cluster surrounding another).
> Hierarchical clustering provides a full picture of similarities (the
> dendrogram) which is rich in information, but you need to decide how
> to cut it for cluster assignments, and that threshold could be
> arbitrary or need dynamic adjustment. In practice, many quants use
> hierarchical clustering on correlation for portfolio construction/risk
> (e.g. the “Herd behavior” or “risk parity” approaches), precisely
> because it’s interpretable – you can **see** which assets group and at
> what distance
> [10](https://www.cs.upc.edu/~argimiro/mytalks/MIDAS16slides.pdf#:~:text=Experiments%20with%20real%20data%20show,2015)
> . For regime detection, interpretability is valuable: you want to be
> able to explain *why* your system says the regime changed (“the
> correlation between these clusters dropped from 0.5 to near zero,
> etc.”). So lean towards methods that give clear signals (correlation
> up or down, cluster count change) unless you have evidence that a
> fancier method materially outperforms.
>
> • **Adaptability** **vs.** **Stability:** A highly adaptive system
> (fast-decay EWMA, very sensitive clustering parameters) will catch
> regime changes quickly, but could also false-alarm or oscillate if the
> market is choppy. A more stable system (longer window, conservative
> thresholds) might miss the very start of a regime shift but avoid
> whipsaws. This is a classic precision-recall trade-off. You should
> tune this based on the cost of false alarms vs missed detections in
> your strategy. If your strategy can safely tolerate being a few
> minutes late to confirm a regime (but false alarms cause unnecessary
> trades or exits), then err on the side of stability. Conversely, if
> missing a regime change costs a lot (e.g. a
> volatilitybreakoutthatyouwanttohedgeimmediately),youmightacceptacoupleoffalsepositives.
> Dynamic clustering gives you multiple knobs (window length, decay
> factor, clustering epsilon or K,
> thresholdsformetrics)–thesecanbeoptimizedbybacktestingonhistoricaldatawithknownregime
> events.
>
> • **Testing** **and** **Verification:** It’s advisable to test the
> dynamic clustering module ofline on historical data. Feed it
> minute-by-minute past data around notable events (e.g. 2015 CHF unpeg,
> Brexit vote, 2020 Covid crisis onset, etc.) and see how it behaves.
> Does it detect the regime shifts in correlation that you know
> happened? Does it ever flip-flop clusters erratically in quiet times?
> By examining these,youcanrefineparameters.Alsotestduring
> *normal*periodstoensureitdoesn’tconstantlyfind phantom clusters (some
> clustering algorithms will always partition the data even if it’s
> actually one blob – K-Means with K fixed will do so no matter what;
> that’s where monitoring silhouette helps to know if that partitioning
> is meaningful or not).

In conclusion, integrating dynamic clustering into your FX dispersion
system can greatly enhance its regime detection capabilities. By using
suitable algorithms (from eficient online K-Means updates
[1](https://stackoverflow.com/questions/53091623/differences-between-minibatchkmeans-fit-and-minibatchkmeans-partial-fit#:~:text=So%20as%20well%20as%20the,as%20online%20clustering)
to adaptive density clustering), properly representing the data (sliding
correlations, normalized volatilities), and monitoring the evolution of
cluster structures (via silhouette scores, cluster counts, etc.), your
system will
gainanuancedunderstandingofmarketstatebeyondwhatasingle-numberdispersionmetriccanprovide.
This not only helps in generating more robust signals (confirming when
dispersion truly indicates a regime shift) but also in risk management
(adjusting strategy when the market’s correlation structure changes). As
noted in research, clustering analysis can reflect market reorganization
in times of stress
[3](https://www.cs.upc.edu/~argimiro/mytalks/MIDAS16slides.pdf#:~:text=Experiments%20with%20real%20data%20show,2015)
and provide insight into how assets “herd” or diverge. With careful
implementation and tuning, your minute-by-minute

> 13

clustering will become a powerful component of the FX dashboard,
alerting you to correlation breakdowns or formations in real time and
complementing the CSSD signals to navigate different market regimes with
confidence.

**Sources:**

> • Rezaei, B. (2019). *Volatility* *Clustering* *in* *Forex* *Market* –
> Demonstrates K-Means clustering of FX pairs by volatility, using elbow
> and silhouette methods to choose cluster count
> [8](https://medium.com/@bbkrze/volatility-clustering-in-forex-market-93db8e1e28d3#:~:text=To%20summarize%2C%20in%20this%20article%2C,determine%20the%20optimum%20cluster%20numbers)
> . Highlights how grouping pairs by similar characteristics can aid
> diversification.
>
> • Arratia, A. & Renedo, M. (2016). *Clustering* *of* *Exchange*
> *Rates* *and* *their* *Dynamics* – Shows that clustering currencies
> (especially with distance correlation) revealed realistic FX community
> reorganization during crises
> [3](https://www.cs.upc.edu/~argimiro/mytalks/MIDAS16slides.pdf#:~:text=Experiments%20with%20real%20data%20show,2015)
> .
>
> • Scikit-Learn Documentation – Notes that MiniBatchKMeans and Birch
> support partial_fit for online clustering updates
> [1](https://stackoverflow.com/questions/53091623/differences-between-minibatchkmeans-fit-and-minibatchkmeans-partial-fit#:~:text=So%20as%20well%20as%20the,as%20online%20clustering)
> .
>
> • Ferrer, J. et al. (2023). *Temporal* *Silhouette* *for* *Stream*
> *Clustering* *Validation* – Discusses internal validation indices
> (silhouette, Davies-Bouldin, etc.) and challenges of concept drift in
> streaming clustering
> [9](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=match%20at%20L274%20Many%20methods,compare%2011%20popular%20CVIs)
> [11](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=Silva%20et%20al,algorithms%20used%20in%20our%20experiments)
> .
>
> • Silva, A. et al. (2013). *Data* *Stream* *Clustering* *Algorithms* –
> Survey highlighting algorithms like BIRCH, CluStream, DenStream, and
> StreamKM++ for evolving data
> [4](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=Silva%20et%20al,algorithms%20used%20in%20our%20experiments)
> [2](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=that%20relies%20on%20micro,construct%20and%20save%20coreset%20information)
> , which inform possible approaches for real-time clustering.

[1](https://stackoverflow.com/questions/53091623/differences-between-minibatchkmeans-fit-and-minibatchkmeans-partial-fit#:~:text=So%20as%20well%20as%20the,as%20online%20clustering)
python - Differences between MiniBatchKMeans.fit and
MiniBatchKMeans.partial_fit - Stack Overflow
<https://stackoverflow.com/questions/53091623/differences-between-minibatchkmeans-fit-and-minibatchkmeans-partial-fit>

[2](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=that%20relies%20on%20micro,construct%20and%20save%20coreset%20information)
[4](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=Silva%20et%20al,algorithms%20used%20in%20our%20experiments)
[5](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=BIRCH%20%28Zhang%20et%20al,on%20their%20density%20before%20clustering)
[9](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=match%20at%20L274%20Many%20methods,compare%2011%20popular%20CVIs)
[11](https://link.springer.com/article/10.1007/s10994-023-06462-2#:~:text=Silva%20et%20al,algorithms%20used%20in%20our%20experiments)
Temporal silhouette: validation of stream clustering robust to concept
drift \| Machine Learning

<https://link.springer.com/article/10.1007/s10994-023-06462-2>

[3
10](https://www.cs.upc.edu/~argimiro/mytalks/MIDAS16slides.pdf#:~:text=Experiments%20with%20real%20data%20show,2015)
Clustering of exchange rates and their dynamics under different
dependence measures - ECML-PKDD 2016 Workshop MIDAS Sept. 19, 2016 -
Riva del Garda, Italy

<https://www.cs.upc.edu/~argimiro/mytalks/MIDAS16slides.pdf>

[6](https://medium.com/@bbkrze/volatility-clustering-in-forex-market-93db8e1e28d3#:~:text=To%20summarize%2C%20in%20this%20article%2C,determine%20the%20optimum%20cluster%20numbers)
[7](https://medium.com/@bbkrze/volatility-clustering-in-forex-market-93db8e1e28d3#:~:text=Dividing%20currency%20pairs%20into%20groups,with%20enough%20diversification%20between%20them)
[8](https://medium.com/@bbkrze/volatility-clustering-in-forex-market-93db8e1e28d3#:~:text=To%20summarize%2C%20in%20this%20article%2C,determine%20the%20optimum%20cluster%20numbers)
Volatility Clustering in Forex Market \| by Babak Rezaei \| Medium
<https://medium.com/@bbkrze/volatility-clustering-in-forex-market-93db8e1e28d3>

> 14
