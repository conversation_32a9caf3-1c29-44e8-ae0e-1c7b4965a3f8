# Dynamic FX Clustering Application - Test Runner Script
# PowerShell script to run comprehensive tests

param(
    [switch]$Unit,
    [switch]$Integration,
    [switch]$E2E,
    [switch]$Performance,
    [switch]$Coverage,
    [switch]$Benchmark,
    [switch]$All,
    [string]$TestPath = "",
    [switch]$Verbose
)

Write-Host "=== Dynamic FX Clustering Application Test Runner ===" -ForegroundColor Cyan

# Function to check if pytest is available
function Test-PytestAvailable {
    try {
        pytest --version | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Function to run tests with proper error handling
function Invoke-TestSuite {
    param(
        [string]$TestType,
        [string]$Path,
        [string[]]$ExtraArgs = @()
    )
    
    Write-Host "`n--- Running $TestType Tests ---" -ForegroundColor Yellow
    
    $args = @()
    if ($Path) { $args += $Path }
    if ($Verbose) { $args += "-v" }
    $args += $ExtraArgs
    
    try {
        $startTime = Get-Date
        pytest @args
        $endTime = Get-Date
        $duration = $endTime - $startTime
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ $TestType tests passed in $($duration.TotalSeconds.ToString('F2')) seconds" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ $TestType tests failed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ Error running $TestType tests: $_" -ForegroundColor Red
        return $false
    }
}

# Step 1: Check prerequisites
Write-Host "`n--- Checking Prerequisites ---" -ForegroundColor Yellow

if (-not (Test-PytestAvailable)) {
    Write-Host "✗ pytest not found. Installing..." -ForegroundColor Yellow
    try {
        pip install pytest pytest-cov pytest-benchmark
        Write-Host "✓ pytest installed" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to install pytest" -ForegroundColor Red
        exit 1
    }
}

# Check if we're in the correct directory
if (-not (Test-Path "tests")) {
    Write-Host "✗ tests directory not found" -ForegroundColor Red
    Write-Host "Please run this script from the application root directory" -ForegroundColor Red
    exit 1
}

# Step 2: Environment setup
Write-Host "`n--- Environment Setup ---" -ForegroundColor Yellow

# Set test environment variables
$env:TESTING = "1"
$env:OPENBLAS_NUM_THREADS = "1"
$env:MKL_NUM_THREADS = "1"

Write-Host "✓ Test environment configured" -ForegroundColor Green

# Step 3: Run tests based on parameters
$testResults = @()

if ($All -or (-not $Unit -and -not $Integration -and -not $E2E -and -not $Performance -and -not $TestPath)) {
    Write-Host "`n--- Running All Tests ---" -ForegroundColor Cyan
    
    # Unit tests
    $result = Invoke-TestSuite "Unit" "tests/unit/" @("--tb=short")
    $testResults += @{ Type = "Unit"; Success = $result }
    
    # Integration tests
    $result = Invoke-TestSuite "Integration" "tests/integration/" @("--tb=short")
    $testResults += @{ Type = "Integration"; Success = $result }
    
    # E2E tests
    $result = Invoke-TestSuite "End-to-End" "tests/e2e/" @("--tb=short")
    $testResults += @{ Type = "E2E"; Success = $result }
    
    # Performance tests (if requested or in comprehensive mode)
    if ($Performance -or $All) {
        $result = Invoke-TestSuite "Performance" "tests/performance/" @("--tb=short")
        $testResults += @{ Type = "Performance"; Success = $result }
    }
    
} else {
    # Run specific test categories
    
    if ($Unit) {
        $result = Invoke-TestSuite "Unit" "tests/unit/" @("--tb=short")
        $testResults += @{ Type = "Unit"; Success = $result }
    }
    
    if ($Integration) {
        $result = Invoke-TestSuite "Integration" "tests/integration/" @("--tb=short")
        $testResults += @{ Type = "Integration"; Success = $result }
    }
    
    if ($E2E) {
        $result = Invoke-TestSuite "End-to-End" "tests/e2e/" @("--tb=short")
        $testResults += @{ Type = "E2E"; Success = $result }
    }
    
    if ($Performance) {
        $result = Invoke-TestSuite "Performance" "tests/performance/" @("--tb=short")
        $testResults += @{ Type = "Performance"; Success = $result }
    }
    
    if ($TestPath) {
        $result = Invoke-TestSuite "Custom" $TestPath @("--tb=short")
        $testResults += @{ Type = "Custom"; Success = $result }
    }
}

# Step 4: Coverage report
if ($Coverage) {
    Write-Host "`n--- Generating Coverage Report ---" -ForegroundColor Yellow
    
    try {
        pytest tests/ --cov=clustering --cov-report=html --cov-report=term-missing
        
        if (Test-Path "htmlcov/index.html") {
            Write-Host "✓ Coverage report generated: htmlcov/index.html" -ForegroundColor Green
            
            $response = Read-Host "Open coverage report in browser? (y/N)"
            if ($response -eq "y" -or $response -eq "Y") {
                Start-Process "htmlcov/index.html"
            }
        }
    } catch {
        Write-Host "✗ Failed to generate coverage report: $_" -ForegroundColor Red
    }
}

# Step 5: Benchmark tests
if ($Benchmark) {
    Write-Host "`n--- Running Benchmark Tests ---" -ForegroundColor Yellow
    
    try {
        pytest tests/performance/ --benchmark-only --benchmark-sort=mean
        Write-Host "✓ Benchmark tests completed" -ForegroundColor Green
    } catch {
        Write-Host "✗ Benchmark tests failed: $_" -ForegroundColor Red
    }
}

# Step 6: Test summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Cyan

$totalTests = $testResults.Count
$passedTests = ($testResults | Where-Object { $_.Success }).Count
$failedTests = $totalTests - $passedTests

Write-Host "Total test suites: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red

foreach ($result in $testResults) {
    $status = if ($result.Success) { "✓" } else { "✗" }
    $color = if ($result.Success) { "Green" } else { "Red" }
    Write-Host "$status $($result.Type) tests" -ForegroundColor $color
}

# Step 7: Recommendations
if ($failedTests -gt 0) {
    Write-Host "`nRecommendations:" -ForegroundColor Yellow
    Write-Host "- Review failed test output above" -ForegroundColor White
    Write-Host "- Run specific test suites with -Verbose for more details" -ForegroundColor White
    Write-Host "- Check that MetaTrader 5 is properly configured for testing" -ForegroundColor White
    Write-Host "- Verify all dependencies are installed: pip install -r requirements-dev.txt" -ForegroundColor White
    
    exit 1
} else {
    Write-Host "`n✓ All tests passed successfully!" -ForegroundColor Green
    
    if (-not $Coverage) {
        Write-Host "Run with -Coverage flag to generate coverage report" -ForegroundColor Cyan
    }
    
    if (-not $Benchmark) {
        Write-Host "Run with -Benchmark flag to run performance benchmarks" -ForegroundColor Cyan
    }
}

Write-Host "`nTest run completed." -ForegroundColor Gray
