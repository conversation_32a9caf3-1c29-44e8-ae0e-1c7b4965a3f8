"""
End-to-end tests for dashboard workflow
"""

import pytest
import time
import threading
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
import dash
from dash import html, dcc
from dash.testing.application_runners import import_app


@pytest.mark.e2e
@pytest.mark.slow
class TestDashboardWorkflow:
    """End-to-end test cases for dashboard workflow"""
    
    @pytest.fixture
    def dash_app(self, test_symbols, temp_data_dir):
        """Create a test Dash app instance"""
        # Mock the clustering engine to avoid MT5 dependency
        with patch('run_clustering_app.ClusteringEngine') as mock_engine_class:
            mock_engine = Mock()
            mock_engine.symbols = test_symbols
            mock_engine.is_connected.return_value = True
            mock_engine.get_current_state.return_value = {
                'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
                'silhouette_score': 0.75,
                'n_clusters': 3,
                'timestamp': datetime.now()
            }
            mock_engine.get_historical_states.return_value = []
            mock_engine.get_performance_metrics.return_value = {
                'avg_clustering_time': 1.5,
                'avg_silhouette_score': 0.7
            }
            mock_engine_class.return_value = mock_engine
            
            # Import the app
            app = import_app('run_clustering_app')
            return app
    
    def test_dashboard_initialization(self, dash_app):
        """Test dashboard initializes correctly"""
        assert dash_app is not None
        assert hasattr(dash_app, 'layout')
        assert hasattr(dash_app, 'callback_map')
    
    def test_dashboard_layout_components(self, dash_app):
        """Test all required dashboard components are present"""
        layout = dash_app.layout
        
        # Check for main components
        component_ids = self._extract_component_ids(layout)
        
        required_components = [
            'clustering-data-store',
            'volatility-data-store', 
            'main-tabs',
            'dendrogram-graph',
            'sankey-graph',
            'statistics-panel',
            'event-log'
        ]
        
        for component_id in required_components:
            assert component_id in component_ids, f"Missing component: {component_id}"
    
    def test_data_update_workflow(self, dash_app, test_symbols):
        """Test complete data update workflow"""
        with patch('run_clustering_app.clustering_engine') as mock_engine:
            # Setup mock engine
            mock_engine.run_clustering_analysis.return_value = {
                'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
                'silhouette_score': 0.8,
                'n_clusters': 3,
                'timestamp': datetime.now(),
                'event_detected': True,
                'event_type': 'regime_change'
            }
            mock_engine.get_current_state.return_value = mock_engine.run_clustering_analysis.return_value
            mock_engine.symbols = test_symbols
            
            # Simulate data update interval callback
            from run_clustering_app import update_clustering_data
            
            result = update_clustering_data(1)  # Trigger update
            
            assert result is not None
            assert 'cluster_assignments' in result
            assert result['silhouette_score'] == 0.8
    
    def test_chart_update_workflow(self, dash_app):
        """Test chart update workflow"""
        # Mock clustering data
        clustering_data = {
            'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
            'silhouette_score': 0.75,
            'n_clusters': 3,
            'symbols': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 
                       'AUDUSD', 'NZDUSD', 'EURJPY', 'EURGBP', 'EURCHF'],
            'correlation_matrix': [[1.0, 0.5, 0.3] for _ in range(10)],
            'timestamp': datetime.now().isoformat()
        }
        
        # Test dendrogram update
        from run_clustering_app import update_dendrogram
        
        dendrogram_fig = update_dendrogram(clustering_data)
        
        assert dendrogram_fig is not None
        assert 'data' in dendrogram_fig
        assert 'layout' in dendrogram_fig
    
    def test_statistics_panel_workflow(self, dash_app):
        """Test statistics panel update workflow"""
        clustering_data = {
            'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
            'silhouette_score': 0.75,
            'n_clusters': 3,
            'symbols': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 
                       'AUDUSD', 'NZDUSD', 'EURJPY', 'EURGBP', 'EURCHF'],
            'timestamp': datetime.now().isoformat()
        }
        
        # Test statistics update
        from run_clustering_app import update_statistics_panel
        
        stats_content = update_statistics_panel(clustering_data, None)
        
        assert stats_content is not None
        assert isinstance(stats_content, list)
    
    def test_event_log_workflow(self, dash_app):
        """Test event log update workflow"""
        clustering_data = {
            'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
            'silhouette_score': 0.75,
            'n_clusters': 3,
            'event_detected': True,
            'event_type': 'regime_change',
            'event_confidence': 0.8,
            'timestamp': datetime.now().isoformat()
        }
        
        # Test event log update
        from run_clustering_app import update_event_log
        
        event_content = update_event_log(clustering_data)
        
        assert event_content is not None
        assert isinstance(event_content, list)
    
    def test_volatility_regime_workflow(self, dash_app):
        """Test volatility regime workflow"""
        volatility_data = {
            'regimes': [1, 2, 3, 1, 2],
            'regime_descriptions': {
                1: "Low volatility regime",
                2: "Medium volatility regime",
                3: "High volatility regime"
            },
            'calendar_data': {
                'dates': ['2025-01-01', '2025-01-02', '2025-01-03'],
                'regimes': [1, 2, 3],
                'colors': ['#1f77b4', '#ff7f0e', '#2ca02c']
            },
            'timestamp': datetime.now().isoformat()
        }
        
        # Test volatility regime calendar update
        from run_clustering_app import update_volatility_calendar
        
        calendar_fig = update_volatility_calendar(volatility_data)
        
        assert calendar_fig is not None
        assert 'data' in calendar_fig
        assert 'layout' in calendar_fig
    
    def test_advanced_analytics_workflow(self, dash_app):
        """Test advanced analytics workflow"""
        clustering_data = {
            'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
            'silhouette_score': 0.75,
            'n_clusters': 3,
            'advanced_analytics': {
                'avg_silhouette_score': 0.7,
                'avg_cluster_count': 3.2,
                'total_measurements': 100
            },
            'timestamp': datetime.now().isoformat()
        }
        
        # Test advanced analytics callbacks
        from run_clustering_app import update_clustering_metrics
        
        metrics_content = update_clustering_metrics(clustering_data)
        
        assert metrics_content is not None
        assert isinstance(metrics_content, list)
    
    def test_error_handling_workflow(self, dash_app):
        """Test error handling in dashboard workflow"""
        # Test with invalid/missing data
        invalid_data = None
        
        from run_clustering_app import update_dendrogram, update_statistics_panel
        
        # Should handle None data gracefully
        dendrogram_fig = update_dendrogram(invalid_data)
        assert dendrogram_fig is not None  # Should return empty/default figure
        
        stats_content = update_statistics_panel(invalid_data, None)
        assert stats_content is not None  # Should return error message or default content
    
    def test_real_time_updates_workflow(self, dash_app):
        """Test real-time updates workflow"""
        update_count = 0
        
        def mock_update_function():
            nonlocal update_count
            update_count += 1
            return {
                'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
                'silhouette_score': 0.75 + update_count * 0.01,
                'n_clusters': 3,
                'timestamp': datetime.now().isoformat()
            }
        
        # Simulate multiple updates
        for i in range(5):
            data = mock_update_function()
            assert data['silhouette_score'] == 0.75 + (i + 1) * 0.01
        
        assert update_count == 5
    
    def test_user_interaction_workflow(self, dash_app):
        """Test user interaction workflow"""
        # Test cluster selection
        clustering_data = {
            'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
            'silhouette_score': 0.75,
            'n_clusters': 3,
            'symbols': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 
                       'AUDUSD', 'NZDUSD', 'EURJPY', 'EURGBP', 'EURCHF'],
            'timestamp': datetime.now().isoformat()
        }
        
        # Simulate cluster selection
        selected_cluster = {
            'cluster_id': 0,
            'symbols': ['EURUSD', 'USDCHF', 'EURGBP'],
            'source': 'dendrogram'
        }
        
        from run_clustering_app import update_statistics_panel
        
        stats_content = update_statistics_panel(clustering_data, selected_cluster)
        
        assert stats_content is not None
        assert isinstance(stats_content, list)
    
    def test_performance_under_load(self, dash_app):
        """Test dashboard performance under load"""
        import time
        
        # Simulate rapid updates
        start_time = time.time()
        
        for i in range(10):
            clustering_data = {
                'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
                'silhouette_score': 0.75 + i * 0.01,
                'n_clusters': 3,
                'timestamp': datetime.now().isoformat()
            }
            
            # Update multiple components
            from run_clustering_app import (
                update_dendrogram, 
                update_statistics_panel,
                update_event_log
            )
            
            update_dendrogram(clustering_data)
            update_statistics_panel(clustering_data, None)
            update_event_log(clustering_data)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should complete within reasonable time
        assert total_time < 5.0  # Less than 5 seconds for 10 updates
    
    def _extract_component_ids(self, component):
        """Recursively extract component IDs from layout"""
        ids = set()
        
        if hasattr(component, 'id') and component.id:
            ids.add(component.id)
        
        if hasattr(component, 'children'):
            if isinstance(component.children, list):
                for child in component.children:
                    ids.update(self._extract_component_ids(child))
            elif component.children:
                ids.update(self._extract_component_ids(component.children))
        
        return ids
