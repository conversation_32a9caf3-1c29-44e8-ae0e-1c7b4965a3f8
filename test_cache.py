#!/usr/bin/env python3
"""
Test script to verify volatility regime caching behavior
"""

import os
import sys
import time
import logging

# Fix OpenBLAS threading issue
os.environ['OPENBLAS_NUM_THREADS'] = '8'
os.environ['MKL_NUM_THREADS'] = '8'
os.environ['NUMEXPR_NUM_THREADS'] = '8'
os.environ['OMP_NUM_THREADS'] = '8'

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from clustering.state_manager import StateManager
from clustering.data_manager import ClusteringDataManager
from config import CURRENCY_PAIRS

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cache_behavior():
    """Test that multiple calls to update_volatility_regimes use cache"""
    
    logger.info("=== Testing Volatility Regime Cache Behavior ===")
    
    # Initialize components
    data_manager = ClusteringDataManager()
    state_manager = StateManager(persistence_dir="data/test_clustering")
    
    # Connect to MT5
    if not data_manager.connect():
        logger.error("Failed to connect to MT5")
        return
    
    logger.info("Connected to MT5 successfully")
    
    # Test 1: First call should trigger data collection
    logger.info("\n--- Test 1: First call (should collect data) ---")
    start_time = time.time()
    result1 = state_manager.update_volatility_regimes(num_days_history=30, n_clusters=5)
    duration1 = time.time() - start_time
    logger.info(f"First call completed in {duration1:.2f}s, result: {result1}")
    
    # Test 2: Immediate second call should use cache
    logger.info("\n--- Test 2: Immediate second call (should use cache) ---")
    start_time = time.time()
    result2 = state_manager.update_volatility_regimes(num_days_history=30, n_clusters=5)
    duration2 = time.time() - start_time
    logger.info(f"Second call completed in {duration2:.2f}s, result: {result2}")
    
    # Test 3: Third call should also use cache
    logger.info("\n--- Test 3: Third call (should use cache) ---")
    start_time = time.time()
    result3 = state_manager.update_volatility_regimes(num_days_history=30, n_clusters=5)
    duration3 = time.time() - start_time
    logger.info(f"Third call completed in {duration3:.2f}s, result: {result3}")
    
    # Test 4: Fourth call should also use cache
    logger.info("\n--- Test 4: Fourth call (should use cache) ---")
    start_time = time.time()
    result4 = state_manager.update_volatility_regimes(num_days_history=30, n_clusters=5)
    duration4 = time.time() - start_time
    logger.info(f"Fourth call completed in {duration4:.2f}s, result: {result4}")
    
    # Summary
    logger.info(f"\n=== CACHE TEST SUMMARY ===")
    logger.info(f"Call 1 (data collection): {duration1:.2f}s")
    logger.info(f"Call 2 (cached): {duration2:.2f}s")
    logger.info(f"Call 3 (cached): {duration3:.2f}s") 
    logger.info(f"Call 4 (cached): {duration4:.2f}s")
    
    if duration2 < duration1 * 0.1 and duration3 < duration1 * 0.1 and duration4 < duration1 * 0.1:
        logger.info("✅ CACHE WORKING: Subsequent calls are significantly faster")
    else:
        logger.warning("❌ CACHE NOT WORKING: Subsequent calls are still slow")
    
    # Cleanup
    data_manager.disconnect()

if __name__ == "__main__":
    test_cache_behavior()
