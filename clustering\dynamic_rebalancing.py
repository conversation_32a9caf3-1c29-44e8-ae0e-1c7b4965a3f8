"""
Dynamic Rebalancing Strategies for Portfolio Management

This module provides sophisticated rebalancing algorithms that respond to:
- Cluster structure changes
- Volatility regime shifts
- Correlation breakdowns
- Market stress events

Features:
- Regime-aware rebalancing
- Threshold-based triggers
- Risk-adjusted rebalancing
- Transaction cost optimization
- Emergency rebalancing protocols
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, NamedTuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import logging

from portfolio_analytics import PortfolioAnalytics, RebalancingSignal
from risk_optimization import RiskOptimizer, OptimizationResult

logger = logging.getLogger(__name__)


class RebalancingTrigger(Enum):
    """Types of rebalancing triggers"""
    CLUSTER_CHANGE = "cluster_change"
    VOLATILITY_REGIME = "volatility_regime"
    CORRELATION_SHIFT = "correlation_shift"
    RISK_BUDGET_BREACH = "risk_budget_breach"
    SCHEDULED = "scheduled"
    EMERGENCY = "emergency"


class RebalancingUrgency(Enum):
    """Urgency levels for rebalancing"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RebalancingThresholds:
    """Configuration for rebalancing thresholds"""
    cluster_stability_threshold: float = 0.7
    correlation_shift_threshold: float = 0.3
    volatility_change_threshold: float = 0.5
    risk_budget_deviation_threshold: float = 0.2
    max_position_drift: float = 0.1
    min_rebalancing_interval_hours: int = 6


@dataclass
class RebalancingAction:
    """Specific rebalancing action to take"""
    timestamp: datetime
    trigger: RebalancingTrigger
    urgency: RebalancingUrgency
    current_weights: Dict[str, float]
    target_weights: Dict[str, float]
    weight_changes: Dict[str, float]
    expected_cost: float
    expected_benefit: float
    reason: str
    confidence: float


@dataclass
class RebalancingHistory:
    """Historical record of rebalancing actions"""
    action: RebalancingAction
    execution_timestamp: Optional[datetime] = None
    actual_cost: Optional[float] = None
    performance_impact: Optional[float] = None
    success: bool = False


class DynamicRebalancer:
    """
    Dynamic Portfolio Rebalancing Engine
    
    Monitors market conditions and portfolio state to determine optimal
    rebalancing timing and allocation adjustments.
    """
    
    def __init__(self, 
                 symbols: List[str],
                 thresholds: Optional[RebalancingThresholds] = None,
                 transaction_cost_bps: float = 5.0):
        """
        Initialize dynamic rebalancer
        
        Args:
            symbols: List of asset symbols
            thresholds: Rebalancing threshold configuration
            transaction_cost_bps: Transaction cost in basis points
        """
        self.symbols = symbols
        self.thresholds = thresholds or RebalancingThresholds()
        self.transaction_cost_bps = transaction_cost_bps
        
        # Initialize components
        self.portfolio_analytics = PortfolioAnalytics(symbols)
        self.risk_optimizer = RiskOptimizer(symbols)
        
        # State tracking
        self.last_rebalancing = None
        self.rebalancing_history: List[RebalancingHistory] = []
        self.current_portfolio_state = None
        
        # Market state tracking
        self.previous_correlation_matrix = None
        self.previous_cluster_assignments = None
        self.previous_volatility_profiles = None
        self.previous_regime_classification = None
    
    def evaluate_rebalancing_need(self, 
                                current_weights: Dict[str, float],
                                correlation_matrix: np.ndarray,
                                cluster_assignments: List[int],
                                volatility_profiles: Dict[str, float],
                                regime_classification: Optional[str] = None) -> List[RebalancingAction]:
        """
        Evaluate if rebalancing is needed based on current market conditions
        
        Args:
            current_weights: Current portfolio weights
            correlation_matrix: Current correlation matrix
            cluster_assignments: Current cluster assignments
            volatility_profiles: Current volatility profiles
            regime_classification: Current market regime classification
            
        Returns:
            List of recommended rebalancing actions
        """
        try:
            actions = []
            timestamp = datetime.now()
            
            # Check if minimum time interval has passed since last rebalancing
            if self._is_too_soon_to_rebalance():
                return []
            
            # 1. Check for cluster structure changes
            if self.previous_cluster_assignments is not None:
                cluster_action = self._evaluate_cluster_changes(
                    current_weights, cluster_assignments, correlation_matrix, 
                    volatility_profiles, timestamp
                )
                if cluster_action:
                    actions.append(cluster_action)
            
            # 2. Check for correlation regime shifts
            if self.previous_correlation_matrix is not None:
                correlation_action = self._evaluate_correlation_shifts(
                    current_weights, correlation_matrix, volatility_profiles, timestamp
                )
                if correlation_action:
                    actions.append(correlation_action)
            
            # 3. Check for volatility regime changes
            if self.previous_volatility_profiles is not None:
                volatility_action = self._evaluate_volatility_changes(
                    current_weights, volatility_profiles, correlation_matrix, timestamp
                )
                if volatility_action:
                    actions.append(volatility_action)
            
            # 4. Check for risk budget breaches
            risk_action = self._evaluate_risk_budget_breaches(
                current_weights, correlation_matrix, volatility_profiles, timestamp
            )
            if risk_action:
                actions.append(risk_action)
            
            # 5. Check for position drift
            drift_action = self._evaluate_position_drift(
                current_weights, timestamp
            )
            if drift_action:
                actions.append(drift_action)
            
            # Update state tracking
            self.previous_correlation_matrix = correlation_matrix.copy()
            self.previous_cluster_assignments = cluster_assignments.copy()
            self.previous_volatility_profiles = volatility_profiles.copy()
            self.previous_regime_classification = regime_classification
            
            # Sort actions by urgency and expected benefit
            actions.sort(key=lambda x: (x.urgency.value, -x.expected_benefit))
            
            return actions
            
        except Exception as e:
            logger.error(f"Error evaluating rebalancing need: {str(e)}")
            return []
    
    def _is_too_soon_to_rebalance(self) -> bool:
        """Check if minimum time interval has passed since last rebalancing"""
        if self.last_rebalancing is None:
            return False
        
        time_since_last = datetime.now() - self.last_rebalancing
        min_interval = timedelta(hours=self.thresholds.min_rebalancing_interval_hours)
        
        return time_since_last < min_interval
    
    def _evaluate_cluster_changes(self, 
                                current_weights: Dict[str, float],
                                cluster_assignments: List[int],
                                correlation_matrix: np.ndarray,
                                volatility_profiles: Dict[str, float],
                                timestamp: datetime) -> Optional[RebalancingAction]:
        """Evaluate need for rebalancing due to cluster changes"""
        try:
            # Calculate cluster stability
            stability = self._calculate_cluster_stability(
                self.previous_cluster_assignments, cluster_assignments
            )
            
            if stability < self.thresholds.cluster_stability_threshold:
                # Significant cluster change detected
                
                # Generate new cluster-diversified allocation
                new_weights = self.portfolio_analytics._create_cluster_diversified_portfolio(
                    cluster_assignments, volatility_profiles
                )
                
                # Calculate weight changes
                weight_changes = {
                    symbol: new_weights.get(symbol, 0.0) - current_weights.get(symbol, 0.0)
                    for symbol in self.symbols
                }
                
                # Estimate transaction costs
                total_turnover = sum(abs(change) for change in weight_changes.values())
                estimated_cost = total_turnover * self.transaction_cost_bps / 10000
                
                # Estimate benefit (risk reduction)
                current_risk = self.portfolio_analytics.calculate_portfolio_risk(
                    current_weights, correlation_matrix, volatility_profiles
                )
                new_risk = self.portfolio_analytics.calculate_portfolio_risk(
                    new_weights, correlation_matrix, volatility_profiles
                )
                expected_benefit = max(0, current_risk - new_risk)
                
                # Determine urgency
                if stability < 0.4:
                    urgency = RebalancingUrgency.CRITICAL
                elif stability < 0.5:
                    urgency = RebalancingUrgency.HIGH
                else:
                    urgency = RebalancingUrgency.MEDIUM
                
                return RebalancingAction(
                    timestamp=timestamp,
                    trigger=RebalancingTrigger.CLUSTER_CHANGE,
                    urgency=urgency,
                    current_weights=current_weights,
                    target_weights=new_weights,
                    weight_changes=weight_changes,
                    expected_cost=estimated_cost,
                    expected_benefit=expected_benefit,
                    reason=f"Cluster structure changed significantly (stability: {stability:.2f})",
                    confidence=1.0 - stability
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error evaluating cluster changes: {str(e)}")
            return None
    
    def _evaluate_correlation_shifts(self, 
                                   current_weights: Dict[str, float],
                                   correlation_matrix: np.ndarray,
                                   volatility_profiles: Dict[str, float],
                                   timestamp: datetime) -> Optional[RebalancingAction]:
        """Evaluate need for rebalancing due to correlation shifts"""
        try:
            # Calculate correlation shift magnitude
            shift_magnitude = self._calculate_correlation_shift(
                self.previous_correlation_matrix, correlation_matrix
            )
            
            if shift_magnitude > self.thresholds.correlation_shift_threshold:
                # Significant correlation shift detected
                
                # Optimize for minimum variance with new correlation structure
                covariance_matrix = self.risk_optimizer.calculate_covariance_matrix(
                    correlation_matrix, volatility_profiles
                )
                
                optimization_result = self.risk_optimizer.optimize_minimum_variance(covariance_matrix)
                
                if optimization_result.success:
                    new_weights = optimization_result.weights
                    
                    # Calculate weight changes
                    weight_changes = {
                        symbol: new_weights.get(symbol, 0.0) - current_weights.get(symbol, 0.0)
                        for symbol in self.symbols
                    }
                    
                    # Estimate costs and benefits
                    total_turnover = sum(abs(change) for change in weight_changes.values())
                    estimated_cost = total_turnover * self.transaction_cost_bps / 10000
                    expected_benefit = shift_magnitude * 0.1  # Simplified benefit calculation
                    
                    # Determine urgency based on shift magnitude
                    if shift_magnitude > 0.6:
                        urgency = RebalancingUrgency.HIGH
                    elif shift_magnitude > 0.4:
                        urgency = RebalancingUrgency.MEDIUM
                    else:
                        urgency = RebalancingUrgency.LOW
                    
                    return RebalancingAction(
                        timestamp=timestamp,
                        trigger=RebalancingTrigger.CORRELATION_SHIFT,
                        urgency=urgency,
                        current_weights=current_weights,
                        target_weights=new_weights,
                        weight_changes=weight_changes,
                        expected_cost=estimated_cost,
                        expected_benefit=expected_benefit,
                        reason=f"Correlation structure shifted significantly (magnitude: {shift_magnitude:.2f})",
                        confidence=min(shift_magnitude, 1.0)
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"Error evaluating correlation shifts: {str(e)}")
            return None

    def _evaluate_volatility_changes(self,
                                   current_weights: Dict[str, float],
                                   volatility_profiles: Dict[str, float],
                                   correlation_matrix: np.ndarray,
                                   timestamp: datetime) -> Optional[RebalancingAction]:
        """Evaluate need for rebalancing due to volatility regime changes"""
        try:
            # Calculate average volatility change
            current_avg_vol = np.mean(list(volatility_profiles.values()))
            previous_avg_vol = np.mean(list(self.previous_volatility_profiles.values()))

            volatility_change = abs(current_avg_vol - previous_avg_vol) / previous_avg_vol

            if volatility_change > self.thresholds.volatility_change_threshold:
                # Significant volatility change detected

                # Adjust strategy based on volatility direction
                if current_avg_vol > previous_avg_vol:
                    # Volatility increased - focus on low volatility assets
                    new_weights = self.portfolio_analytics._create_volatility_focused_portfolio(
                        volatility_profiles, "low"
                    )
                    reason = f"Volatility increased by {volatility_change:.1%}, shifting to low-vol assets"
                else:
                    # Volatility decreased - can take more risk
                    new_weights = self.portfolio_analytics._create_volatility_focused_portfolio(
                        volatility_profiles, "high"
                    )
                    reason = f"Volatility decreased by {volatility_change:.1%}, increasing risk exposure"

                # Calculate weight changes
                weight_changes = {
                    symbol: new_weights.get(symbol, 0.0) - current_weights.get(symbol, 0.0)
                    for symbol in self.symbols
                }

                # Estimate costs and benefits
                total_turnover = sum(abs(change) for change in weight_changes.values())
                estimated_cost = total_turnover * self.transaction_cost_bps / 10000
                expected_benefit = volatility_change * 0.05  # Simplified benefit calculation

                # Determine urgency
                if volatility_change > 1.0:  # 100% change
                    urgency = RebalancingUrgency.HIGH
                elif volatility_change > 0.7:
                    urgency = RebalancingUrgency.MEDIUM
                else:
                    urgency = RebalancingUrgency.LOW

                return RebalancingAction(
                    timestamp=timestamp,
                    trigger=RebalancingTrigger.VOLATILITY_REGIME,
                    urgency=urgency,
                    current_weights=current_weights,
                    target_weights=new_weights,
                    weight_changes=weight_changes,
                    expected_cost=estimated_cost,
                    expected_benefit=expected_benefit,
                    reason=reason,
                    confidence=min(volatility_change, 1.0)
                )

            return None

        except Exception as e:
            logger.error(f"Error evaluating volatility changes: {str(e)}")
            return None

    def _evaluate_risk_budget_breaches(self,
                                     current_weights: Dict[str, float],
                                     correlation_matrix: np.ndarray,
                                     volatility_profiles: Dict[str, float],
                                     timestamp: datetime) -> Optional[RebalancingAction]:
        """Evaluate need for rebalancing due to risk budget breaches"""
        try:
            # Calculate current risk budgets
            covariance_matrix = self.risk_optimizer.calculate_covariance_matrix(
                correlation_matrix, volatility_profiles
            )

            risk_budgets = self.risk_optimizer.calculate_risk_budgets(
                current_weights, covariance_matrix
            )

            # Check for significant deviations from equal risk contribution
            target_risk_contribution = 1.0 / len(self.symbols)
            max_deviation = 0.0

            for risk_budget in risk_budgets:
                deviation = abs(risk_budget.risk_percentage - target_risk_contribution)
                max_deviation = max(max_deviation, deviation)

            if max_deviation > self.thresholds.risk_budget_deviation_threshold:
                # Risk budget breach detected - rebalance to risk parity

                optimization_result = self.risk_optimizer.optimize_risk_parity(covariance_matrix)

                if optimization_result.success:
                    new_weights = optimization_result.weights

                    # Calculate weight changes
                    weight_changes = {
                        symbol: new_weights.get(symbol, 0.0) - current_weights.get(symbol, 0.0)
                        for symbol in self.symbols
                    }

                    # Estimate costs and benefits
                    total_turnover = sum(abs(change) for change in weight_changes.values())
                    estimated_cost = total_turnover * self.transaction_cost_bps / 10000
                    expected_benefit = max_deviation * 0.1  # Risk reduction benefit

                    # Determine urgency
                    if max_deviation > 0.4:
                        urgency = RebalancingUrgency.HIGH
                    elif max_deviation > 0.3:
                        urgency = RebalancingUrgency.MEDIUM
                    else:
                        urgency = RebalancingUrgency.LOW

                    return RebalancingAction(
                        timestamp=timestamp,
                        trigger=RebalancingTrigger.RISK_BUDGET_BREACH,
                        urgency=urgency,
                        current_weights=current_weights,
                        target_weights=new_weights,
                        weight_changes=weight_changes,
                        expected_cost=estimated_cost,
                        expected_benefit=expected_benefit,
                        reason=f"Risk budget breach detected (max deviation: {max_deviation:.1%})",
                        confidence=min(max_deviation * 2, 1.0)
                    )

            return None

        except Exception as e:
            logger.error(f"Error evaluating risk budget breaches: {str(e)}")
            return None

    def _evaluate_position_drift(self,
                               current_weights: Dict[str, float],
                               timestamp: datetime) -> Optional[RebalancingAction]:
        """Evaluate need for rebalancing due to position drift"""
        try:
            if not hasattr(self, 'target_weights') or self.target_weights is None:
                return None

            # Calculate maximum position drift
            max_drift = 0.0
            for symbol in self.symbols:
                current_weight = current_weights.get(symbol, 0.0)
                target_weight = self.target_weights.get(symbol, 0.0)
                drift = abs(current_weight - target_weight)
                max_drift = max(max_drift, drift)

            if max_drift > self.thresholds.max_position_drift:
                # Significant position drift detected

                # Target weights are the original strategic allocation
                new_weights = self.target_weights.copy()

                # Calculate weight changes
                weight_changes = {
                    symbol: new_weights.get(symbol, 0.0) - current_weights.get(symbol, 0.0)
                    for symbol in self.symbols
                }

                # Estimate costs and benefits
                total_turnover = sum(abs(change) for change in weight_changes.values())
                estimated_cost = total_turnover * self.transaction_cost_bps / 10000
                expected_benefit = max_drift * 0.05  # Drift correction benefit

                return RebalancingAction(
                    timestamp=timestamp,
                    trigger=RebalancingTrigger.SCHEDULED,
                    urgency=RebalancingUrgency.LOW,
                    current_weights=current_weights,
                    target_weights=new_weights,
                    weight_changes=weight_changes,
                    expected_cost=estimated_cost,
                    expected_benefit=expected_benefit,
                    reason=f"Position drift exceeded threshold (max drift: {max_drift:.1%})",
                    confidence=0.8
                )

            return None

        except Exception as e:
            logger.error(f"Error evaluating position drift: {str(e)}")
            return None

    def _calculate_cluster_stability(self,
                                   previous_assignments: List[int],
                                   current_assignments: List[int]) -> float:
        """Calculate stability between two cluster assignments"""
        try:
            if len(previous_assignments) != len(current_assignments):
                return 0.0

            # Calculate adjusted rand index (simplified)
            matches = sum(1 for p, c in zip(previous_assignments, current_assignments) if p == c)
            stability = matches / len(previous_assignments)

            return stability

        except Exception as e:
            logger.error(f"Error calculating cluster stability: {str(e)}")
            return 0.0

    def _calculate_correlation_shift(self,
                                   previous_matrix: np.ndarray,
                                   current_matrix: np.ndarray) -> float:
        """Calculate the magnitude of correlation matrix change"""
        try:
            if previous_matrix.shape != current_matrix.shape:
                return 1.0  # Maximum change

            # Calculate Frobenius norm of the difference
            diff_matrix = current_matrix - previous_matrix
            frobenius_norm = np.linalg.norm(diff_matrix, 'fro')

            # Normalize by matrix size
            normalized_change = frobenius_norm / np.sqrt(previous_matrix.size)

            return min(normalized_change, 1.0)

        except Exception as e:
            logger.error(f"Error calculating correlation shift: {str(e)}")
            return 0.0

    def execute_rebalancing(self, action: RebalancingAction) -> RebalancingHistory:
        """
        Execute a rebalancing action (simulation)

        Args:
            action: Rebalancing action to execute

        Returns:
            Rebalancing history record
        """
        try:
            execution_timestamp = datetime.now()

            # Simulate execution (in real implementation, this would place orders)
            success = True
            actual_cost = action.expected_cost * (1.0 + np.random.normal(0, 0.1))  # Add some noise

            # Record the execution
            history = RebalancingHistory(
                action=action,
                execution_timestamp=execution_timestamp,
                actual_cost=actual_cost,
                success=success
            )

            # Update state
            self.last_rebalancing = execution_timestamp
            self.target_weights = action.target_weights.copy()
            self.rebalancing_history.append(history)

            logger.info(f"Executed rebalancing: {action.trigger.value} - {action.reason}")

            return history

        except Exception as e:
            logger.error(f"Error executing rebalancing: {str(e)}")
            return RebalancingHistory(
                action=action,
                execution_timestamp=datetime.now(),
                success=False
            )

    def get_rebalancing_summary(self) -> Dict:
        """Get summary of rebalancing activity"""
        try:
            if not self.rebalancing_history:
                return {
                    "total_rebalancings": 0,
                    "success_rate": 0.0,
                    "total_cost": 0.0,
                    "avg_cost": 0.0,
                    "trigger_breakdown": {},
                    "last_rebalancing": None
                }

            successful_rebalancings = [h for h in self.rebalancing_history if h.success]
            total_cost = sum(h.actual_cost or 0 for h in successful_rebalancings)

            # Count triggers
            trigger_counts = {}
            for history in self.rebalancing_history:
                trigger = history.action.trigger.value
                trigger_counts[trigger] = trigger_counts.get(trigger, 0) + 1

            return {
                "total_rebalancings": len(self.rebalancing_history),
                "successful_rebalancings": len(successful_rebalancings),
                "success_rate": len(successful_rebalancings) / len(self.rebalancing_history),
                "total_cost": total_cost,
                "avg_cost": total_cost / len(successful_rebalancings) if successful_rebalancings else 0,
                "trigger_breakdown": trigger_counts,
                "last_rebalancing": self.last_rebalancing
            }

        except Exception as e:
            logger.error(f"Error generating rebalancing summary: {str(e)}")
            return {}

    def set_target_weights(self, target_weights: Dict[str, float]):
        """Set target strategic allocation weights"""
        # Validate weights sum to 1
        total_weight = sum(target_weights.values())
        if abs(total_weight - 1.0) > 1e-6:
            logger.warning(f"Target weights sum to {total_weight}, normalizing to 1.0")
            target_weights = {k: v/total_weight for k, v in target_weights.items()}

        self.target_weights = target_weights
        logger.info("Updated target strategic allocation weights")


def create_rebalancing_demo():
    """Create a demonstration of dynamic rebalancing functionality"""

    # Sample currency pairs
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']

    # Initialize rebalancer
    rebalancer = DynamicRebalancer(symbols)

    # Set initial target weights (equal allocation)
    target_weights = {symbol: 1.0/len(symbols) for symbol in symbols}
    rebalancer.set_target_weights(target_weights)

    # Simulate market conditions
    np.random.seed(42)

    # Current portfolio weights (with some drift)
    current_weights = {
        symbol: max(0.01, target_weights[symbol] + np.random.normal(0, 0.05))
        for symbol in symbols
    }
    # Normalize
    total_weight = sum(current_weights.values())
    current_weights = {k: v/total_weight for k, v in current_weights.items()}

    # Generate correlation matrix
    correlation_matrix = np.random.rand(len(symbols), len(symbols))
    correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2
    np.fill_diagonal(correlation_matrix, 1.0)

    # Generate cluster assignments
    cluster_assignments = [i % 3 for i in range(len(symbols))]  # 3 clusters

    # Generate volatility profiles
    volatility_profiles = {
        symbol: 0.01 + np.random.exponential(0.005) for symbol in symbols
    }

    print("=== Dynamic Rebalancing Demo ===")
    print(f"Symbols: {symbols}")
    print(f"Current weights: {current_weights}")
    print(f"Target weights: {target_weights}")
    print(f"Volatility profiles: {volatility_profiles}")
    print()

    # Evaluate rebalancing need
    actions = rebalancer.evaluate_rebalancing_need(
        current_weights=current_weights,
        correlation_matrix=correlation_matrix,
        cluster_assignments=cluster_assignments,
        volatility_profiles=volatility_profiles,
        regime_classification="normal"
    )

    print(f"Rebalancing actions recommended: {len(actions)}")

    for i, action in enumerate(actions):
        print(f"\nAction {i+1}:")
        print(f"  Trigger: {action.trigger.value}")
        print(f"  Urgency: {action.urgency.value}")
        print(f"  Reason: {action.reason}")
        print(f"  Expected cost: {action.expected_cost:.4f}")
        print(f"  Expected benefit: {action.expected_benefit:.4f}")
        print(f"  Confidence: {action.confidence:.2f}")

        # Show top 3 weight changes
        sorted_changes = sorted(action.weight_changes.items(),
                              key=lambda x: abs(x[1]), reverse=True)[:3]
        print(f"  Top weight changes:")
        for symbol, change in sorted_changes:
            print(f"    {symbol}: {change:+.3f}")

    # Execute first action if any
    if actions:
        print(f"\nExecuting first action...")
        history = rebalancer.execute_rebalancing(actions[0])
        print(f"Execution successful: {history.success}")
        if history.actual_cost:
            print(f"Actual cost: {history.actual_cost:.4f}")

    # Show summary
    summary = rebalancer.get_rebalancing_summary()
    print(f"\nRebalancing Summary:")
    for key, value in summary.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    create_rebalancing_demo()
