# Dynamic FX Clustering Application

A real-time forex market analysis tool that combines dynamic correlation clustering with volatility regime detection for comprehensive market insights.

## 🚀 Features

- **Real-time Clustering**: Dynamic correlation-based clustering of 28 major currency pairs
- **Volatility Regimes**: 5-level volatility classification with regime transition tracking
- **Interactive Dashboard**: Web-based interface with multiple visualization tabs
- **Advanced Analytics**: Clustering quality metrics, performance trends, and market insights
- **Real-time Alerts**: Configurable alert system for market events and regime changes
- **High Performance**: Rust-powered clustering engine with Python integration
- **Export Capabilities**: Data and chart export in multiple formats

## 📊 Dashboard Components

### Clustering Analysis
- **Dendrogram**: Hierarchical clustering visualization
- **Sankey Diagram**: Cluster transition flows over time
- **Statistics Panel**: Real-time clustering metrics and quality scores
- **Event Log**: Market events and significant changes

### Volatility Regimes
- **Calendar View**: Daily volatility regime classification
- **Transitions Chart**: Intraday regime changes and patterns
- **Regime Explanations**: Detailed descriptions of each volatility state

### Advanced Analytics
- **Quality Metrics**: Silhouette scores, stability measures, and validation
- **Market Classification**: Current market state and trend analysis
- **Performance Trends**: Historical performance and optimization metrics
- **Alert Dashboard**: Real-time alerts and system notifications

## 🛠️ Installation

### Quick Installation (Windows)
```powershell
# Run automated installer
.\scripts\install.ps1
```

### Manual Installation
```powershell
# 1. Install Python dependencies
pip install -r requirements.txt

# 2. Build Rust clustering engine
cd cluster_core
cargo build --release
cd ..

# 3. Configure MetaTrader 5
# - Enable automated trading
# - Enable DLL imports
# - Ensure account access to currency pairs
```

### Development Installation
```powershell
# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install
```

## 🚀 Quick Start

### 1. Start the Application
```powershell
# Using startup script (recommended)
.\scripts\start.ps1

# Or manually
python run_clustering_app.py
```

### 2. Access Dashboard
Open your browser to: `http://localhost:8050`

### 3. Verify Connection
- Check "MT5 Connected" status indicator
- Wait for initial data collection (30-60 seconds)
- Explore clustering analysis and volatility regimes

## 📋 Requirements

### System Requirements
- **OS**: Windows 10/11 (64-bit)
- **CPU**: Intel i5 / AMD Ryzen 5 (minimum)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 2GB free space

### Software Requirements
- **Python**: 3.9, 3.10, 3.11, or 3.12
- **MetaTrader 5**: Latest version
- **Rust**: Latest stable (for building clustering engine)

### Currency Pairs
Monitors 28 major forex pairs:
- Majors: EURUSD, GBPUSD, USDJPY, USDCHF, USDCAD, AUDUSD, NZDUSD
- Crosses: EURJPY, EURGBP, EURCHF, GBPJPY, GBPCHF, GBPCAD, etc.

## 🧪 Testing

### Run All Tests
```powershell
.\scripts\test.ps1 -All
```

### Run Specific Test Categories
```powershell
# Unit tests only
.\scripts\test.ps1 -Unit

# Integration tests
.\scripts\test.ps1 -Integration

# End-to-end tests
.\scripts\test.ps1 -E2E

# Performance benchmarks
.\scripts\test.ps1 -Performance -Benchmark
```

### Generate Coverage Report
```powershell
.\scripts\test.ps1 -Coverage
```

## 📖 Documentation

- **[User Guide](docs/USER_GUIDE.md)**: Complete user documentation
- **[Installation Guide](docs/INSTALLATION.md)**: Detailed installation instructions
- **[Developer Guide](docs/DEVELOPER_GUIDE.md)**: API reference and development guide

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Dashboard     │    │   Python        │    │   Rust          │
│   (Dash/Plotly) │◄──►│   Backend       │◄──►│   Clustering    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   State Mgmt    │    │   MetaTrader 5  │
│                 │    │   & Analytics   │    │   Data Source   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components
- **Frontend**: Dash + Plotly + Bootstrap for interactive web interface
- **Backend**: Python state management, analytics, and coordination
- **Clustering Engine**: High-performance Rust algorithms via PyO3
- **Data Source**: MetaTrader 5 real-time forex data

## 🔧 Configuration

### Basic Configuration (`config.py`)
```python
# Timezone settings
MARKET_TIMEZONE = 'Europe/Bucharest'

# Update frequency
UPDATE_INTERVAL = 30  # seconds

# Data retention
DATA_HISTORY_HOURS = 24  # hours

# Currency pairs (customize as needed)
CURRENCY_PAIRS = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF',
    # ... add or remove pairs
]
```

### Performance Tuning
```python
# For high-end systems
UPDATE_INTERVAL = 15
DATA_HISTORY_HOURS = 48

# For low-end systems
CURRENCY_PAIRS = CURRENCY_PAIRS[:14]  # Monitor fewer pairs
UPDATE_INTERVAL = 60
```

## 🚨 Troubleshooting

### Common Issues

#### MetaTrader 5 Connection Failed
- Ensure MT5 is running and logged in
- Check Expert Advisors settings (allow automated trading)
- Verify account has access to required currency pairs

#### Empty Charts or No Data
- Wait for initial data collection (1-2 minutes)
- Check internet connection and MT5 connectivity
- Verify currency pairs are available in your account

#### Performance Issues
- Reduce number of monitored currency pairs
- Increase update interval
- Check system resources (CPU, memory)

### Error Codes
- **E001**: MT5 Connection Failed
- **E002**: Insufficient Data
- **E003**: Clustering Failed
- **E004**: Data Quality Low
- **E005**: Memory Error

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run test suite: `.\scripts\test.ps1 -All`
5. Submit pull request

### Development Workflow
```powershell
# Setup development environment
pip install -r requirements-dev.txt
pre-commit install

# Run tests
.\scripts\test.ps1 -All -Coverage

# Code formatting
black .
flake8 clustering/
mypy clustering/
```

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the [docs/](docs/) directory
- **Issues**: Report bugs and feature requests via GitHub issues
- **Testing**: Run `.\scripts\test.ps1 -All` to verify installation

## 📈 Performance

- **Clustering Speed**: < 2 seconds for 28 currency pairs
- **Memory Usage**: < 500MB typical operation
- **Update Frequency**: 30-second default (configurable)
- **Data Throughput**: > 10,000 data points/second

## 🔮 Volatility Regimes

The application classifies market volatility into 5 distinct regimes:

1. **Low Volatility** (Regime 1): Stable, calm market conditions
2. **Medium Volatility** (Regime 2): Normal market activity
3. **High Volatility** (Regime 3): Increased market stress
4. **Extreme Volatility** (Regime 4): Significant market disruption
5. **Crisis Volatility** (Regime 5): Market crisis conditions

---

**Dynamic FX Clustering Application** - Real-time forex market analysis with advanced clustering and volatility regime detection.
