[package]
name = "cluster_core"
version = "0.1.0"
edition = "2021"

[lib]
name = "cluster_core"
crate-type = ["cdylib"]

[dependencies]
pyo3 = { version = "0.22", features = ["extension-module"] }
ndarray = { version = "0.15", features = ["serde"] }
nalgebra = "0.32"
linfa = "0.7"
linfa-clustering = "0.7"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
rayon = "1.7"
thiserror = "1.0"
chrono = { version = "0.4", features = ["serde"] }
