# Dynamic FX Clustering Application - Automated Installation Script
# PowerShell script for Windows installation

param(
    [switch]$Dev,
    [switch]$SkipRust,
    [switch]$SkipMT5Check,
    [string]$InstallPath = $PWD
)

Write-Host "=== Dynamic FX Clustering Application Installer ===" -ForegroundColor Cyan
Write-Host "Installation Path: $InstallPath" -ForegroundColor Green

# Function to check if command exists
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Function to check Python version
function Test-PythonVersion {
    try {
        $pythonVersion = python --version 2>&1
        if ($pythonVersion -match "Python (\d+)\.(\d+)") {
            $major = [int]$matches[1]
            $minor = [int]$matches[2]
            if ($major -eq 3 -and $minor -ge 9) {
                Write-Host "✓ Python $($matches[0]) detected" -ForegroundColor Green
                return $true
            }
        }
        Write-Host "✗ Python 3.9+ required, found: $pythonVersion" -ForegroundColor Red
        return $false
    } catch {
        Write-Host "✗ Python not found" -ForegroundColor Red
        return $false
    }
}

# Function to check Rust installation
function Test-RustInstallation {
    if (Test-Command "rustc" -and Test-Command "cargo") {
        $rustVersion = rustc --version
        Write-Host "✓ Rust detected: $rustVersion" -ForegroundColor Green
        return $true
    } else {
        Write-Host "✗ Rust not found" -ForegroundColor Red
        return $false
    }
}

# Function to check MetaTrader 5
function Test-MT5Installation {
    $mt5Paths = @(
        "${env:ProgramFiles}\MetaTrader 5\terminal64.exe",
        "${env:ProgramFiles(x86)}\MetaTrader 5\terminal64.exe",
        "${env:APPDATA}\MetaQuotes\Terminal\*\terminal64.exe"
    )
    
    foreach ($path in $mt5Paths) {
        if (Test-Path $path) {
            Write-Host "✓ MetaTrader 5 found at: $path" -ForegroundColor Green
            return $true
        }
    }
    
    Write-Host "⚠ MetaTrader 5 not found in standard locations" -ForegroundColor Yellow
    return $false
}

# Step 1: Check prerequisites
Write-Host "`n--- Checking Prerequisites ---" -ForegroundColor Yellow

$pythonOk = Test-PythonVersion
if (-not $pythonOk) {
    Write-Host "Please install Python 3.9+ from https://python.org" -ForegroundColor Red
    exit 1
}

if (-not $SkipRust) {
    $rustOk = Test-RustInstallation
    if (-not $rustOk) {
        Write-Host "Please install Rust from https://rustup.rs/" -ForegroundColor Red
        Write-Host "Or use -SkipRust flag to skip Rust components" -ForegroundColor Yellow
        exit 1
    }
}

if (-not $SkipMT5Check) {
    $mt5Ok = Test-MT5Installation
    if (-not $mt5Ok) {
        Write-Host "Please install MetaTrader 5 from https://www.metatrader5.com" -ForegroundColor Red
        Write-Host "Or use -SkipMT5Check flag to skip this check" -ForegroundColor Yellow
        exit 1
    }
}

# Step 2: Install Python dependencies
Write-Host "`n--- Installing Python Dependencies ---" -ForegroundColor Yellow

try {
    Write-Host "Upgrading pip..." -ForegroundColor Cyan
    python -m pip install --upgrade pip

    if ($Dev) {
        Write-Host "Installing development dependencies..." -ForegroundColor Cyan
        pip install -r requirements-dev.txt
    } else {
        Write-Host "Installing production dependencies..." -ForegroundColor Cyan
        pip install -r requirements.txt
    }
    
    Write-Host "✓ Python dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to install Python dependencies: $_" -ForegroundColor Red
    exit 1
}

# Step 3: Build Rust components
if (-not $SkipRust) {
    Write-Host "`n--- Building Rust Components ---" -ForegroundColor Yellow
    
    try {
        Push-Location "cluster_core"
        
        Write-Host "Building Rust clustering engine..." -ForegroundColor Cyan
        cargo build --release
        
        Write-Host "✓ Rust components built successfully" -ForegroundColor Green
        Pop-Location
    } catch {
        Write-Host "✗ Failed to build Rust components: $_" -ForegroundColor Red
        Pop-Location
        exit 1
    }
}

# Step 4: Create data directories
Write-Host "`n--- Setting Up Data Directories ---" -ForegroundColor Yellow

$dataDirs = @("data", "data/clustering", "data/state", "exports")
foreach ($dir in $dataDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ Created directory: $dir" -ForegroundColor Green
    }
}

# Step 5: Run tests
Write-Host "`n--- Running Installation Tests ---" -ForegroundColor Yellow

try {
    Write-Host "Testing clustering engine..." -ForegroundColor Cyan
    python test_clustering_engine.py
    
    Write-Host "Testing dashboard components..." -ForegroundColor Cyan
    python test_dashboard.py
    
    Write-Host "✓ Installation tests passed" -ForegroundColor Green
} catch {
    Write-Host "⚠ Some tests failed, but installation may still work" -ForegroundColor Yellow
    Write-Host "Error: $_" -ForegroundColor Red
}

# Step 6: Create shortcuts
Write-Host "`n--- Creating Shortcuts ---" -ForegroundColor Yellow

try {
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutPath = Join-Path $desktopPath "FX Clustering.lnk"
    
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut($shortcutPath)
    $Shortcut.TargetPath = "python"
    $Shortcut.Arguments = "run_clustering_app.py"
    $Shortcut.WorkingDirectory = $InstallPath
    $Shortcut.IconLocation = "python.exe,0"
    $Shortcut.Description = "Dynamic FX Clustering Application"
    $Shortcut.Save()
    
    Write-Host "✓ Desktop shortcut created" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not create desktop shortcut: $_" -ForegroundColor Yellow
}

# Installation complete
Write-Host "`n=== Installation Complete ===" -ForegroundColor Cyan
Write-Host "✓ Dynamic FX Clustering Application installed successfully" -ForegroundColor Green

Write-Host "`nNext Steps:" -ForegroundColor Yellow
Write-Host "1. Start MetaTrader 5 and log in to your account" -ForegroundColor White
Write-Host "2. Configure Expert Advisors (Tools → Options → Expert Advisors)" -ForegroundColor White
Write-Host "3. Run: python run_clustering_app.py" -ForegroundColor White
Write-Host "4. Open browser to: http://localhost:8050" -ForegroundColor White

Write-Host "`nDocumentation:" -ForegroundColor Yellow
Write-Host "- User Guide: docs/USER_GUIDE.md" -ForegroundColor White
Write-Host "- Installation Guide: docs/INSTALLATION.md" -ForegroundColor White
Write-Host "- Developer Guide: docs/DEVELOPER_GUIDE.md" -ForegroundColor White

if ($Dev) {
    Write-Host "`nDevelopment Tools Installed:" -ForegroundColor Yellow
    Write-Host "- Run tests: pytest" -ForegroundColor White
    Write-Host "- Code formatting: black ." -ForegroundColor White
    Write-Host "- Linting: flake8 clustering/" -ForegroundColor White
    Write-Host "- Type checking: mypy clustering/" -ForegroundColor White
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
