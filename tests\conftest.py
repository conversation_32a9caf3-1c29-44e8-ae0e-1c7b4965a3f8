"""
Pytest configuration and shared fixtures for Dynamic FX Clustering tests
"""

import pytest
import sys
import os
import tempfile
import shutil
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock
import numpy as np
import pandas as pd

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from clustering.clustering_engine import ClusteringEngine
from clustering.state_manager import StateManager
from clustering.data_manager import ClusteringDataManager
from clustering.advanced_analytics import AdvancedAnalytics
from clustering.alert_system import AlertSystem
from config import CURRENCY_PAIRS


@pytest.fixture(scope="session")
def test_symbols():
    """Subset of currency pairs for testing"""
    return CURRENCY_PAIRS[:10]


@pytest.fixture(scope="session")
def temp_data_dir():
    """Temporary directory for test data"""
    temp_dir = tempfile.mkdtemp(prefix="clustering_test_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def mock_mt5_data():
    """Mock MetaTrader 5 data for testing"""
    def generate_mock_data(symbol, timeframe, start_time, end_time, count=100):
        """Generate realistic mock OHLC data"""
        dates = pd.date_range(start=start_time, end=end_time, periods=count)
        base_price = 1.1000 if 'EUR' in symbol else 1.3000
        
        # Generate realistic price movements
        returns = np.random.normal(0, 0.001, count)
        prices = base_price * np.exp(np.cumsum(returns))
        
        data = []
        for i, date in enumerate(dates):
            high = prices[i] * (1 + abs(np.random.normal(0, 0.0005)))
            low = prices[i] * (1 - abs(np.random.normal(0, 0.0005)))
            open_price = prices[i-1] if i > 0 else prices[i]
            close_price = prices[i]
            
            data.append({
                'time': int(date.timestamp()),
                'open': round(open_price, 5),
                'high': round(high, 5),
                'low': round(low, 5),
                'close': round(close_price, 5),
                'tick_volume': np.random.randint(50, 200),
                'spread': np.random.randint(1, 5),
                'real_volume': 0
            })
        
        return np.array(data, dtype=[
            ('time', 'i8'), ('open', 'f8'), ('high', 'f8'), ('low', 'f8'),
            ('close', 'f8'), ('tick_volume', 'i8'), ('spread', 'i4'), ('real_volume', 'i8')
        ])
    
    return generate_mock_data


@pytest.fixture
def mock_clustering_engine(test_symbols, temp_data_dir):
    """Mock clustering engine for testing"""
    engine = ClusteringEngine(
        symbols=test_symbols,
        event_threshold=0.7,
        min_data_quality=0.8,
        persistence_dir=os.path.join(temp_data_dir, "clustering")
    )
    
    # Mock the data manager to avoid MT5 dependency
    engine.data_manager = Mock(spec=ClusteringDataManager)
    engine.data_manager.is_connected = Mock(return_value=True)
    engine.data_manager.get_symbols = Mock(return_value=test_symbols)
    
    return engine


@pytest.fixture
def mock_state_manager(temp_data_dir):
    """Mock state manager for testing"""
    state_manager = StateManager(
        persistence_dir=os.path.join(temp_data_dir, "state"),
        enable_advanced_analytics=True,
        enable_alerts=True
    )
    return state_manager


@pytest.fixture
def sample_clustering_result():
    """Sample clustering result for testing"""
    return {
        'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
        'cluster_centers': np.random.rand(3, 5),
        'silhouette_score': 0.75,
        'inertia': 0.25,
        'n_clusters': 3,
        'stability_score': 0.85,
        'quality_score': 0.90,
        'event_detected': True,
        'event_type': 'regime_change',
        'event_confidence': 0.80
    }


@pytest.fixture
def sample_volatility_data():
    """Sample volatility regime data for testing"""
    return {
        'regimes': [1, 2, 3, 1, 2, 3, 1, 2, 3, 1],
        'regime_probabilities': np.random.rand(10, 5),
        'regime_descriptions': {
            1: "Low volatility regime",
            2: "Medium volatility regime", 
            3: "High volatility regime",
            4: "Extreme volatility regime",
            5: "Crisis volatility regime"
        },
        'transition_matrix': np.random.rand(5, 5),
        'regime_statistics': {
            'mean_duration': [120, 90, 60, 30, 15],
            'volatility_levels': [0.01, 0.02, 0.04, 0.08, 0.16]
        }
    }


@pytest.fixture
def sample_market_data(test_symbols):
    """Sample market data for testing"""
    n_periods = 100
    n_symbols = len(test_symbols)
    
    # Generate correlated returns
    correlation_matrix = np.random.rand(n_symbols, n_symbols)
    correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2
    np.fill_diagonal(correlation_matrix, 1.0)
    
    returns = np.random.multivariate_normal(
        mean=np.zeros(n_symbols),
        cov=correlation_matrix * 0.001,
        size=n_periods
    )
    
    # Convert to price data
    base_prices = np.random.uniform(1.0, 2.0, n_symbols)
    prices = base_prices * np.exp(np.cumsum(returns, axis=0))
    
    return {
        'symbols': test_symbols,
        'prices': prices,
        'returns': returns,
        'correlation_matrix': correlation_matrix,
        'timestamps': pd.date_range(
            start=datetime.now() - timedelta(days=1),
            periods=n_periods,
            freq='5min'
        )
    }


@pytest.fixture
def performance_thresholds():
    """Performance thresholds for benchmarking"""
    return {
        'clustering_time_max': 2.0,  # seconds
        'data_fetch_time_max': 5.0,  # seconds
        'dashboard_render_time_max': 1.0,  # seconds
        'memory_usage_max': 500,  # MB
        'cpu_usage_max': 80,  # percent
        'min_silhouette_score': 0.3,
        'min_stability_score': 0.5
    }


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Setup test environment before each test"""
    # Set test-specific environment variables
    os.environ['CLUSTERING_TEST_MODE'] = '1'
    os.environ['MT5_MOCK_MODE'] = '1'
    
    yield
    
    # Cleanup after test
    os.environ.pop('CLUSTERING_TEST_MODE', None)
    os.environ.pop('MT5_MOCK_MODE', None)


# Test markers
def pytest_configure(config):
    """Configure custom pytest markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "performance: mark test as a performance benchmark"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
