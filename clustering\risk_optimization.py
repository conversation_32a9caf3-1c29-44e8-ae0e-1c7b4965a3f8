"""
Advanced Risk Optimization Engine for Portfolio Construction

This module provides sophisticated risk optimization algorithms using
Modern Portfolio Theory (MPT) principles with clustering insights.

Features:
- Efficient frontier calculation
- Risk parity optimization
- Maximum Sharpe ratio optimization
- Cluster-constrained optimization
- Risk budgeting
- Black-Litterman integration
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, NamedTuple
from dataclasses import dataclass
from scipy.optimize import minimize, LinearConstraint
from scipy.stats import norm
import logging

logger = logging.getLogger(__name__)


@dataclass
class OptimizationResult:
    """Results from portfolio optimization"""
    weights: Dict[str, float]
    expected_return: float
    expected_risk: float
    sharpe_ratio: float
    optimization_method: str
    success: bool
    message: str


@dataclass
class EfficientFrontierPoint:
    """Point on the efficient frontier"""
    risk: float
    return_: float
    weights: Dict[str, float]
    sharpe_ratio: float


@dataclass
class RiskBudget:
    """Risk budget allocation"""
    symbol: str
    risk_contribution: float
    risk_percentage: float
    weight: float


class RiskOptimizer:
    """
    Advanced Risk Optimization Engine
    
    Implements various portfolio optimization techniques with clustering constraints
    """
    
    def __init__(self,
                 symbols: List[str],
                 risk_free_rate: float = 0.02,
                 max_position_size: float = 1.0,
                 min_position_size: float = -1.0):
        """
        Initialize risk optimizer
        
        Args:
            symbols: List of asset symbols
            risk_free_rate: Risk-free rate for Sharpe ratio calculation
            max_position_size: Maximum weight for any single position
            min_position_size: Minimum weight for any position
        """
        self.symbols = symbols
        self.risk_free_rate = risk_free_rate
        self.max_position_size = max_position_size
        self.min_position_size = min_position_size
        self.n_assets = len(symbols)

    def _absolute_sum_constraint(self, weights: np.ndarray) -> float:
        """Constraint function for absolute sum = 1"""
        return np.sum(np.abs(weights)) - 1.0

    def _normalize_weights_absolute(self, weights: np.ndarray) -> np.ndarray:
        """Normalize weights so absolute sum = 1"""
        abs_sum = np.sum(np.abs(weights))
        if abs_sum <= 1e-15:
            return np.ones(len(weights)) / len(weights)
        return weights / abs_sum
    
    def calculate_covariance_matrix(self, 
                                  correlation_matrix: np.ndarray,
                                  volatility_profiles: Dict[str, float]) -> np.ndarray:
        """
        Calculate covariance matrix from correlation matrix and volatilities
        
        Args:
            correlation_matrix: Correlation matrix
            volatility_profiles: Volatility for each symbol
            
        Returns:
            Covariance matrix
        """
        try:
            # Get volatilities in symbol order
            volatilities = np.array([volatility_profiles.get(symbol, 0.01) for symbol in self.symbols])
            
            # Create covariance matrix: Σ = D * R * D
            # where D is diagonal matrix of volatilities, R is correlation matrix
            vol_matrix = np.outer(volatilities, volatilities)
            covariance_matrix = correlation_matrix * vol_matrix
            
            return covariance_matrix
            
        except Exception as e:
            logger.error(f"Error calculating covariance matrix: {str(e)}")
            # Return identity matrix as fallback
            return np.eye(self.n_assets) * 0.01
    
    def optimize_minimum_variance(self, 
                                covariance_matrix: np.ndarray,
                                cluster_constraints: Optional[Dict[int, Tuple[float, float]]] = None,
                                cluster_assignments: Optional[List[int]] = None) -> OptimizationResult:
        """
        Optimize for minimum variance portfolio
        
        Args:
            covariance_matrix: Asset covariance matrix
            cluster_constraints: Optional constraints on cluster allocation (cluster_id: (min, max))
            cluster_assignments: Cluster assignment for each asset
            
        Returns:
            Optimization result
        """
        try:
            # Objective function: minimize portfolio variance
            def objective(weights):
                return np.dot(weights.T, np.dot(covariance_matrix, weights))
            
            # Constraints - use absolute sum = 1
            constraints = [
                {'type': 'eq', 'fun': self._absolute_sum_constraint}  # Absolute sum of weights = 1
            ]
            
            # Add cluster constraints if provided
            if cluster_constraints and cluster_assignments:
                for cluster_id, (min_weight, max_weight) in cluster_constraints.items():
                    cluster_indices = [i for i, c in enumerate(cluster_assignments) if c == cluster_id]
                    if cluster_indices:
                        # Constraint for cluster weight sum
                        def cluster_constraint(w, indices=cluster_indices, min_w=min_weight, max_w=max_weight):
                            cluster_weight = sum(w[i] for i in indices)
                            return [cluster_weight - min_w, max_w - cluster_weight]
                        
                        constraints.append({
                            'type': 'ineq', 
                            'fun': lambda w, indices=cluster_indices, min_w=min_weight, max_w=max_weight: 
                                   [sum(w[i] for i in indices) - min_w, max_w - sum(w[i] for i in indices)]
                        })
            
            # Bounds for individual weights
            bounds = [(self.min_position_size, self.max_position_size) for _ in range(self.n_assets)]
            
            # Initial guess (equal weights with absolute sum = 1)
            initial_weights = np.array([1.0 / self.n_assets] * self.n_assets)
            
            # Optimize
            result = minimize(
                objective,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )
            
            if result.success:
                # Normalize weights to ensure absolute sum = 1
                normalized_weights = self._normalize_weights_absolute(result.x)
                weights_dict = dict(zip(self.symbols, normalized_weights))
                portfolio_risk = np.sqrt(result.fun)
                
                return OptimizationResult(
                    weights=weights_dict,
                    expected_return=0.0,  # Not optimizing for return
                    expected_risk=portfolio_risk,
                    sharpe_ratio=0.0,
                    optimization_method="minimum_variance",
                    success=True,
                    message="Optimization successful"
                )
            else:
                logger.warning(f"Minimum variance optimization failed: {result.message}")
                # Return equal weights as fallback
                equal_weight = 1.0 / self.n_assets
                weights_dict = {symbol: equal_weight for symbol in self.symbols}
                
                return OptimizationResult(
                    weights=weights_dict,
                    expected_return=0.0,
                    expected_risk=0.0,
                    sharpe_ratio=0.0,
                    optimization_method="equal_weights_fallback",
                    success=False,
                    message=f"Optimization failed: {result.message}"
                )
                
        except Exception as e:
            logger.error(f"Error in minimum variance optimization: {str(e)}")
            equal_weight = 1.0 / self.n_assets
            weights_dict = {symbol: equal_weight for symbol in self.symbols}
            
            return OptimizationResult(
                weights=weights_dict,
                expected_return=0.0,
                expected_risk=0.0,
                sharpe_ratio=0.0,
                optimization_method="error_fallback",
                success=False,
                message=f"Error: {str(e)}"
            )
    
    def optimize_maximum_sharpe(self, 
                              covariance_matrix: np.ndarray,
                              expected_returns: np.ndarray,
                              cluster_constraints: Optional[Dict[int, Tuple[float, float]]] = None,
                              cluster_assignments: Optional[List[int]] = None) -> OptimizationResult:
        """
        Optimize for maximum Sharpe ratio portfolio
        
        Args:
            covariance_matrix: Asset covariance matrix
            expected_returns: Expected returns for each asset
            cluster_constraints: Optional constraints on cluster allocation
            cluster_assignments: Cluster assignment for each asset
            
        Returns:
            Optimization result
        """
        try:
            # Objective function: maximize Sharpe ratio (minimize negative Sharpe)
            def objective(weights):
                portfolio_return = np.dot(weights, expected_returns)
                portfolio_risk = np.sqrt(np.dot(weights.T, np.dot(covariance_matrix, weights)))
                
                if portfolio_risk == 0:
                    return -np.inf
                
                sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk
                return -sharpe_ratio  # Minimize negative Sharpe
            
            # Constraints - use absolute sum = 1
            constraints = [
                {'type': 'eq', 'fun': self._absolute_sum_constraint}  # Absolute sum of weights = 1
            ]
            
            # Add cluster constraints if provided
            if cluster_constraints and cluster_assignments:
                for cluster_id, (min_weight, max_weight) in cluster_constraints.items():
                    cluster_indices = [i for i, c in enumerate(cluster_assignments) if c == cluster_id]
                    if cluster_indices:
                        constraints.append({
                            'type': 'ineq', 
                            'fun': lambda w, indices=cluster_indices, min_w=min_weight, max_w=max_weight: 
                                   [sum(w[i] for i in indices) - min_w, max_w - sum(w[i] for i in indices)]
                        })
            
            # Bounds
            bounds = [(self.min_position_size, self.max_position_size) for _ in range(self.n_assets)]
            
            # Initial guess (equal weights)
            initial_weights = np.array([1.0 / self.n_assets] * self.n_assets)
            
            # Optimize
            result = minimize(
                objective,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )
            
            if result.success:
                # Normalize weights to ensure absolute sum = 1
                normalized_weights = self._normalize_weights_absolute(result.x)
                weights_dict = dict(zip(self.symbols, normalized_weights))
                portfolio_return = np.dot(normalized_weights, expected_returns)
                portfolio_risk = np.sqrt(np.dot(normalized_weights.T, np.dot(covariance_matrix, normalized_weights)))
                sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk if portfolio_risk > 0 else 0
                
                return OptimizationResult(
                    weights=weights_dict,
                    expected_return=portfolio_return,
                    expected_risk=portfolio_risk,
                    sharpe_ratio=sharpe_ratio,
                    optimization_method="maximum_sharpe",
                    success=True,
                    message="Optimization successful"
                )
            else:
                logger.warning(f"Maximum Sharpe optimization failed: {result.message}")
                equal_weight = 1.0 / self.n_assets
                weights_dict = {symbol: equal_weight for symbol in self.symbols}
                
                return OptimizationResult(
                    weights=weights_dict,
                    expected_return=0.0,
                    expected_risk=0.0,
                    sharpe_ratio=0.0,
                    optimization_method="equal_weights_fallback",
                    success=False,
                    message=f"Optimization failed: {result.message}"
                )
                
        except Exception as e:
            logger.error(f"Error in maximum Sharpe optimization: {str(e)}")
            equal_weight = 1.0 / self.n_assets
            weights_dict = {symbol: equal_weight for symbol in self.symbols}
            
            return OptimizationResult(
                weights=weights_dict,
                expected_return=0.0,
                expected_risk=0.0,
                sharpe_ratio=0.0,
                optimization_method="error_fallback",
                success=False,
                message=f"Error: {str(e)}"
            )

    def optimize_risk_parity(self,
                           covariance_matrix: np.ndarray,
                           target_risk_contributions: Optional[Dict[str, float]] = None) -> OptimizationResult:
        """
        Optimize for risk parity (equal risk contribution) portfolio

        Args:
            covariance_matrix: Asset covariance matrix
            target_risk_contributions: Optional target risk contributions for each asset

        Returns:
            Optimization result
        """
        try:
            # If no target specified, use equal risk contribution
            if target_risk_contributions is None:
                target_risk_contributions = {symbol: 1.0/self.n_assets for symbol in self.symbols}

            # Convert to array in symbol order
            target_rc = np.array([target_risk_contributions.get(symbol, 1.0/self.n_assets)
                                for symbol in self.symbols])

            # Objective function: minimize sum of squared deviations from target risk contributions
            def objective(weights):
                portfolio_variance = np.dot(weights.T, np.dot(covariance_matrix, weights))

                if portfolio_variance <= 0:
                    return 1e10

                # Calculate risk contributions
                marginal_risk = np.dot(covariance_matrix, weights)
                risk_contributions = weights * marginal_risk / portfolio_variance

                # Sum of squared deviations from target
                deviations = risk_contributions - target_rc
                return np.sum(deviations ** 2)

            # Constraints - use absolute sum = 1
            constraints = [
                {'type': 'eq', 'fun': self._absolute_sum_constraint}  # Absolute sum of weights = 1
            ]

            # Bounds
            bounds = [(self.min_position_size, self.max_position_size) for _ in range(self.n_assets)]

            # Initial guess (inverse volatility weights)
            volatilities = np.sqrt(np.diag(covariance_matrix))
            inv_vol_weights = (1.0 / volatilities) / np.sum(1.0 / volatilities)

            # Optimize
            result = minimize(
                objective,
                inv_vol_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )

            if result.success:
                # Normalize weights to ensure absolute sum = 1
                normalized_weights = self._normalize_weights_absolute(result.x)
                weights_dict = dict(zip(self.symbols, normalized_weights))
                portfolio_risk = np.sqrt(np.dot(normalized_weights.T, np.dot(covariance_matrix, normalized_weights)))

                return OptimizationResult(
                    weights=weights_dict,
                    expected_return=0.0,  # Not optimizing for return
                    expected_risk=portfolio_risk,
                    sharpe_ratio=0.0,
                    optimization_method="risk_parity",
                    success=True,
                    message="Risk parity optimization successful"
                )
            else:
                logger.warning(f"Risk parity optimization failed: {result.message}")
                # Return inverse volatility weights as fallback
                weights_dict = dict(zip(self.symbols, inv_vol_weights))

                return OptimizationResult(
                    weights=weights_dict,
                    expected_return=0.0,
                    expected_risk=0.0,
                    sharpe_ratio=0.0,
                    optimization_method="inverse_volatility_fallback",
                    success=False,
                    message=f"Optimization failed: {result.message}"
                )

        except Exception as e:
            logger.error(f"Error in risk parity optimization: {str(e)}")
            equal_weight = 1.0 / self.n_assets
            weights_dict = {symbol: equal_weight for symbol in self.symbols}

            return OptimizationResult(
                weights=weights_dict,
                expected_return=0.0,
                expected_risk=0.0,
                sharpe_ratio=0.0,
                optimization_method="error_fallback",
                success=False,
                message=f"Error: {str(e)}"
            )

    def calculate_efficient_frontier(self,
                                   covariance_matrix: np.ndarray,
                                   expected_returns: np.ndarray,
                                   n_points: int = 20) -> List[EfficientFrontierPoint]:
        """
        Calculate efficient frontier points

        Args:
            covariance_matrix: Asset covariance matrix
            expected_returns: Expected returns for each asset
            n_points: Number of points to calculate on the frontier

        Returns:
            List of efficient frontier points
        """
        try:
            frontier_points = []

            # Calculate minimum and maximum return bounds
            min_var_result = self.optimize_minimum_variance(covariance_matrix)
            if min_var_result.success:
                min_return = np.dot(list(min_var_result.weights.values()), expected_returns)
            else:
                min_return = np.min(expected_returns)

            max_return = np.max(expected_returns)

            # Generate target returns
            target_returns = np.linspace(min_return, max_return, n_points)

            for target_return in target_returns:
                # Optimize for minimum variance given target return
                def objective(weights):
                    return np.dot(weights.T, np.dot(covariance_matrix, weights))

                constraints = [
                    {'type': 'eq', 'fun': self._absolute_sum_constraint},  # Absolute sum of weights = 1
                    {'type': 'eq', 'fun': lambda w: np.dot(w, expected_returns) - target_return}  # Target return
                ]

                bounds = [(self.min_position_size, self.max_position_size) for _ in range(self.n_assets)]
                initial_weights = np.array([1.0 / self.n_assets] * self.n_assets)

                result = minimize(
                    objective,
                    initial_weights,
                    method='SLSQP',
                    bounds=bounds,
                    constraints=constraints,
                    options={'maxiter': 1000}
                )

                if result.success:
                    # Normalize weights to ensure absolute sum = 1
                    normalized_weights = self._normalize_weights_absolute(result.x)
                    weights_dict = dict(zip(self.symbols, normalized_weights))
                    portfolio_risk = np.sqrt(result.fun)
                    portfolio_return = np.dot(normalized_weights, expected_returns)
                    sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk if portfolio_risk > 0 else 0

                    frontier_points.append(EfficientFrontierPoint(
                        risk=portfolio_risk,
                        return_=portfolio_return,
                        weights=weights_dict,
                        sharpe_ratio=sharpe_ratio
                    ))

            return frontier_points

        except Exception as e:
            logger.error(f"Error calculating efficient frontier: {str(e)}")
            return []

    def calculate_risk_budgets(self,
                             weights: Dict[str, float],
                             covariance_matrix: np.ndarray) -> List[RiskBudget]:
        """
        Calculate risk budgets for a given portfolio

        Args:
            weights: Portfolio weights
            covariance_matrix: Asset covariance matrix

        Returns:
            List of risk budgets for each asset
        """
        try:
            # Convert weights to array
            weight_array = np.array([weights.get(symbol, 0.0) for symbol in self.symbols])

            # Calculate portfolio variance
            portfolio_variance = np.dot(weight_array.T, np.dot(covariance_matrix, weight_array))

            if portfolio_variance <= 0:
                return []

            # Calculate marginal risk contributions
            marginal_risk = np.dot(covariance_matrix, weight_array)

            # Calculate risk contributions
            risk_contributions = weight_array * marginal_risk

            # Create risk budget objects
            risk_budgets = []
            for i, symbol in enumerate(self.symbols):
                risk_contribution = risk_contributions[i]
                risk_percentage = risk_contribution / portfolio_variance if portfolio_variance > 0 else 0

                risk_budgets.append(RiskBudget(
                    symbol=symbol,
                    risk_contribution=risk_contribution,
                    risk_percentage=risk_percentage,
                    weight=weight_array[i]
                ))

            return risk_budgets

        except Exception as e:
            logger.error(f"Error calculating risk budgets: {str(e)}")
            return []

    def optimize_cluster_constrained(self,
                                   covariance_matrix: np.ndarray,
                                   cluster_assignments: List[int],
                                   cluster_weights: Dict[int, float],
                                   optimization_type: str = "minimum_variance",
                                   expected_returns: Optional[np.ndarray] = None) -> OptimizationResult:
        """
        Optimize portfolio with cluster weight constraints

        Args:
            covariance_matrix: Asset covariance matrix
            cluster_assignments: Cluster assignment for each asset
            cluster_weights: Target weight for each cluster
            optimization_type: Type of optimization ("minimum_variance" or "maximum_sharpe")
            expected_returns: Expected returns (required for maximum_sharpe)

        Returns:
            Optimization result
        """
        try:
            # Validate cluster weights sum to 1
            total_cluster_weight = sum(cluster_weights.values())
            if abs(total_cluster_weight - 1.0) > 1e-6:
                logger.warning(f"Cluster weights sum to {total_cluster_weight}, normalizing to 1.0")
                cluster_weights = {k: v/total_cluster_weight for k, v in cluster_weights.items()}

            # Define objective function based on optimization type
            if optimization_type == "minimum_variance":
                def objective(weights):
                    return np.dot(weights.T, np.dot(covariance_matrix, weights))
            elif optimization_type == "maximum_sharpe":
                if expected_returns is None:
                    raise ValueError("Expected returns required for maximum Sharpe optimization")
                def objective(weights):
                    portfolio_return = np.dot(weights, expected_returns)
                    portfolio_risk = np.sqrt(np.dot(weights.T, np.dot(covariance_matrix, weights)))
                    if portfolio_risk == 0:
                        return -np.inf
                    sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk
                    return -sharpe_ratio  # Minimize negative Sharpe
            else:
                raise ValueError(f"Unknown optimization type: {optimization_type}")

            # Constraints - use absolute sum = 1
            constraints = [
                {'type': 'eq', 'fun': self._absolute_sum_constraint}  # Absolute sum of weights = 1
            ]

            # Add cluster weight constraints
            for cluster_id, target_weight in cluster_weights.items():
                cluster_indices = [i for i, c in enumerate(cluster_assignments) if c == cluster_id]
                if cluster_indices:
                    def cluster_constraint(w, indices=cluster_indices, target=target_weight):
                        return sum(w[i] for i in indices) - target

                    constraints.append({
                        'type': 'eq',
                        'fun': cluster_constraint
                    })

            # Bounds
            bounds = [(self.min_position_size, self.max_position_size) for _ in range(self.n_assets)]

            # Initial guess based on cluster weights
            initial_weights = np.zeros(self.n_assets)
            for cluster_id, target_weight in cluster_weights.items():
                cluster_indices = [i for i, c in enumerate(cluster_assignments) if c == cluster_id]
                if cluster_indices:
                    weight_per_asset = target_weight / len(cluster_indices)
                    for idx in cluster_indices:
                        initial_weights[idx] = weight_per_asset

            # Optimize
            result = minimize(
                objective,
                initial_weights,
                method='SLSQP',
                bounds=bounds,
                constraints=constraints,
                options={'maxiter': 1000, 'ftol': 1e-9}
            )

            if result.success:
                # Normalize weights to ensure absolute sum = 1
                normalized_weights = self._normalize_weights_absolute(result.x)
                weights_dict = dict(zip(self.symbols, normalized_weights))
                portfolio_risk = np.sqrt(np.dot(normalized_weights.T, np.dot(covariance_matrix, normalized_weights)))

                if optimization_type == "maximum_sharpe" and expected_returns is not None:
                    portfolio_return = np.dot(normalized_weights, expected_returns)
                    sharpe_ratio = (portfolio_return - self.risk_free_rate) / portfolio_risk if portfolio_risk > 0 else 0
                else:
                    portfolio_return = 0.0
                    sharpe_ratio = 0.0

                return OptimizationResult(
                    weights=weights_dict,
                    expected_return=portfolio_return,
                    expected_risk=portfolio_risk,
                    sharpe_ratio=sharpe_ratio,
                    optimization_method=f"cluster_constrained_{optimization_type}",
                    success=True,
                    message="Cluster-constrained optimization successful"
                )
            else:
                logger.warning(f"Cluster-constrained optimization failed: {result.message}")
                # Return initial weights as fallback
                weights_dict = dict(zip(self.symbols, initial_weights))

                return OptimizationResult(
                    weights=weights_dict,
                    expected_return=0.0,
                    expected_risk=0.0,
                    sharpe_ratio=0.0,
                    optimization_method="cluster_weights_fallback",
                    success=False,
                    message=f"Optimization failed: {result.message}"
                )

        except Exception as e:
            logger.error(f"Error in cluster-constrained optimization: {str(e)}")
            equal_weight = 1.0 / self.n_assets
            weights_dict = {symbol: equal_weight for symbol in self.symbols}

            return OptimizationResult(
                weights=weights_dict,
                expected_return=0.0,
                expected_risk=0.0,
                sharpe_ratio=0.0,
                optimization_method="error_fallback",
                success=False,
                message=f"Error: {str(e)}"
            )
