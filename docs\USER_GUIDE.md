# Dynamic FX Clustering Application - User Guide

## Table of Contents
1. [Overview](#overview)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Getting Started](#getting-started)
5. [Dashboard Usage](#dashboard-usage)
6. [Advanced Features](#advanced-features)
7. [Troubleshooting](#troubleshooting)
8. [FAQ](#faq)

## Overview

The Dynamic FX Clustering Application is a real-time forex market analysis tool that combines:
- **Dynamic Correlation Clustering**: Groups currency pairs based on real-time correlation patterns
- **Volatility Regime Detection**: Identifies market volatility states and transitions
- **Advanced Analytics**: Provides clustering quality metrics and market insights
- **Real-time Alerts**: Monitors market events and regime changes

### Key Features
- Real-time data from MetaTrader 5
- Interactive dashboard with multiple visualization tabs
- 7-cluster correlation analysis
- 5-regime volatility classification
- Advanced analytics and performance metrics
- Export capabilities for data and charts

## Installation

### Prerequisites
- Windows 10/11 (required for MetaTrader 5)
- Python 3.9 or higher
- MetaTrader 5 terminal installed and configured
- Rust compiler (for building clustering engine)

### Step 1: Install Python Dependencies
```powershell
# Navigate to project directory
cd C:\Users\<USER>\Desktop\Clustering

# Install required packages
pip install dash plotly pandas numpy scipy scikit-learn
pip install MetaTrader5 pytz dash-bootstrap-components
pip install psutil memory-profiler pytest
```

### Step 2: Build Rust Clustering Engine
```powershell
# Navigate to Rust project
cd cluster_core

# Build the Rust library
cargo build --release

# Return to main directory
cd ..
```

### Step 3: Configure MetaTrader 5
1. Open MetaTrader 5 terminal
2. Go to Tools → Options → Expert Advisors
3. Enable "Allow automated trading"
4. Enable "Allow DLL imports"
5. Ensure your account has access to required currency pairs

### Step 4: Verify Installation
```powershell
# Run test to verify installation
python test_clustering_engine.py
```

## Configuration

### Basic Configuration
Edit `config.py` to customize settings:

```python
# Currency pairs to monitor (default: 28 major pairs)
CURRENCY_PAIRS = [
    'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD',
    'AUDUSD', 'NZDUSD', 'EURJPY', 'EURGBP', 'EURCHF',
    # ... add or remove pairs as needed
]

# Timezone settings
MARKET_TIMEZONE = 'Europe/Bucharest'  # Adjust to your timezone

# Performance settings
UPDATE_INTERVAL = 30  # seconds between updates
DATA_HISTORY_HOURS = 24  # hours of historical data
```

### Advanced Configuration
For advanced users, additional settings are available in the clustering engine:

```python
# In your startup script
engine = ClusteringEngine(
    symbols=CURRENCY_PAIRS,
    event_threshold=0.8,      # Sensitivity for event detection
    min_data_quality=0.7,     # Minimum data quality threshold
    persistence_dir="data"    # Data storage directory
)
```

## Getting Started

### Step 1: Start the Application
```powershell
# Start the dashboard
python run_clustering_app.py
```

### Step 2: Access the Dashboard
1. Open your web browser
2. Navigate to `http://localhost:8050`
3. Wait for initial data loading (may take 30-60 seconds)

### Step 3: Verify Connection
- Check the connection status indicator in the top-right corner
- Ensure "MT5 Connected" shows green status
- Verify that clustering data is updating

## Dashboard Usage

### Main Tabs

#### 1. Clustering Analysis Tab
- **Dendrogram**: Shows hierarchical clustering of currency pairs
- **Sankey Diagram**: Displays cluster transitions over time
- **Statistics Panel**: Current clustering metrics and quality scores
- **Event Log**: Real-time market events and regime changes

**How to Use:**
- Click on dendrogram branches to explore cluster details
- Hover over Sankey flows to see transition information
- Monitor statistics panel for clustering quality
- Review event log for significant market changes

#### 2. Volatility Regimes Tab
- **Calendar View**: Daily volatility regime classification
- **Transitions Chart**: Intraday regime changes
- **Regime Explanations**: Detailed descriptions of each regime

**Regime Types:**
1. **Low Volatility**: Stable market conditions
2. **Medium Volatility**: Normal market activity
3. **High Volatility**: Increased market stress
4. **Extreme Volatility**: Significant market disruption
5. **Crisis Volatility**: Market crisis conditions

#### 3. Advanced Analytics Tab
- **Clustering Quality Metrics**: Silhouette scores, stability measures
- **Market Regime Classification**: Current market state analysis
- **Real-time Alerts**: System alerts and notifications
- **Performance Trends**: Historical performance metrics

### Interactive Features

#### Cluster Selection
- Click on dendrogram branches to select specific clusters
- Selected cluster details appear in the statistics panel
- Use cluster information for trading decisions

#### Time Range Selection
- Use date pickers to analyze historical periods
- Compare clustering patterns across different timeframes
- Export historical data for further analysis

#### Real-time Updates
- Dashboard updates automatically every 30 seconds
- Manual refresh available via browser refresh
- Connection status monitored continuously

## Advanced Features

### Export Functionality
Access export features in the Advanced Analytics tab:

1. **Data Export**:
   - CSV format for clustering results
   - JSON format for complete state data
   - Excel format for comprehensive reports

2. **Chart Export**:
   - PNG images for presentations
   - HTML files for interactive sharing
   - PDF reports for documentation

### Alert System
Configure alerts for:
- Significant clustering changes
- Volatility regime transitions
- Data quality issues
- System performance problems

### Performance Monitoring
Monitor system performance:
- Clustering computation time
- Data fetch latency
- Memory usage
- CPU utilization

## Troubleshooting

### Common Issues

#### 1. MetaTrader 5 Connection Failed
**Symptoms**: "MT5 Disconnected" status, no data updates
**Solutions**:
- Ensure MetaTrader 5 is running and logged in
- Check Expert Advisors settings (allow automated trading)
- Verify account has access to required currency pairs
- Restart MetaTrader 5 and the application

#### 2. Empty Charts or No Data
**Symptoms**: Blank charts, "No data available" messages
**Solutions**:
- Wait for initial data collection (1-2 minutes)
- Check internet connection
- Verify currency pairs are available in your MT5 account
- Check weekend/market hours (limited data during market close)

#### 3. Slow Performance
**Symptoms**: Delayed updates, high CPU usage
**Solutions**:
- Reduce number of monitored currency pairs
- Increase update interval in configuration
- Close other resource-intensive applications
- Check system requirements

#### 4. Clustering Errors
**Symptoms**: Error messages in event log, failed clustering
**Solutions**:
- Check data quality (minimum 100 data points required)
- Verify Rust clustering engine is properly built
- Review error logs for specific issues
- Restart the application

### Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| E001 | MT5 Connection Failed | Check MetaTrader 5 setup |
| E002 | Insufficient Data | Wait for more data collection |
| E003 | Clustering Failed | Check Rust engine build |
| E004 | Data Quality Low | Verify data sources |
| E005 | Memory Error | Reduce dataset size |

### Log Files
Check log files for detailed error information:
- `alerts.log`: Alert system logs
- Application console: Real-time debug information

## FAQ

### General Questions

**Q: How often does the data update?**
A: By default, every 30 seconds. This can be configured in `config.py`.

**Q: Can I use this with other trading platforms?**
A: Currently only MetaTrader 5 is supported. Other platforms may be added in future versions.

**Q: How much historical data is needed?**
A: Minimum 24 hours for reliable clustering. More data improves accuracy.

### Technical Questions

**Q: Why do I see different cluster counts?**
A: The algorithm automatically determines optimal cluster count (typically 3-7 clusters) based on data patterns.

**Q: What do the volatility regimes mean?**
A: Regimes represent different market volatility states, from calm (1) to crisis (5) conditions.

**Q: Can I export the clustering results?**
A: Yes, use the export functionality in the Advanced Analytics tab.

### Performance Questions

**Q: How much memory does the application use?**
A: Typically 200-500MB depending on data size and number of currency pairs.

**Q: Can I run this on multiple timeframes?**
A: Currently optimized for 5-minute data. Other timeframes require configuration changes.

**Q: Is real-time analysis resource intensive?**
A: Moderate resource usage. Performance depends on number of monitored pairs and update frequency.

### Support

For additional support:
1. Check the troubleshooting section above
2. Review error logs for specific issues
3. Consult the developer documentation
4. Contact technical support with log files and error descriptions

## Quick Start Checklist

### For New Users
- [ ] Install Python 3.9+ and required packages
- [ ] Build Rust clustering engine
- [ ] Configure MetaTrader 5 with automated trading enabled
- [ ] Run `python run_clustering_app.py`
- [ ] Open browser to `http://localhost:8050`
- [ ] Verify MT5 connection status
- [ ] Wait for initial data collection (1-2 minutes)
- [ ] Explore clustering analysis tab
- [ ] Check volatility regimes tab
- [ ] Review advanced analytics features

### Daily Usage
- [ ] Start MetaTrader 5 and log in
- [ ] Launch the clustering application
- [ ] Monitor connection status
- [ ] Review overnight regime changes
- [ ] Check for significant clustering events
- [ ] Export data if needed for analysis

---

*Last updated: July 2025*
*Version: 1.0.0*
