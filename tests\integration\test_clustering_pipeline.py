"""
Integration tests for the complete clustering pipeline
"""

import pytest
import numpy as np
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from clustering.clustering_engine import ClusteringEngine
from clustering.state_manager import StateManager
from clustering.data_manager import ClusteringDataManager


@pytest.mark.integration
class TestClusteringPipeline:
    """Test cases for complete clustering pipeline integration"""
    
    def test_full_clustering_workflow(self, test_symbols, temp_data_dir, mock_mt5_data):
        """Test complete clustering workflow from data fetch to state update"""
        # Initialize components
        engine = ClusteringEngine(
            symbols=test_symbols,
            persistence_dir=temp_data_dir
        )
        
        # Mock data manager with realistic data
        with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
            mock_fetch.return_value = mock_mt5_data(
                'EURUSD', 'M5', 
                datetime.now() - timedelta(hours=24),
                datetime.now(),
                count=288  # 24 hours of 5-minute data
            )
            
            # Mock Rust clustering
            with patch('clustering.clustering_engine.cluster_core') as mock_cluster:
                mock_cluster.cluster_currencies.return_value = {
                    'cluster_assignments': [0, 1, 2, 0, 1, 2, 0, 1, 2, 0],
                    'silhouette_score': 0.75,
                    'inertia': 0.25,
                    'n_clusters': 3
                }
                
                # Run clustering analysis
                result = engine.run_clustering_analysis()
                
                # Verify results
                assert result is not None
                assert 'cluster_assignments' in result
                assert 'silhouette_score' in result
                assert result['silhouette_score'] == 0.75
                
                # Verify state was updated
                current_state = engine.get_current_state()
                assert current_state is not None
                assert current_state['silhouette_score'] == 0.75
    
    def test_data_manager_clustering_engine_integration(self, test_symbols, temp_data_dir):
        """Test integration between data manager and clustering engine"""
        engine = ClusteringEngine(
            symbols=test_symbols,
            persistence_dir=temp_data_dir
        )
        
        # Test connection
        with patch.object(engine.data_manager, 'connect') as mock_connect:
            mock_connect.return_value = True
            
            connected = engine.connect_to_data_sources()
            assert connected is True
            
            # Test symbol retrieval
            with patch.object(engine.data_manager, 'get_symbols') as mock_get_symbols:
                mock_get_symbols.return_value = test_symbols
                
                symbols = engine.get_symbols()
                assert symbols == test_symbols
    
    def test_state_manager_persistence_integration(self, temp_data_dir, sample_clustering_result):
        """Test integration between state manager and persistence layer"""
        # Create state manager
        state_manager = StateManager(persistence_dir=temp_data_dir)
        
        # Update state
        state_manager.update_state(sample_clustering_result)
        
        # Verify state is persisted
        assert state_manager.current_state == sample_clustering_result
        
        # Create new state manager instance (simulating restart)
        new_state_manager = StateManager(persistence_dir=temp_data_dir)
        
        # Verify state is loaded from persistence
        loaded_state = new_state_manager.get_current_state()
        assert loaded_state is not None
        assert loaded_state['silhouette_score'] == sample_clustering_result['silhouette_score']
    
    def test_advanced_analytics_integration(self, temp_data_dir, sample_clustering_result):
        """Test integration with advanced analytics system"""
        state_manager = StateManager(
            persistence_dir=temp_data_dir,
            enable_advanced_analytics=True
        )
        
        # Add multiple states for analytics
        for i in range(5):
            result = sample_clustering_result.copy()
            result['silhouette_score'] = 0.7 + i * 0.05
            result['timestamp'] = datetime.now() - timedelta(hours=i)
            state_manager.update_state(result)
        
        # Get analytics summary
        summary = state_manager.get_advanced_analytics_summary()
        
        assert summary is not None
        assert 'avg_silhouette_score' in summary
        assert 'total_measurements' in summary
        assert summary['total_measurements'] == 5
    
    def test_alert_system_integration(self, temp_data_dir, sample_clustering_result):
        """Test integration with alert system"""
        state_manager = StateManager(
            persistence_dir=temp_data_dir,
            enable_alerts=True
        )
        
        # Trigger alert by updating with significant change
        previous_result = sample_clustering_result.copy()
        previous_result['silhouette_score'] = 0.3  # Low quality
        state_manager.update_state(previous_result)
        
        # Update with high quality (should trigger alert)
        new_result = sample_clustering_result.copy()
        new_result['silhouette_score'] = 0.9  # High quality
        state_manager.update_state(new_result)
        
        # Check for alerts
        alerts = state_manager.get_alert_history(hours_back=1)
        assert alerts is not None
        assert len(alerts) >= 0  # May or may not have alerts depending on rules
    
    def test_volatility_regime_integration(self, temp_data_dir, test_symbols, sample_volatility_data):
        """Test integration with volatility regime system"""
        state_manager = StateManager(
            persistence_dir=temp_data_dir,
            enable_advanced_analytics=True
        )
        
        # Mock volatility regime calculation
        with patch.object(state_manager, '_calculate_volatility_profiles_python') as mock_calc:
            mock_calc.return_value = sample_volatility_data['regimes']
            
            # Update volatility regimes
            state_manager.update_volatility_regimes()
            
            # Verify regime data is available
            regime_data = state_manager.get_volatility_regime_data()
            assert regime_data is not None
    
    def test_performance_metrics_integration(self, temp_data_dir, sample_clustering_result):
        """Test integration of performance metrics across components"""
        state_manager = StateManager(persistence_dir=temp_data_dir)
        
        # Simulate multiple clustering runs with timing
        for i in range(3):
            start_time = time.time()
            
            # Simulate clustering work
            time.sleep(0.1)
            
            end_time = time.time()
            clustering_time = end_time - start_time
            
            # Update metrics
            metrics = {
                'clustering_time': clustering_time,
                'silhouette_score': 0.7 + i * 0.1,
                'data_quality': 0.8
            }
            state_manager.update_performance_metrics(metrics)
        
        # Get aggregated metrics
        performance = state_manager.get_performance_metrics()
        
        assert performance is not None
        assert 'avg_clustering_time' in performance or 'clustering_time' in performance
    
    def test_error_propagation_integration(self, test_symbols, temp_data_dir):
        """Test error handling and propagation across components"""
        engine = ClusteringEngine(
            symbols=test_symbols,
            persistence_dir=temp_data_dir
        )
        
        # Test data fetch error propagation
        with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
            mock_fetch.side_effect = Exception("Network error")
            
            result = engine.run_clustering_analysis()
            assert result is None
        
        # Test clustering error propagation
        with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
            mock_fetch.return_value = np.random.rand(100, 10)
            
            with patch('clustering.clustering_engine.cluster_core') as mock_cluster:
                mock_cluster.cluster_currencies.side_effect = Exception("Clustering error")
                
                result = engine.run_clustering_analysis()
                assert result is None
    
    def test_concurrent_access_integration(self, temp_data_dir, sample_clustering_result):
        """Test concurrent access to shared components"""
        import threading
        import queue
        
        state_manager = StateManager(persistence_dir=temp_data_dir)
        results_queue = queue.Queue()
        
        def update_state_worker(worker_id):
            """Worker function for concurrent state updates"""
            try:
                result = sample_clustering_result.copy()
                result['worker_id'] = worker_id
                result['timestamp'] = datetime.now()
                
                state_manager.update_state(result)
                results_queue.put(f"Worker {worker_id} completed")
            except Exception as e:
                results_queue.put(f"Worker {worker_id} failed: {e}")
        
        # Start multiple workers
        threads = []
        for i in range(3):
            thread = threading.Thread(target=update_state_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all workers to complete
        for thread in threads:
            thread.join(timeout=5.0)
        
        # Check results
        results = []
        while not results_queue.empty():
            results.append(results_queue.get())
        
        assert len(results) == 3
        assert all("completed" in result for result in results)
    
    def test_memory_management_integration(self, test_symbols, temp_data_dir):
        """Test memory management across multiple clustering runs"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        engine = ClusteringEngine(
            symbols=test_symbols,
            persistence_dir=temp_data_dir
        )
        
        # Run multiple clustering analyses
        with patch.object(engine.data_manager, 'fetch_data') as mock_fetch:
            mock_fetch.return_value = np.random.rand(1000, len(test_symbols))
            
            with patch('clustering.clustering_engine.cluster_core') as mock_cluster:
                mock_cluster.cluster_currencies.return_value = {
                    'cluster_assignments': list(range(len(test_symbols))),
                    'silhouette_score': 0.75,
                    'inertia': 0.25,
                    'n_clusters': 3
                }
                
                for i in range(10):
                    result = engine.run_clustering_analysis()
                    assert result is not None
        
        # Check memory usage hasn't grown excessively
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_growth = final_memory - initial_memory
        
        # Allow for some memory growth but not excessive
        assert memory_growth < 100  # Less than 100MB growth
